@font-face {
  font-family: 'devicon';
  src: url('fonts/devicon.eot?#iefix') format('embedded-opentype'),
       url('fonts/devicon.woff') format('woff'),
       url('fonts/devicon.ttf') format('truetype'),
       url('fonts/devicon.svg#devicon') format('svg');
  font-weight: normal;
  font-style: normal;
}

.devicon {
  font-family: 'devicon' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Windows */
.devicon-windows8:before {
  content: "\e0ee";
}

/* Linux */
.devicon-linux:before {
  content: "\e63e";
}

/* Apple */
.devicon-apple:before {
  content: "\e604";
}

/* Ubuntu */
.devicon-ubuntu:before {
  content: "\e6c8";
}

/* Unix */
.devicon-unix:before {
  content: "\e6cb";
}
