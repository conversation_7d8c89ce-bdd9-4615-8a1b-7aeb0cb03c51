import React, { useState, useRef, useEffect } from 'react';
import { Filter } from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';
import { PopupWindow } from './ui/PopupWindow';
import { MenuItem } from './ui/MenuItem';

interface AgentFilterProps {
  onFilterChange: (value: string[]) => void;
  currentFilter: string[];
  darkMode?: boolean; // Keep for backward compatibility
}

export const AgentFilter: React.FC<AgentFilterProps> = ({ onFilterChange, currentFilter = [], darkMode: _ }) => {
  const { theme, darkMode } = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsOpen(!isOpen);
  };

  const handleOptionClick = (value: string) => {
    // Ensure currentFilter is an array
    const currentValues = Array.isArray(currentFilter) ? currentFilter : [];

    let newValues: string[];
    if (currentValues.includes(value)) {
      newValues = currentValues.filter(v => v !== value);
    } else {
      newValues = [...currentValues, value];
    }

    onFilterChange(newValues);
  };

  const options = [
    { value: 'connected', label: 'Connected' },
    { value: 'disconnected', label: 'Disconnected' },
  ];

  const hasFilters = currentFilter.length > 0;

  return (
    <div className="relative" ref={menuRef}>
      <button
        ref={buttonRef}
        onClick={handleClick}
        className={`group flex items-center gap-2 p-0.5 rounded-xl transition-all duration-200 hover:scale-105 ${
          hasFilters
            ? theme.textBlue
            : theme.textMuted + ' ' + theme.hoverText
        }`}
      >
        <div className="filter-component-icon p-1 rounded-xl transition-colors duration-200">
          <Filter className="w-4 h-4" />
        </div>
        {hasFilters && (
          <span className={`text-xs font-medium ${theme.textSecondary}`}>
            {currentFilter.length}
          </span>
        )}
      </button>

      {isOpen && (
        <PopupWindow
          isOpen={isOpen}
          anchorRef={buttonRef}
          title="Agent Status"
          subtitle="Filter by status"
          width="w-56"
          onClose={() => setIsOpen(false)}
        >
          {options.map((option) => (
            <MenuItem
              key={option.value}
              icon={option.icon}
              label={option.label}
              isActive={Array.isArray(currentFilter) ? currentFilter.includes(option.value) : false}
              onClick={() => handleOptionClick(option.value)}
            />
          ))}
        </PopupWindow>
      )}
    </div>
  );
};
