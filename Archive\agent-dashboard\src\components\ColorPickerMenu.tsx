import React, { useState, useEffect } from 'react';
import { HexColorPicker } from 'react-colorful';
import { SunIcon, MoonIcon, ChevronDownIcon } from '@heroicons/react/24/outline';
import { useTheme } from '../contexts/ThemeContext';
import { CustomColors } from '../types/theme';

interface ColorPickerMenuProps {
  menuRef: React.RefObject<HTMLDivElement>;
  position: { x: number; y: number };
  isDragging: boolean;
  onMouseDown: (e: React.MouseEvent) => void;
  onClose: () => void;
}

const elementLabels: Record<keyof CustomColors, string> = {
  primary: 'Primary Color',
  header: 'Header Background',
  text: 'Text Color',
  border: 'Border Color',
  background: 'Background Color'
};

function hexToRGB(hex: string): { r: number; g: number; b: number } {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return { r, g, b };
}

function RGBToHex(r: number, g: number, b: number): string {
  return '#' + [r, g, b].map(x => {
    const hex = x.toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  }).join('').toUpperCase();
}

function getRelativeLuminance(r: number, g: number, b: number): number {
  const [rs, gs, bs] = [r, g, b].map(c => {
    c = c / 255;
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
  });
  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
}

function shouldUseWhiteText(color: string): boolean {
  const { r, g, b } = hexToRGB(color);
  const luminance = getRelativeLuminance(r, g, b);
  // Adjusted threshold for better contrast
  return luminance < 0.6;
}

function RGBToHSV(r: number, g: number, b: number): { h: number; s: number; v: number } {
  r /= 255;
  g /= 255;
  b /= 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  const d = max - min;
  let h = 0;
  const s = max === 0 ? 0 : d / max;
  const v = max;

  if (max !== min) {
    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / d + 2;
        break;
      case b:
        h = (r - g) / d + 4;
        break;
    }
    h /= 6;
  }

  return {
    h: Math.round(h * 360),
    s: Math.round(s * 100),
    v: Math.round(v * 100)
  };
}

function HSVToRGB(h: number, s: number, v: number): { r: number; g: number; b: number } {
  h /= 360;
  s /= 100;
  v /= 100;

  let r = 0, g = 0, b = 0;
  const i = Math.floor(h * 6);
  const f = h * 6 - i;
  const p = v * (1 - s);
  const q = v * (1 - f * s);
  const t = v * (1 - (1 - f) * s);

  switch (i % 6) {
    case 0:
      r = v; g = t; b = p;
      break;
    case 1:
      r = q; g = v; b = p;
      break;
    case 2:
      r = p; g = v; b = t;
      break;
    case 3:
      r = p; g = q; b = v;
      break;
    case 4:
      r = t; g = p; b = v;
      break;
    case 5:
      r = v; g = p; b = q;
      break;
  }

  return {
    r: Math.round(r * 255),
    g: Math.round(g * 255),
    b: Math.round(b * 255)
  };
}

export function ColorPickerMenu({
  menuRef,
  position,
  isDragging,
  onMouseDown,
  onClose
}: ColorPickerMenuProps) {
  const {
    theme,
    darkMode,
    toggleDarkMode,
    customColors,
    selectedElement,
    setSelectedElement,
    handleColorChange,
    resetToDefault,
    saveAsDefault
  } = useTheme();

  const [inputValue, setInputValue] = useState(customColors[selectedElement]);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [rgb, setRGB] = useState(hexToRGB(customColors[selectedElement]));
  const [hsv, setHSV] = useState(RGBToHSV(rgb.r, rgb.g, rgb.b));

  useEffect(() => {
    setInputValue(customColors[selectedElement]);
    const newRGB = hexToRGB(customColors[selectedElement]);
    setRGB(newRGB);
    setHSV(RGBToHSV(newRGB.r, newRGB.g, newRGB.b));
  }, [customColors, selectedElement]);

  const handleHexInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value.toUpperCase();
    if (!value.startsWith('#')) {
      value = '#' + value;
    }
    setInputValue(value);
  };

  const handleHexInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      if (/^#[0-9A-F]{6}$/i.test(inputValue)) {
        handleColorChange(inputValue);
        const newRGB = hexToRGB(inputValue);
        setRGB(newRGB);
        setHSV(RGBToHSV(newRGB.r, newRGB.g, newRGB.b));
      } else {
        setInputValue(customColors[selectedElement]);
      }
    }
  };

  const handleHexInputBlur = () => {
    if (/^#[0-9A-F]{6}$/i.test(inputValue)) {
      handleColorChange(inputValue);
      const newRGB = hexToRGB(inputValue);
      setRGB(newRGB);
      setHSV(RGBToHSV(newRGB.r, newRGB.g, newRGB.b));
    } else {
      setInputValue(customColors[selectedElement]);
    }
  };

  const handleRGBChange = (channel: 'r' | 'g' | 'b', value: number) => {
    const newRGB = { ...rgb, [channel]: value };
    setRGB(newRGB);
    const newHex = RGBToHex(newRGB.r, newRGB.g, newRGB.b);
    setInputValue(newHex);
    handleColorChange(newHex);
    setHSV(RGBToHSV(newRGB.r, newRGB.g, newRGB.b));
  };

  const handleHSVChange = (channel: 'h' | 's' | 'v', value: number) => {
    const newHSV = { ...hsv, [channel]: value };
    setHSV(newHSV);
    const newRGB = HSVToRGB(newHSV.h, newHSV.s, newHSV.v);
    setRGB(newRGB);
    const newHex = RGBToHex(newRGB.r, newRGB.g, newRGB.b);
    setInputValue(newHex);
    handleColorChange(newHex);
  };

  const getTextColorClass = (bgColor: string) => {
    // Enhanced contrast by using more contrasting colors
    return shouldUseWhiteText(bgColor) ? 'text-white' : 'text-gray-900';
  };

  // Helper function to get background color for panels
  const getPanelBackgroundStyle = (baseColor: string) => {
    return {
      backgroundColor: darkMode
        ? 'rgba(55,65,81,0.8)' // Dark mode panel background
        : 'rgba(243,244,246,0.8)' // Light mode panel background
    };
  };

  return (
    <div
      ref={menuRef}
      style={{
        left: position.x,
        top: position.y,
        position: 'fixed',
        transform: 'translate(0, 0)',
        cursor: isDragging ? 'grabbing' : 'auto',
        backgroundColor: darkMode ? 'rgba(31,41,55,0.95)' : 'rgba(255,255,255,0.95)'
      }}
      className={`z-50 rounded-lg shadow-lg border ${theme.borderPrimary} w-[500px] overflow-hidden backdrop-blur-sm`}
    >
      <div
        className={`menu-handle px-3 py-1.5 cursor-grab text-sm font-medium select-none border-b ${theme.borderPrimary} flex justify-between items-center`}
        style={{
          backgroundColor: darkMode ? 'rgba(31,41,55,0.95)' : 'rgba(255,255,255,0.95)'
        }}
        onMouseDown={onMouseDown}
      >
        <span className={theme.textPrimary}>Theme Settings</span>
        <div className="flex items-center gap-2">
          <button
            onClick={toggleDarkMode}
            className={`p-1 rounded-md hover:${theme.bgTertiary} transition-colors ${theme.textPrimary}`}
          >
            {darkMode ? <SunIcon className="h-4 w-4" /> : <MoonIcon className="h-4 w-4" />}
          </button>
        </div>
      </div>
      <div className="p-3 flex flex-col min-h-[400px]">
        <div className="flex gap-3 flex-1">
          <div className="w-[160px] flex flex-col">
            <div className="space-y-0.5">
              {(Object.keys(elementLabels) as Array<keyof CustomColors>).map((element) => (
                <div
                  key={element}
                  onClick={() => setSelectedElement(element)}
                  style={{ backgroundColor: element === selectedElement ? customColors[element] : 'transparent' }}
                  className={`py-1 px-1 rounded-md cursor-pointer transition-all duration-200 group ${
                    selectedElement === element
                      ? `font-bold`
                      : `hover:opacity-80`
                  }`}
                >
                  <div className="flex items-center gap-1.5">
                    <div
                      className={`w-2 h-2 rounded-sm border transition-all duration-200 ${
                        selectedElement === element
                          ? 'ring-1 ring-current'
                          : 'border-current opacity-20 group-hover:opacity-50'
                      }`}
                      style={{ backgroundColor: customColors[element] }}
                    />
                    <span
                      className={`text-[11px] font-medium transition-all duration-200 ${
                        selectedElement === element
                          ? getTextColorClass(customColors[element])
                          : darkMode ? 'text-gray-200' : 'text-gray-800'
                      }`}
                    >
                      {elementLabels[element]}
                    </span>
                  </div>
                  <code
                    className={`text-[9px] font-mono ml-3.5 mt-0.5 transition-all duration-200 ${
                      selectedElement === element
                        ? `opacity-70 ${getTextColorClass(customColors[element])}`
                        : `opacity-70 ${darkMode ? 'text-gray-200' : 'text-gray-800'}`
                    }`}
                  >
                    {customColors[element].toUpperCase()}
                  </code>
                </div>
              ))}
            </div>

            <div className="mt-auto space-y-1">
              <div
                className="group relative flex items-center gap-2 p-1.5 rounded-lg overflow-hidden backdrop-blur-sm border border-gray-300 dark:border-gray-600 shadow-sm hover:shadow-md transition-all duration-200"
                style={getPanelBackgroundStyle(customColors.background)}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent translate-x-[-200%] group-hover:translate-x-[200%] transition-transform duration-[400ms] ease-out" />
                <button
                  onClick={resetToDefault}
                  className="text-xs font-medium w-full relative z-10 font-semibold"
                  style={{ color: darkMode ? 'white' : '#1f2937' }}
                >
                  Reset to Default
                </button>
              </div>

              <div
                className="group relative flex items-center gap-2 p-1.5 rounded-lg overflow-hidden backdrop-blur-sm border border-gray-300 dark:border-gray-600 shadow-sm hover:shadow-md transition-all duration-200"
                style={getPanelBackgroundStyle(customColors.background)}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent translate-x-[-200%] group-hover:translate-x-[200%] transition-transform duration-[400ms] ease-out" />
                <button
                  onClick={saveAsDefault}
                  className="text-xs font-medium w-full relative z-10 font-semibold"
                  style={{ color: darkMode ? 'white' : '#1f2937' }}
                >
                  Save as Default
                </button>
              </div>
            </div>
          </div>

          <div className="flex-1 flex flex-col">
            <HexColorPicker
              color={customColors[selectedElement]}
              onChange={handleColorChange}
              className="!w-full !h-[200px]"
            />

            <div className="flex items-center justify-between gap-2 mt-2">
              <div
                className="group relative flex items-center gap-2 p-1.5 rounded-lg overflow-hidden backdrop-blur-sm border border-gray-300 dark:border-gray-600 shadow-sm hover:shadow-md transition-all duration-200"
                style={{
                  backgroundColor: darkMode
                    ? 'rgba(55,65,81,0.8)' // Dark mode panel background
                    : 'rgba(243,244,246,0.8)' // Light mode panel background
                }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent translate-x-[-200%] group-hover:translate-x-[200%] transition-transform duration-[400ms] ease-out" />
                <div className="flex items-center justify-center w-8">
                  <div
                    className="w-4 h-4 rounded border border-white/20 shadow-sm"
                    style={{ backgroundColor: customColors[selectedElement] }}
                  />
                </div>
                <input
                  type="text"
                  value={inputValue}
                  onChange={handleHexInputChange}
                  onKeyDown={handleHexInputKeyDown}
                  onBlur={handleHexInputBlur}
                  className="text-xs font-mono px-2 py-1 rounded w-20 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-blue-500 relative z-10"
                  placeholder="#000000"
                />
              </div>
              <div
                className="group relative flex items-center gap-2 p-1.5 rounded-lg overflow-hidden backdrop-blur-sm border border-gray-300 dark:border-gray-600 shadow-sm hover:shadow-md transition-all duration-200"
                style={getPanelBackgroundStyle(customColors.background)}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent translate-x-[-200%] group-hover:translate-x-[200%] transition-transform duration-[400ms] ease-out" />
                <button
                  onClick={() => setShowAdvanced(!showAdvanced)}
                  className="flex items-center justify-center gap-1 text-xs font-medium px-2 py-1 rounded w-[112px] border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-blue-500 relative z-10"
                >
                  Advanced
                  <ChevronDownIcon
                    className={`w-3 h-3 transition-transform ${showAdvanced ? 'rotate-180' : ''}`}
                  />
                </button>
              </div>
            </div>

            {showAdvanced && (
              <div
                className="mt-3 space-y-4 p-3 rounded-lg overflow-hidden backdrop-blur-sm border border-gray-300 dark:border-gray-600 shadow-sm flex-1"
                style={getPanelBackgroundStyle(customColors[selectedElement])}
              >
                <div className="space-y-2">
                  <div className="text-xs font-medium mb-2 text-gray-800 dark:text-gray-200">RGB</div>
                  {[
                    { label: 'R', value: rgb.r, color: 'red' },
                    { label: 'G', value: rgb.g, color: 'green' },
                    { label: 'B', value: rgb.b, color: 'blue' }
                  ].map(({ label, value, color }) => (
                    <div key={label} className="flex items-center gap-2">
                      <span className="text-xs font-mono w-4 text-gray-800 dark:text-gray-200 font-semibold">{label}</span>
                      <input
                        type="range"
                        min="0"
                        max="255"
                        value={value}
                        onChange={(e) => handleRGBChange(label.toLowerCase() as 'r' | 'g' | 'b', parseInt(e.target.value))}
                        className="flex-1 h-1.5 rounded-lg appearance-none cursor-pointer"
                        style={{
                          background: `linear-gradient(to right, black, ${color})`
                        }}
                      />
                      <div className="w-[72px] flex items-center justify-end gap-0.5">
                        <div className="group relative overflow-hidden rounded">
                          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent translate-x-[-200%] group-hover:translate-x-[200%] transition-transform duration-[400ms] ease-out" />
                          <input
                            type="number"
                            min="0"
                            max="255"
                            value={value}
                            onChange={(e) => handleRGBChange(label.toLowerCase() as 'r' | 'g' | 'b', parseInt(e.target.value) || 0)}
                            className="w-12 text-xs px-1 py-0.5 rounded text-center relative z-10 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="space-y-2">
                  <div className="text-xs font-medium mb-2 text-gray-800 dark:text-gray-200">HSV</div>
                  {[
                    { label: 'H', value: hsv.h, max: 360, unit: '°' },
                    { label: 'S', value: hsv.s, max: 100, unit: '%' },
                    { label: 'V', value: hsv.v, max: 100, unit: '%' }
                  ].map(({ label, value, max, unit }) => (
                    <div key={label} className="flex items-center gap-2">
                      <span className="text-xs font-mono w-4 text-gray-800 dark:text-gray-200 font-semibold">{label}</span>
                      <input
                        type="range"
                        min="0"
                        max={max}
                        value={value}
                        onChange={(e) => handleHSVChange(label.toLowerCase() as 'h' | 's' | 'v', parseInt(e.target.value))}
                        className="flex-1 h-1.5 rounded-lg appearance-none cursor-pointer"
                        style={{
                          background: label === 'H'
                            ? 'linear-gradient(to right, #ff0000, #ffff00, #00ff00, #00ffff, #0000ff, #ff00ff, #ff0000)'
                            : label === 'S'
                            ? `linear-gradient(to right, white, ${RGBToHex(rgb.r, rgb.g, rgb.b)})`
                            : `linear-gradient(to right, black, ${RGBToHex(rgb.r, rgb.g, rgb.b)})`
                        }}
                      />
                      <div className="w-[72px] flex items-center justify-end gap-0.5">
                        <div className="group relative overflow-hidden rounded">
                          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent translate-x-[-200%] group-hover:translate-x-[200%] transition-transform duration-[400ms] ease-out" />
                          <input
                            type="number"
                            min="0"
                            max={max}
                            value={value}
                            onChange={(e) => handleHSVChange(label.toLowerCase() as 'h' | 's' | 'v', parseInt(e.target.value) || 0)}
                            className="w-12 text-xs px-1 py-0.5 rounded text-center relative z-10 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          />
                        </div>
                        <span className="text-xs w-4 opacity-70 text-gray-600 dark:text-gray-300">{unit}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
