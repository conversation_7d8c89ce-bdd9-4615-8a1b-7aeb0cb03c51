import React, { useState, useRef, useEffect } from 'react';
import { Settings2, Check } from 'lucide-react';
import { Column } from '../types';
import { useTheme } from '../contexts/ThemeContext';
import { PopupWindow } from './ui/PopupWindow';

interface ColumnManagerProps {
  columns: Column[];
  visibleColumns: string[] | Set<string>;
  onColumnToggle: (key: string) => void;
  darkMode?: boolean; // Keep for backward compatibility
}

export const ColumnManager: React.FC<ColumnManagerProps> = ({
  columns,
  visibleColumns,
  onColumnToggle,
  darkMode: propDarkMode
}) => {
  const { theme, darkMode } = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleClick = () => {
    setIsOpen(!isOpen);
  };

  const handleColumnToggle = (key: string) => {
    onColumnToggle(key);
  };

  return (
    <div className="relative" ref={menuRef}>
      <button
        ref={buttonRef}
        onClick={handleClick}
        className={`group flex items-center gap-2 p-0.5 rounded-xl transition-all duration-200 hover:scale-105 ${theme.textMuted} ${theme.hoverText}`}
      >
        <div className={`p-1 rounded-xl transition-colors duration-200 ${theme.headerIconBg}`}>
          <Settings2 className="w-4 h-4" />
        </div>
      </button>

      {isOpen && (
        <PopupWindow
          isOpen={isOpen}
          anchorRef={buttonRef}
          title="Manage Columns"
          subtitle="Show or hide columns"
          width="w-56"
          onClose={() => setIsOpen(false)}
        >
          {columns.map((column) => (
            <button
              key={column.key}
              className={`w-full flex items-center gap-3 px-4 py-2 text-sm transition-colors duration-200 ${theme.textSecondary} ${theme.hoverText}`}
              onClick={() => handleColumnToggle(column.key)}
            >
              <div className="flex-1 text-left">
                {column.label}
              </div>
              <div className="w-5 h-5 flex-shrink-0 flex items-center justify-center">
                {(Array.isArray(visibleColumns) ? visibleColumns.includes(column.key) : visibleColumns.has(column.key)) && (
                  <Check className="w-4 h-4 flex-shrink-0" />
                )}
              </div>
            </button>
          ))}
        </PopupWindow>
      )}
    </div>
  );
};
