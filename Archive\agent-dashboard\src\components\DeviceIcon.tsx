import React, { memo } from 'react';
import { Monitor, Server, Laptop, Tablet, Smartphone, Cpu } from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';

interface DeviceIconProps {
  type: string;
  className?: string;
  darkMode?: boolean; // Keep for backward compatibility
}

// Using memo to prevent unnecessary re-renders
export const DeviceIcon: React.FC<DeviceIconProps> = memo(({
  type,
  className = 'w-5 h-5',
  darkMode: propDarkMode
}) => {
  const { theme } = useTheme();

  const getIcon = () => {
    switch (type.toLowerCase()) {
      case 'desktop':
        return <Monitor className={className} />;
      case 'server':
        return <Server className={className} />;
      case 'laptop':
        return <Laptop className={className} />;
      case 'tablet':
        return <Tablet className={className} />;
      case 'mobile':
        return <Smartphone className={className} />;
      case 'iot':
        return <Cpu className={className} />;
      default:
        return <Monitor className={className} />;
    }
  };

  return getIcon();
}, (prevProps, nextProps) => {
  // Custom comparison function to determine if component should re-render
  return (
    prevProps.type === nextProps.type &&
    prevProps.className === nextProps.className
  );
});