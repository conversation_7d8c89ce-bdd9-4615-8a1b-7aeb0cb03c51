import React, { useEffect } from 'react';

export const DeviconLoader: React.FC = () => {
  useEffect(() => {
    // Load the Devicon CSS
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = '/assets/devicon-custom.css';
    document.head.appendChild(link);

    // Cleanup function
    return () => {
      document.head.removeChild(link);
    };
  }, []);

  return null; // This component doesn't render anything
};
