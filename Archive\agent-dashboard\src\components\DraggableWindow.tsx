import React, { useState, useEffect, useRef } from 'react';
import ReactDOM from 'react-dom';
import { useTheme } from '../contexts/ThemeContext';

interface DraggableWindowProps {
  children: React.ReactNode;
  className?: string;
  anchorEl: HTMLElement | null;
  darkMode?: boolean;
  title?: string;
  subtitle?: string;
}

export const DraggableWindow: React.FC<DraggableWindowProps> = ({
  children,
  className = '',
  anchorEl,
  darkMode: propDarkMode = false,
  title,
  subtitle,
}) => {
  const { theme, darkMode } = useTheme();
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [isVisible, setIsVisible] = useState(false);
  const windowRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (anchorEl && windowRef.current) {
      // Position immediately based on anchor element
      const anchorRect = anchorEl.getBoundingClientRect();

      // Position below and to the left of the anchor element
      let x = anchorRect.left;
      let y = anchorRect.bottom + 5; // Position below with a small gap

      setPosition({ x, y });
    }
  }, [anchorEl]);

  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (windowRef.current) {
      setDragOffset({
        x: e.clientX - position.x,
        y: e.clientY - position.y,
      });
      setIsDragging(true);
    }
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      e.preventDefault();

      // Simply update position based on mouse movement
      const x = e.clientX - dragOffset.x;
      const y = e.clientY - dragOffset.y;

      setPosition({ x, y });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging]);

  const handleContentClick = (e: React.MouseEvent) => {
    // Prevent clicks inside the window from closing it
    e.stopPropagation();
  };

  // Set isVisible to true after a small delay to ensure proper positioning
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 50);
    return () => clearTimeout(timer);
  }, []);

  if (!isVisible) {
    return null;
  }

  // Use a portal to render the window directly in the document body
  return ReactDOM.createPortal(
    <div
      ref={windowRef}
      className={`${className} fixed transition-all duration-200 opacity-100 backdrop-blur-sm shadow-lg`}
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        cursor: isDragging ? 'grabbing' : 'default',
        zIndex: 99999,
      }}
      onClick={handleContentClick}
    >
      <div
        className={`px-4 py-3 cursor-grab ${theme.popupHeaderBg} ${theme.textPrimary} rounded-t-md flex flex-col items-center justify-center`}
        onMouseDown={handleMouseDown}
      >
        <div className="text-sm font-medium text-center">
          {title || 'Options'}
        </div>
        {subtitle ? (
          <div className="text-xs opacity-80 mt-0.5 text-center">
            {subtitle}
          </div>
        ) : title && (
          <div className="text-xs opacity-80 mt-0.5 text-center">
            Filter by {title.replace('Operating System', 'OS').replace('Device Type', 'Type').replace('Agent Type', 'Agent')}
          </div>
        )}
      </div>
      <hr className={`border-t ${theme.borderPrimary} mx-2 my-1`} />
      <div className="p-3">{children}</div>
    </div>,
    document.body
  );
};

