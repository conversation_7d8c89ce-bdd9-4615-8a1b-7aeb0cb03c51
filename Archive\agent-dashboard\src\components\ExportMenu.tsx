import React, { useState, useRef, useEffect } from 'react';
import { Download, FileText, FileSpreadsheet, FileJson } from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';
import { PopupWindow } from './ui/PopupWindow';
import { MenuItem } from './ui/MenuItem';

export const ExportMenu: React.FC = () => {
  const { theme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleClick = () => {
    setIsOpen(!isOpen);
  };

  const exportOptions = [
    { icon: <FileText className="w-4 h-4" />, label: 'Export as CSV', description: 'Comma-separated values' },
    { icon: <FileSpreadsheet className="w-4 h-4" />, label: 'Export as Excel', description: 'Microsoft Excel file' },
    { icon: <FileJson className="w-4 h-4" />, label: 'Export as JSON', description: 'JavaScript Object Notation' },
  ];

  return (
    <div className="relative" ref={menuRef}>
      <button
        ref={buttonRef}
        onClick={handleClick}
        className={`group flex items-center gap-2 p-0.5 rounded-xl transition-all duration-200 hover:scale-105 ${theme.textMuted} ${theme.hoverText}`}
      >
        <div className={`p-1 rounded-xl transition-colors duration-200 ${theme.headerIconBg}`}>
          <Download className="w-4 h-4" />
        </div>
      </button>

      {isOpen && (
        <PopupWindow
          isOpen={isOpen}
          anchorRef={buttonRef}
          title="Export Options"
          subtitle="Download device data"
          width="w-56"
          onClose={() => setIsOpen(false)}
        >
          {exportOptions.map((option, index) => (
            <MenuItem
              key={index}
              icon={option.icon}
              label={option.label}
              description={option.description}
            />
          ))}
        </PopupWindow>
      )}
    </div>
  );
};
