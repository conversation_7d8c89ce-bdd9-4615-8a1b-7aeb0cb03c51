import React, { useState, useCallback } from 'react';
import { Responsive, WidthProvider, Layout } from 'react-grid-layout';
import { Plus, Settings, Save, RotateCcw } from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';
import { GridDashboardProps, DashboardWidget, WidgetType } from '../types/dashboard';
import { DeviceCountWidget } from './widgets/DeviceCountWidget';
import { AgentStatusWidget } from './widgets/AgentStatusWidget';
import { OSDistributionWidget } from './widgets/OSDistributionWidget';
import { DeviceTableWidget } from './widgets/DeviceTableWidget';
import { SystemHealthWidget } from './widgets/SystemHealthWidget';
import { AlertsWidget } from './widgets/AlertsWidget';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';

const ResponsiveGridLayout = WidthProvider(Responsive);

// Widget factory function
const renderWidget = (widget: DashboardWidget, props: any) => {
  switch (widget.type) {
    case 'device-count':
      return <DeviceCountWidget {...props} />;
    case 'agent-status':
      return <AgentStatusWidget {...props} />;
    case 'os-distribution':
      return <OSDistributionWidget {...props} />;
    case 'device-table':
      return <DeviceTableWidget {...props} />;
    case 'system-health':
      return <SystemHealthWidget {...props} />;
    case 'alerts':
      return <AlertsWidget {...props} />;
    default:
      return (
        <div className="h-full w-full bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
          <span className="text-gray-500">Widget: {widget.type}</span>
        </div>
      );
  }
};

export const GridDashboard: React.FC<GridDashboardProps> = ({
  layout,
  onLayoutChange,
  onWidgetAdd,
  onWidgetRemove,
  onWidgetUpdate,
  editable = false
}) => {
  const { theme } = useTheme();
  const [showAddWidget, setShowAddWidget] = useState(false);

  const handleLayoutChange = useCallback((currentLayout: Layout[], allLayouts: { [key: string]: Layout[] }) => {
    onLayoutChange(allLayouts);
  }, [onLayoutChange]);

  const handleAddWidget = (type: WidgetType) => {
    const newWidget: DashboardWidget = {
      id: `widget-${Date.now()}`,
      title: type.charAt(0).toUpperCase() + type.slice(1).replace('-', ' '),
      type,
      config: {
        showHeader: true,
        refreshInterval: 30000
      }
    };
    onWidgetAdd(newWidget);
    setShowAddWidget(false);
  };

  const availableWidgets: { type: WidgetType; name: string; description: string }[] = [
    { type: 'device-count', name: 'Device Count', description: 'Overview of device types' },
    { type: 'agent-status', name: 'Agent Status', description: 'Agent connectivity status' },
    { type: 'os-distribution', name: 'OS Distribution', description: 'Operating system breakdown' },
    { type: 'device-table', name: 'Device Table', description: 'Detailed device listing' },
    { type: 'status-chart', name: 'Status Chart', description: 'Visual status overview' },
    { type: 'recent-activity', name: 'Recent Activity', description: 'Latest system events' },
    { type: 'system-health', name: 'System Health', description: 'Overall system metrics' },
    { type: 'alerts', name: 'Alerts', description: 'Active system alerts' }
  ];

  return (
    <div className="h-full w-full">
      {/* Dashboard Header */}
      {editable && (
        <div className={`flex items-center justify-between p-4 ${theme.bgSecondary} ${theme.borderPrimary} border-b`}>
          <div>
            <h2 className={`text-lg font-semibold ${theme.textPrimary}`}>
              {layout.name}
            </h2>
            <p className={`text-sm ${theme.textSecondary}`}>
              Drag and resize widgets to customize your dashboard
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={() => setShowAddWidget(!showAddWidget)}
              className={`flex items-center gap-2 px-3 py-1.5 rounded ${theme.buttonPrimary} ${theme.buttonText} hover:opacity-90 transition-opacity`}
            >
              <Plus size={16} />
              Add Widget
            </button>
            
            <button className={`p-1.5 rounded ${theme.hoverBg} ${theme.textMuted} hover:${theme.textPrimary} transition-colors`}>
              <Settings size={16} />
            </button>
            
            <button className={`p-1.5 rounded ${theme.hoverBg} ${theme.textMuted} hover:${theme.textPrimary} transition-colors`}>
              <Save size={16} />
            </button>
            
            <button className={`p-1.5 rounded ${theme.hoverBg} ${theme.textMuted} hover:${theme.textPrimary} transition-colors`}>
              <RotateCcw size={16} />
            </button>
          </div>
        </div>
      )}

      {/* Add Widget Panel */}
      {showAddWidget && editable && (
        <div className={`p-4 ${theme.bgTertiary} ${theme.borderPrimary} border-b`}>
          <h3 className={`text-sm font-medium ${theme.textPrimary} mb-3`}>
            Available Widgets
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            {availableWidgets.map((widget) => (
              <button
                key={widget.type}
                onClick={() => handleAddWidget(widget.type)}
                className={`p-3 text-left rounded-lg ${theme.bgSecondary} ${theme.borderPrimary} border hover:${theme.hoverBg} transition-colors`}
              >
                <div className={`font-medium text-sm ${theme.textPrimary}`}>
                  {widget.name}
                </div>
                <div className={`text-xs ${theme.textMuted} mt-1`}>
                  {widget.description}
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Grid Layout */}
      <div className="flex-1 p-4">
        <ResponsiveGridLayout
          className="layout"
          layouts={layout.layouts}
          onLayoutChange={handleLayoutChange}
          breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
          cols={{ lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 }}
          rowHeight={60}
          isDraggable={editable}
          isResizable={editable}
          margin={[16, 16]}
          containerPadding={[0, 0]}
          useCSSTransforms={true}
          compactType="vertical"
        >
          {layout.widgets.map((widget) => (
            <div key={widget.id} className="widget-container">
              {renderWidget(widget, {
                widget,
                onUpdate: (updates: Partial<DashboardWidget>) => onWidgetUpdate(widget.id, updates),
                onRemove: () => onWidgetRemove(widget.id),
                editable
              })}
            </div>
          ))}
        </ResponsiveGridLayout>
      </div>
    </div>
  );
};
