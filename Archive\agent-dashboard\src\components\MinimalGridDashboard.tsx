import React, { useState } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { mockData } from '../data/mockData';

interface MinimalGridDashboardProps {
  editable?: boolean;
}

export const MinimalGridDashboard: React.FC<MinimalGridDashboardProps> = ({ editable = false }) => {
  const { theme } = useTheme();

  // Calculate real data
  const totalDevices = mockData.length;
  const connectedDevices = mockData.filter(d => d.agentStatus.toLowerCase() === 'connected').length;
  const healthPercentage = totalDevices > 0 ? (connectedDevices / totalDevices * 100).toFixed(1) : '0';
  
  const deviceCounts = mockData.reduce((acc, device) => {
    const type = device.type.toLowerCase();
    acc[type] = (acc[type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const [widgets, setWidgets] = useState([
    { id: 'device-count', title: 'Device Overview', visible: true },
    { id: 'agent-status', title: 'Agent Health', visible: true },
    { id: 'system-status', title: 'System Status', visible: true },
    { id: 'recent-devices', title: 'Recent Devices', visible: true }
  ]);

  const toggleWidget = (id: string) => {
    setWidgets(prev => prev.map(w => 
      w.id === id ? { ...w, visible: !w.visible } : w
    ));
  };

  const renderWidget = (id: string, title: string) => {
    const widget = widgets.find(w => w.id === id);
    if (!widget?.visible) return null;

    let content;
    switch (id) {
      case 'device-count':
        content = (
          <div className="h-full flex flex-col justify-center">
            <div className={`text-3xl font-bold ${theme.textPrimary} text-center`}>
              {totalDevices}
            </div>
            <div className={`text-sm ${theme.textSecondary} text-center mt-1`}>
              Total Devices
            </div>
            <div className="mt-3 space-y-1">
              <div className="flex justify-between text-xs">
                <span className={theme.textMuted}>Desktop: {deviceCounts.desktop || 0}</span>
                <span className={theme.textMuted}>Laptop: {deviceCounts.laptop || 0}</span>
              </div>
              <div className="flex justify-between text-xs">
                <span className={theme.textMuted}>Server: {deviceCounts.server || 0}</span>
                <span className={theme.textMuted}>Mobile: {deviceCounts.mobile || 0}</span>
              </div>
            </div>
          </div>
        );
        break;

      case 'agent-status':
        content = (
          <div className="h-full flex flex-col justify-center">
            <div className={`text-3xl font-bold text-center ${
              parseFloat(healthPercentage) >= 80 ? 'text-green-500' :
              parseFloat(healthPercentage) >= 60 ? 'text-yellow-500' : 'text-red-500'
            }`}>
              {healthPercentage}%
            </div>
            <div className={`text-sm ${theme.textSecondary} text-center mt-1`}>
              Healthy Agents
            </div>
            <div className="mt-3 text-center">
              <div className={`text-xs ${theme.textMuted}`}>
                {connectedDevices}/{totalDevices} connected
              </div>
            </div>
          </div>
        );
        break;

      case 'system-status':
        content = (
          <div className="h-full flex flex-col justify-center">
            <div className="text-center">
              <div className={`text-2xl font-bold text-green-500`}>
                Online
              </div>
              <div className={`text-sm ${theme.textSecondary} mt-1`}>
                All Systems Operational
              </div>
            </div>
            <div className="mt-4 space-y-2">
              <div className="flex justify-between items-center">
                <span className={`text-xs ${theme.textMuted}`}>API Status</span>
                <span className="text-xs text-green-500">●</span>
              </div>
              <div className="flex justify-between items-center">
                <span className={`text-xs ${theme.textMuted}`}>Database</span>
                <span className="text-xs text-green-500">●</span>
              </div>
              <div className="flex justify-between items-center">
                <span className={`text-xs ${theme.textMuted}`}>Monitoring</span>
                <span className="text-xs text-green-500">●</span>
              </div>
            </div>
          </div>
        );
        break;

      case 'recent-devices':
        content = (
          <div className="h-full overflow-y-auto">
            <div className="space-y-2">
              {mockData.slice(0, 5).map((device) => (
                <div key={device.id} className={`p-2 rounded ${theme.bgTertiary} ${theme.borderPrimary} border`}>
                  <div className={`text-sm font-medium ${theme.textPrimary} truncate`}>
                    {device.deviceName}
                  </div>
                  <div className="flex justify-between items-center mt-1">
                    <span className={`text-xs ${theme.textMuted}`}>
                      {device.type} • {device.os}
                    </span>
                    <span className={`text-xs px-2 py-0.5 rounded-full ${
                      device.agentStatus.toLowerCase() === 'connected' ? 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400' :
                      device.agentStatus.toLowerCase() === 'disconnected' ? 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400' :
                      'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400'
                    }`}>
                      {device.agentStatus}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );
        break;

      default:
        content = <div>Widget content</div>;
    }

    return (
      <div className={`${theme.bgSecondary} ${theme.borderPrimary} border rounded-lg shadow-sm overflow-hidden transition-all duration-200 hover:shadow-md`}>
        {/* Widget Header */}
        <div className={`flex items-center justify-between p-3 ${theme.bgTertiary} ${theme.borderPrimary} border-b`}>
          <h3 className={`text-sm font-medium ${theme.textPrimary} truncate`}>
            {title}
          </h3>
          
          {editable && (
            <button
              onClick={() => toggleWidget(id)}
              className={`text-xs px-2 py-1 rounded ${theme.buttonSecondary} ${theme.textSecondary} hover:opacity-90 transition-opacity`}
            >
              Hide
            </button>
          )}
        </div>

        {/* Widget Content */}
        <div className="p-3 h-48 overflow-hidden">
          {content}
        </div>
      </div>
    );
  };

  return (
    <div className="h-full p-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {renderWidget('device-count', 'Device Overview')}
        {renderWidget('agent-status', 'Agent Health')}
        {renderWidget('system-status', 'System Status')}
      </div>
      
      <div className="grid grid-cols-1 gap-4">
        {renderWidget('recent-devices', 'Recent Devices')}
      </div>

      {editable && (
        <div className={`mt-6 p-4 ${theme.bgTertiary} rounded-lg ${theme.borderPrimary} border`}>
          <h3 className={`text-sm font-medium ${theme.textPrimary} mb-3`}>
            Widget Controls
          </h3>
          <div className="flex flex-wrap gap-2">
            {widgets.map(widget => (
              <button
                key={widget.id}
                onClick={() => toggleWidget(widget.id)}
                className={`text-xs px-3 py-1.5 rounded-lg transition-colors ${
                  widget.visible 
                    ? `${theme.buttonPrimary} ${theme.buttonText}` 
                    : `${theme.bgSecondary} ${theme.textMuted} ${theme.borderPrimary} border`
                }`}
              >
                {widget.title} {widget.visible ? '✓' : '○'}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
