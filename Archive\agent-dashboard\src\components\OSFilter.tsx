import React, { useState, useRef, useEffect } from 'react';
import { Filter } from 'lucide-react';
import { OSIcon } from './OSIcon';
import { useTheme } from '../contexts/ThemeContext';
import { PopupWindow } from './ui/PopupWindow';
import { MenuItem } from './ui/MenuItem';

interface OSFilterProps {
  onFilterChange: (value: string[]) => void;
  currentFilter: string[];
  darkMode?: boolean; // Keep for backward compatibility
}

export const OSFilter: React.FC<OSFilterProps> = ({ onFilterChange, currentFilter = [], darkMode: propDarkMode }) => {
  const { theme, darkMode } = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsOpen(!isOpen);
  };

  const handleOptionClick = (value: string) => {
    let newValues: string[];
    if (currentFilter.includes(value)) {
      newValues = currentFilter.filter(v => v !== value);
    } else {
      newValues = [...currentFilter, value];
    }

    onFilterChange(newValues);
  };

  const options = [
    { value: 'freebsd', label: 'FreeBSD', version: 'FreeBSD 14.0-RELEASE', icon: <OSIcon os="freebsd" darkMode={darkMode} /> },
    { value: 'linux', label: 'Linux', version: 'Linux Kernel 6.1', icon: <OSIcon os="linux" darkMode={darkMode} /> },
    { value: 'macos', label: 'macOS', version: 'macOS Sonoma 14.0', icon: <OSIcon os="macos" darkMode={darkMode} /> },
    { value: 'ubuntu', label: 'Ubuntu', version: 'Ubuntu 22.04 LTS', icon: <OSIcon os="ubuntu" darkMode={darkMode} /> },
    { value: 'unix', label: 'Unix', version: 'Unix System V', icon: <OSIcon os="unix" darkMode={darkMode} /> },
    { value: 'windows', label: 'Windows', version: 'Windows 11 Pro', icon: <OSIcon os="windows" darkMode={darkMode} /> },
    { value: 'windows_server', label: 'Windows Server', version: 'Windows Server 2022', icon: <OSIcon os="windows_server" darkMode={darkMode} /> },
  ].sort((a, b) => a.label.localeCompare(b.label));

  const hasFilters = currentFilter.length > 0;

  return (
    <div className="relative" ref={menuRef}>
      <button
        ref={buttonRef}
        onClick={handleClick}
        className={`group flex items-center gap-2 p-0.5 rounded-xl transition-all duration-200 hover:scale-105 ${
          hasFilters
            ? theme.textBlue
            : theme.textMuted + ' ' + theme.hoverText
        }`}
      >
        <div className="filter-component-icon p-1 rounded-xl transition-colors duration-200">
          <Filter className="w-4 h-4" />
        </div>
        {hasFilters && (
          <span className={`text-xs font-medium ${theme.textSecondary}`}>
            {currentFilter.length}
          </span>
        )}
      </button>

      {isOpen && (
        <PopupWindow
          isOpen={isOpen}
          anchorRef={buttonRef}
          title="Operating System"
          subtitle="Filter by OS"
          width="w-64"
          onClose={() => setIsOpen(false)}
        >
          {options.map((option) => (
            <MenuItem
              key={option.value}
              icon={option.icon}
              label={option.label}
              description={option.version}
              isActive={Array.isArray(currentFilter) ? currentFilter.includes(option.value) : false}
              onClick={() => handleOptionClick(option.value)}
            />
          ))}
        </PopupWindow>
      )}
    </div>
  );
};
