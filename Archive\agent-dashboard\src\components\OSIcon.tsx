import React, { useState, memo } from 'react';
import { useTheme } from '../contexts/ThemeContext';

interface OSIconProps {
  os: string;
  className?: string;
  darkMode?: boolean; // Keep for backward compatibility
}

// Using memo to prevent unnecessary re-renders
export const OSIcon: React.FC<OSIconProps> = memo(({
  os,
  className = '',
  darkMode: propDarkMode
}) => {
  const { theme, darkMode } = useTheme();
  const [showTooltip, setShowTooltip] = useState(false);
  const [imageError, setImageError] = useState(false);

  const getIconUrl = () => {
    // Use local SVG files from the devicon library with specific distro support
    const osLower = os.toLowerCase();

    // Windows versions
    if (osLower === 'windows' || osLower === 'windows_11' || osLower === 'windows11') {
      return '/assets/icons/os/windows/windows11.svg';
    } else if (osLower === 'windows_10' || osLower === 'windows10' || osLower === 'windows_8' || osLower === 'windows8') {
      return '/assets/icons/os/windows/windows8.svg';
    } else if (osLower === 'windows_server' || osLower.includes('server')) {
      return '/assets/icons/os/windows/windows8.svg'; // Use Windows 8 icon for Server
    }

    // Linux distributions
    else if (osLower === 'ubuntu') {
      return '/assets/icons/os/linux/ubuntu.svg';
    } else if (osLower === 'debian' || osLower.includes('debian')) {
      return '/assets/icons/os/linux/debian.svg';
    } else if (osLower === 'fedora' || osLower.includes('fedora')) {
      return '/assets/icons/os/linux/fedora.svg';
    } else if (osLower === 'centos' || osLower.includes('centos')) {
      return '/assets/icons/os/linux/centos.svg';
    } else if (osLower === 'redhat' || osLower.includes('rhel') || osLower.includes('red hat')) {
      return '/assets/icons/os/linux/redhat.svg';
    } else if (osLower === 'opensuse' || osLower.includes('suse')) {
      return '/assets/icons/os/linux/opensuse.svg';
    } else if (osLower === 'arch' || osLower.includes('arch')) {
      return '/assets/icons/os/linux/archlinux.svg';
    } else if (osLower === 'rocky' || osLower.includes('rocky')) {
      return '/assets/icons/os/linux/rockylinux.svg';
    } else if (osLower === 'linux' || osLower.includes('linux')) {
      return '/assets/icons/os/linux.svg';
    }

    // Other OS types
    else if (osLower === 'macos' || osLower.includes('mac') || osLower.includes('apple')) {
      return '/assets/icons/os/macos/macos.svg';
    } else if (osLower === 'freebsd' || osLower.includes('bsd')) {
      return '/assets/icons/os/free-bsd.png';
    } else if (osLower === 'unix' || osLower.includes('unix')) {
      return '/assets/icons/os/unix/unix.svg';
    }

    // Default fallback
    return '/assets/icons/os/linux.svg';
  };

  const getOSInfo = () => {
    const osLower = os.toLowerCase();

    // Windows versions
    if (osLower === 'windows' || osLower === 'windows_11' || osLower === 'windows11') {
      return 'Microsoft Windows 11 Pro';
    } else if (osLower === 'windows_10' || osLower === 'windows10') {
      return 'Microsoft Windows 10 Pro';
    } else if (osLower === 'windows_8' || osLower === 'windows8') {
      return 'Microsoft Windows 8.1 Pro';
    } else if (osLower === 'windows_server' || osLower.includes('server')) {
      return 'Windows Server 2022 Datacenter';
    }

    // Linux distributions
    else if (osLower === 'ubuntu') {
      return 'Ubuntu 22.04 LTS';
    } else if (osLower === 'debian' || osLower.includes('debian')) {
      return 'Debian 12 (Bookworm)';
    } else if (osLower === 'fedora' || osLower.includes('fedora')) {
      return 'Fedora 39 Workstation';
    } else if (osLower === 'centos' || osLower.includes('centos')) {
      return 'CentOS Stream 9';
    } else if (osLower === 'redhat' || osLower.includes('rhel') || osLower.includes('red hat')) {
      return 'Red Hat Enterprise Linux 9';
    } else if (osLower === 'opensuse' || osLower.includes('suse')) {
      return 'openSUSE Leap 15.5';
    } else if (osLower === 'arch' || osLower.includes('arch')) {
      return 'Arch Linux (Rolling Release)';
    } else if (osLower === 'rocky' || osLower.includes('rocky')) {
      return 'Rocky Linux 9';
    } else if (osLower === 'linux' || osLower.includes('linux')) {
      return 'Linux Kernel 6.1';
    }

    // Other OS types
    else if (osLower === 'macos' || osLower.includes('mac') || osLower.includes('apple')) {
      return 'macOS Sonoma 14.0';
    } else if (osLower === 'freebsd' || osLower.includes('bsd')) {
      return 'FreeBSD 14.0-RELEASE';
    } else if (osLower === 'unix' || osLower.includes('unix')) {
      return 'Unix System V';
    }

    // Default fallback
    return os;
  };

  const shouldInvert = darkMode && os.toLowerCase() === 'macos';

  // Fallback for when the CDN icons don't load
  const getFallbackIcon = () => {
    // Use a simple text fallback instead of images
    return '';
  };

  return (
    <div className="relative inline-block">
      {!imageError ? (
        <img
          src={getIconUrl()}
          alt={`${os} icon`}
          className={`w-5 h-5 ${shouldInvert ? theme.osIconInvert : ''} ${className}`}
          onMouseEnter={() => setShowTooltip(true)}
          onMouseLeave={() => setShowTooltip(false)}
          onError={() => setImageError(true)}
        />
      ) : (
        <span
          className={`w-5 h-5 inline-flex items-center justify-center text-xs ${theme.textMuted} ${className}`}
          onMouseEnter={() => setShowTooltip(true)}
          onMouseLeave={() => setShowTooltip(false)}
        >
          {os.substring(0, 1).toUpperCase()}
        </span>
      )}
      {showTooltip && (
        <div
          className={`absolute z-10 px-2 py-1 text-xs rounded shadow-lg -top-8 left-1/2 transform -translate-x-1/2 whitespace-nowrap
            ${theme.tooltipBg} ${theme.tooltipText} border ${theme.tooltipBorder}`}
        >
          {getOSInfo()}
        </div>
      )}
    </div>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function to determine if component should re-render
  return (
    prevProps.os === nextProps.os &&
    prevProps.className === nextProps.className &&
    prevProps.darkMode === nextProps.darkMode
  );
});

