import React, { useState } from 'react';
import { Responsive, WidthProvider, Layout } from 'react-grid-layout';
import { Plus, X } from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';
import { mockData } from '../data/mockData';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';

const ResponsiveGridLayout = WidthProvider(Responsive);

interface SimpleWidget {
  id: string;
  title: string;
  type: string;
  content: React.ReactNode;
}

interface SimpleGridDashboardProps {
  editable?: boolean;
}

export const SimpleGridDashboard: React.FC<SimpleGridDashboardProps> = ({ editable = false }) => {
  const { theme } = useTheme();

  // Calculate real data
  const totalDevices = mockData.length;
  const connectedDevices = mockData.filter(d => d.agentStatus.toLowerCase() === 'connected').length;
  const healthPercentage = totalDevices > 0 ? (connectedDevices / totalDevices * 100).toFixed(1) : '0';
  
  const deviceCounts = mockData.reduce((acc, device) => {
    const type = device.type.toLowerCase();
    acc[type] = (acc[type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const [widgets, setWidgets] = useState<SimpleWidget[]>([
    {
      id: 'device-count',
      title: 'Device Overview',
      type: 'metric',
      content: (
        <div className="h-full flex flex-col justify-center">
          <div className={`text-3xl font-bold ${theme.textPrimary} text-center`}>
            {totalDevices}
          </div>
          <div className={`text-sm ${theme.textSecondary} text-center mt-1`}>
            Total Devices
          </div>
          <div className="mt-3 space-y-1">
            <div className="flex justify-between text-xs">
              <span className={theme.textMuted}>Desktop: {deviceCounts.desktop || 0}</span>
              <span className={theme.textMuted}>Laptop: {deviceCounts.laptop || 0}</span>
            </div>
            <div className="flex justify-between text-xs">
              <span className={theme.textMuted}>Server: {deviceCounts.server || 0}</span>
              <span className={theme.textMuted}>Mobile: {deviceCounts.mobile || 0}</span>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'agent-status',
      title: 'Agent Health',
      type: 'metric',
      content: (
        <div className="h-full flex flex-col justify-center">
          <div className={`text-3xl font-bold text-center ${
            parseFloat(healthPercentage) >= 80 ? 'text-green-500' :
            parseFloat(healthPercentage) >= 60 ? 'text-yellow-500' : 'text-red-500'
          }`}>
            {healthPercentage}%
          </div>
          <div className={`text-sm ${theme.textSecondary} text-center mt-1`}>
            Healthy Agents
          </div>
          <div className="mt-3 text-center">
            <div className={`text-xs ${theme.textMuted}`}>
              {connectedDevices}/{totalDevices} connected
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'recent-devices',
      title: 'Recent Devices',
      type: 'list',
      content: (
        <div className="h-full overflow-y-auto">
          <div className="space-y-2">
            {mockData.slice(0, 5).map((device) => (
              <div key={device.id} className={`p-2 rounded ${theme.bgTertiary} ${theme.borderPrimary} border`}>
                <div className={`text-sm font-medium ${theme.textPrimary} truncate`}>
                  {device.deviceName}
                </div>
                <div className="flex justify-between items-center mt-1">
                  <span className={`text-xs ${theme.textMuted}`}>
                    {device.type} • {device.os}
                  </span>
                  <span className={`text-xs px-2 py-0.5 rounded-full ${
                    device.agentStatus.toLowerCase() === 'connected' ? 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400' :
                    device.agentStatus.toLowerCase() === 'disconnected' ? 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400' :
                    'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400'
                  }`}>
                    {device.agentStatus}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )
    },
    {
      id: 'system-status',
      title: 'System Status',
      type: 'status',
      content: (
        <div className="h-full flex flex-col justify-center">
          <div className="text-center">
            <div className={`text-2xl font-bold text-green-500`}>
              Online
            </div>
            <div className={`text-sm ${theme.textSecondary} mt-1`}>
              All Systems Operational
            </div>
          </div>
          <div className="mt-4 space-y-2">
            <div className="flex justify-between items-center">
              <span className={`text-xs ${theme.textMuted}`}>API Status</span>
              <span className="text-xs text-green-500">●</span>
            </div>
            <div className="flex justify-between items-center">
              <span className={`text-xs ${theme.textMuted}`}>Database</span>
              <span className="text-xs text-green-500">●</span>
            </div>
            <div className="flex justify-between items-center">
              <span className={`text-xs ${theme.textMuted}`}>Monitoring</span>
              <span className="text-xs text-green-500">●</span>
            </div>
          </div>
        </div>
      )
    }
  ]);

  const [layouts, setLayouts] = useState<{ [key: string]: Layout[] }>({
    lg: [
      { i: 'device-count', x: 0, y: 0, w: 3, h: 3, minW: 2, minH: 2 },
      { i: 'agent-status', x: 3, y: 0, w: 3, h: 3, minW: 2, minH: 2 },
      { i: 'system-status', x: 6, y: 0, w: 3, h: 3, minW: 2, minH: 2 },
      { i: 'recent-devices', x: 0, y: 3, w: 9, h: 4, minW: 4, minH: 3 }
    ],
    md: [
      { i: 'device-count', x: 0, y: 0, w: 5, h: 3, minW: 3, minH: 2 },
      { i: 'agent-status', x: 5, y: 0, w: 5, h: 3, minW: 3, minH: 2 },
      { i: 'system-status', x: 0, y: 3, w: 5, h: 3, minW: 3, minH: 2 },
      { i: 'recent-devices', x: 5, y: 3, w: 5, h: 4, minW: 4, minH: 3 }
    ],
    sm: [
      { i: 'device-count', x: 0, y: 0, w: 6, h: 3, minW: 3, minH: 2 },
      { i: 'agent-status', x: 0, y: 3, w: 6, h: 3, minW: 3, minH: 2 },
      { i: 'system-status', x: 0, y: 6, w: 6, h: 3, minW: 3, minH: 2 },
      { i: 'recent-devices', x: 0, y: 9, w: 6, h: 4, minW: 4, minH: 3 }
    ],
    xs: [
      { i: 'device-count', x: 0, y: 0, w: 4, h: 3, minW: 2, minH: 2 },
      { i: 'agent-status', x: 0, y: 3, w: 4, h: 3, minW: 2, minH: 2 },
      { i: 'system-status', x: 0, y: 6, w: 4, h: 3, minW: 2, minH: 2 },
      { i: 'recent-devices', x: 0, y: 9, w: 4, h: 4, minW: 3, minH: 3 }
    ],
    xxs: [
      { i: 'device-count', x: 0, y: 0, w: 2, h: 3, minW: 2, minH: 2 },
      { i: 'agent-status', x: 0, y: 3, w: 2, h: 3, minW: 2, minH: 2 },
      { i: 'system-status', x: 0, y: 6, w: 2, h: 3, minW: 2, minH: 2 },
      { i: 'recent-devices', x: 0, y: 9, w: 2, h: 4, minW: 2, minH: 3 }
    ]
  });

  const handleLayoutChange = (currentLayout: Layout[], allLayouts: { [key: string]: Layout[] }) => {
    setLayouts(allLayouts);
  };

  const removeWidget = (widgetId: string) => {
    setWidgets(prev => prev.filter(w => w.id !== widgetId));
    setLayouts(prev => ({
      ...prev,
      lg: prev.lg?.filter(item => item.i !== widgetId) || [],
      md: prev.md?.filter(item => item.i !== widgetId) || [],
      sm: prev.sm?.filter(item => item.i !== widgetId) || [],
      xs: prev.xs?.filter(item => item.i !== widgetId) || [],
      xxs: prev.xxs?.filter(item => item.i !== widgetId) || []
    }));
  };

  return (
    <div className="h-full p-4">
      <ResponsiveGridLayout
        className="layout"
        layouts={layouts}
        onLayoutChange={handleLayoutChange}
        breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
        cols={{ lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 }}
        rowHeight={60}
        isDraggable={editable}
        isResizable={editable}
        margin={[16, 16]}
        containerPadding={[0, 0]}
        useCSSTransforms={true}
        compactType="vertical"
      >
        {widgets.map((widget) => (
          <div key={widget.id} className="widget-container">
            <div className={`h-full w-full ${theme.bgSecondary} ${theme.borderPrimary} border rounded-lg shadow-sm overflow-hidden transition-all duration-200 hover:shadow-md`}>
              {/* Widget Header */}
              <div className={`flex items-center justify-between p-3 ${theme.bgTertiary} ${theme.borderPrimary} border-b`}>
                <h3 className={`text-sm font-medium ${theme.textPrimary} truncate`}>
                  {widget.title}
                </h3>
                
                {editable && (
                  <button
                    onClick={() => removeWidget(widget.id)}
                    className={`p-1 rounded ${theme.hoverBg} ${theme.textMuted} hover:${theme.error} transition-colors`}
                    title="Remove Widget"
                  >
                    <X size={14} />
                  </button>
                )}
              </div>

              {/* Widget Content */}
              <div className="p-3 h-[calc(100%-52px)] overflow-hidden">
                {widget.content}
              </div>
            </div>
          </div>
        ))}
      </ResponsiveGridLayout>
    </div>
  );
};
