import React from 'react';
import {
  Settings,
  HelpCircle,
  AlertCircle,
  Bell,
  Shield,
  Database,
  Server,
  Cloud,
  Terminal,
  FileText,
  Download,
  LogOut
} from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';

interface SlidingMenuProps {
  isOpen: boolean;
  onClose: () => void;
  darkMode: boolean;
}

export const SlidingMenu: React.FC<SlidingMenuProps> = ({ isOpen, onClose, darkMode }) => {
  const { theme } = useTheme();
  const menuItems = [
    { icon: <Settings className="w-5 h-5" />, label: 'Settings' },
    { icon: <Shield className="w-5 h-5" />, label: 'Security' },
    { icon: <Bell className="w-5 h-5" />, label: 'Notifications' },
    { icon: <Database className="w-5 h-5" />, label: 'Database' },
    { icon: <Server className="w-5 h-5" />, label: 'Servers' },
    { icon: <Cloud className="w-5 h-5" />, label: 'Cloud Services' },
    { icon: <Terminal className="w-5 h-5" />, label: 'Console' },
    { icon: <AlertCircle className="w-5 h-5" />, label: 'Alerts' },
    { icon: <FileText className="w-5 h-5" />, label: 'Documentation' },
    { icon: <Download className="w-5 h-5" />, label: 'Downloads' },
    { icon: <HelpCircle className="w-5 h-5" />, label: 'Help' },
    { icon: <LogOut className="w-5 h-5" />, label: 'Logout' },
  ];

  return (
    <>
      {/* Overlay */}
      {isOpen && (
        <div
          className={`fixed inset-0 ${theme.slidingMenuOverlay} z-40`}
          onClick={onClose}
        />
      )}

      {/* Menu */}
      <div
        className={`fixed top-0 right-0 h-full w-64 ${theme.slidingMenuBg} shadow-lg transform transition-transform duration-300 ease-in-out z-50 ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
      >
        <div className={`p-4 border-b ${theme.slidingMenuBorder}`}>
          <h2 className={`text-lg font-semibold ${theme.slidingMenuTitle}`}>
            Menu
          </h2>
        </div>

        <div className="py-2">
          {menuItems.map((item, index) => (
            <button
              key={index}
              className={`w-full flex items-center gap-3 px-4 py-3 text-sm transition-colors ${theme.slidingMenuItemText} ${theme.slidingMenuItemHover}`}
            >
              {item.icon}
              <span>{item.label}</span>
            </button>
          ))}
        </div>
      </div>
    </>
  );
};