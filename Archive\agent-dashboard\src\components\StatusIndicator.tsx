import React from 'react';
import { useTheme } from '../contexts/ThemeContext';

interface StatusIndicatorProps {
  value: number;
  type?: 'cpu' | 'memory' | 'storage';
  darkMode?: boolean; // Keep for backward compatibility
}

export const StatusIndicator: React.FC<StatusIndicatorProps> = ({ value, type = 'cpu', darkMode: propDarkMode }) => {
  const { theme, darkMode } = useTheme();

  const getColor = () => {
    if (value > 80) return theme.statusDanger;
    if (value > 60) return theme.statusWarning;
    return theme.statusSuccess;
  };

  const getTrackBg = () => {
    return theme.statusTrackBg;
  };

  const getTextColor = () => {
    return theme.statusValueText;
  };

  return (
    <div className="flex items-center gap-1.5">
      <div className={`w-16 h-3 rounded-full overflow-hidden ${getTrackBg()}`}>
        <div
          className={`h-full ${getColor()} transition-all duration-300`}
          style={{ width: `${value}%` }}
        />
      </div>
      <span className={`text-xs ${getTextColor()}`}>{value}%</span>
    </div>
  );
}
