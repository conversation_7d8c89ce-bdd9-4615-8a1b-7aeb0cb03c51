import React, { useState, useEffect, useRef } from 'react';
import { Settings, X, Check, Palette } from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';
import { PopupWindow } from './ui/PopupWindow';
import { Button } from './ui/Button';
import { AccentColor } from '../types/theme';
import { useDraggableMenu } from '../hooks/useDraggableMenu';
import { ColorPickerMenu } from './ColorPickerMenu';

export const ThemeCustomizer: React.FC = () => {
  const { theme, darkMode, toggleDarkMode, accentColor, setThemeAccentColor } = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const menuRef = useRef<HTMLDivElement>(null);
  const colorPickerMenu = useDraggableMenu();

  // Close the menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isOpen &&
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen]);

  const handleToggle = () => {
    setIsOpen(!isOpen);
  };

  const handleAdvancedCustomize = () => {
    setIsOpen(false);
    // Position the color picker at the same position as the theme popup
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      colorPickerMenu.setMenuPosition({
        x: Math.max(0, rect.left - 400), // Position to the left of the button
        y: rect.bottom + 8 // Position below the button
      });
    } else {
      colorPickerMenu.setMenuPosition({ x: 100, y: 100 });
    }
    colorPickerMenu.setShowMenu(true);
  };

  return (
    <div className="relative" ref={menuRef}>
      <button
        ref={buttonRef}
        onClick={handleToggle}
        className={`group flex items-center gap-2 p-0.5 rounded-xl transition-all duration-200 hover:scale-105 ${theme.textMuted} ${theme.hoverText}`}
      >
        <div className={`p-1 rounded-xl transition-colors duration-200 ${theme.headerIconBg}`}>
          <Palette className="w-4 h-4" />
        </div>
      </button>

      {isOpen && (
        <PopupWindow
          isOpen={isOpen}
          anchorRef={buttonRef}
          title="Theme Settings"
          subtitle="Customize your experience"
          width="w-72"
          onClose={() => setIsOpen(false)}
        >
          <div className="p-4 space-y-4">
            <div className="space-y-2">
              <h3 className={`text-sm font-medium ${theme.textPrimary}`}>Mode</h3>
              <div className="flex items-center justify-between">
                <span className={`text-sm ${theme.textSecondary}`}>Dark Mode</span>
                <button
                  onClick={toggleDarkMode}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    darkMode ? theme.buttonPrimary : theme.bgTertiary
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full ${theme.accentTextColor} transition-transform ${
                      darkMode ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </div>

            <div className="space-y-2">
              <h3 className={`text-sm font-medium ${theme.textPrimary}`}>Accent Color</h3>
              <div className="grid grid-cols-5 gap-2">
                {['blue', 'green', 'red', 'purple', 'orange'].map((color) => (
                  <button
                    key={color}
                    onClick={() => setThemeAccentColor(color as AccentColor)}
                    className={`w-8 h-8 rounded-full ${
                      color === 'blue'
                        ? theme.accentBlue
                        : color === 'green'
                        ? theme.accentGreen
                        : color === 'red'
                        ? theme.accentRed
                        : color === 'purple'
                        ? theme.accentPurple
                        : theme.accentOrange
                    } flex items-center justify-center`}
                  >
                    {color === accentColor && <Check className={`w-4 h-4 ${theme.accentTextColor}`} />}
                  </button>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <h3 className={`text-sm font-medium ${theme.textPrimary}`}>Font Size</h3>
              <div className="flex items-center space-x-2">
                <button className={`px-2 py-1 rounded ${theme.bgTertiary} text-xs`}>Small</button>
                <button className={`px-2 py-1 rounded ${theme.accentBlue} ${theme.accentTextColor} text-xs`}>
                  Medium
                </button>
                <button className={`px-2 py-1 rounded ${theme.bgTertiary} text-xs`}>Large</button>
              </div>
            </div>

            <div className="pt-2 space-y-2">
              <Button
                variant="primary"
                size="sm"
                className="w-full"
                onClick={handleAdvancedCustomize}
              >
                Advanced Color Customization
              </Button>

              <Button
                variant="secondary"
                size="sm"
                className="w-full"
                onClick={() => setIsOpen(false)}
              >
                Close
              </Button>
            </div>
          </div>
        </PopupWindow>
      )}

      {colorPickerMenu.showMenu && (
        <ColorPickerMenu
          menuRef={colorPickerMenu.menuRef}
          position={colorPickerMenu.menuPosition}
          isDragging={colorPickerMenu.isDragging}
          onMouseDown={colorPickerMenu.handleMouseDown}
          onClose={() => colorPickerMenu.setShowMenu(false)}
        />
      )}
    </div>
  );
};
