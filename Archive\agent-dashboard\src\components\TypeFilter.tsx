import React, { useState, useRef, useEffect } from 'react';
import { Filter } from 'lucide-react';
import { DeviceIcon } from './DeviceIcon';
import { useTheme } from '../contexts/ThemeContext';
import { PopupWindow } from './ui/PopupWindow';
import { MenuItem } from './ui/MenuItem';

interface TypeFilterProps {
  onFilterChange: (value: string[]) => void;
  currentFilter: string[];
  darkMode?: boolean; // Keep for backward compatibility
}

export const TypeFilter: React.FC<TypeFilterProps> = ({ onFilterChange, currentFilter = [], darkMode: propDarkMode }) => {
  const { theme, darkMode } = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsOpen(!isOpen);
  };

  const handleOptionClick = (value: string) => {
    let newValues: string[];
    if (currentFilter.includes(value)) {
      newValues = currentFilter.filter(v => v !== value);
    } else {
      newValues = [...currentFilter, value];
    }

    onFilterChange(newValues);
  };

  const options = [
    { value: 'desktop', label: 'Desktop', icon: <DeviceIcon type="desktop" darkMode={darkMode} /> },
    { value: 'laptop', label: 'Laptop', icon: <DeviceIcon type="laptop" darkMode={darkMode} /> },
    { value: 'server', label: 'Server', icon: <DeviceIcon type="server" darkMode={darkMode} /> },
    { value: 'tablet', label: 'Tablet', icon: <DeviceIcon type="tablet" darkMode={darkMode} /> },
    { value: 'mobile', label: 'Mobile', icon: <DeviceIcon type="mobile" darkMode={darkMode} /> },
    { value: 'iot', label: 'IoT Device', icon: <DeviceIcon type="iot" darkMode={darkMode} /> },
  ].sort((a, b) => a.label.localeCompare(b.label));

  const hasFilters = currentFilter.length > 0;

  return (
    <div className="relative" ref={menuRef}>
      <button
        ref={buttonRef}
        onClick={handleClick}
        className={`group flex items-center gap-2 p-0.5 rounded-xl transition-all duration-200 hover:scale-105 ${
          hasFilters
            ? theme.textBlue
            : theme.textMuted + ' ' + theme.hoverText
        }`}
      >
        <div className="filter-component-icon p-1 rounded-xl transition-colors duration-200">
          <Filter className="w-4 h-4" />
        </div>
        {hasFilters && (
          <span className={`text-xs font-medium ${theme.textSecondary}`}>
            {currentFilter.length}
          </span>
        )}
      </button>

      {isOpen && (
        <PopupWindow
          isOpen={isOpen}
          anchorRef={buttonRef}
          title="Device Type"
          subtitle="Filter by type"
          width="w-64"
          onClose={() => setIsOpen(false)}
        >
          {options.map((option) => (
            <MenuItem
              key={option.value}
              icon={option.icon}
              label={option.label}
              isActive={Array.isArray(currentFilter) ? currentFilter.includes(option.value) : false}
              onClick={() => handleOptionClick(option.value)}
            />
          ))}
        </PopupWindow>
      )}
    </div>
  );
};
