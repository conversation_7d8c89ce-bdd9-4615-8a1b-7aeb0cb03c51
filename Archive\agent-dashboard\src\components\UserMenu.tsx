import React, { useState, useRef, useEffect } from 'react';
import { LogOut, User, Settings, HelpCircle } from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';
import { PopupWindow } from './ui/PopupWindow';
import { MenuItem } from './ui/MenuItem';

export const UserMenu: React.FC = () => {
  const { theme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleClick = () => {
    setIsOpen(!isOpen);
  };

  const menuItems = [
    { icon: <Settings className="w-4 h-4" />, label: 'Settings' },
    { icon: <HelpCircle className="w-4 h-4" />, label: 'Help' },
    { icon: <LogOut className="w-4 h-4" />, label: 'Logout' },
  ];

  return (
    <div className="relative" ref={menuRef}>
      <button
        ref={buttonRef}
        onClick={handleClick}
        className={`group flex items-center gap-2 p-0.5 rounded-xl transition-all duration-200 hover:scale-105 ${theme.textMuted} ${theme.hoverText}`}
      >
        <div className={`p-1 rounded-xl transition-colors duration-200 ${theme.headerIconBg}`}>
          <User className="w-4 h-4" />
        </div>
      </button>

      {isOpen && (
        <PopupWindow
          isOpen={isOpen}
          anchorRef={buttonRef}
          title="Administrator"
          subtitle="<EMAIL>"
          width="w-56"
          onClose={() => setIsOpen(false)}
        >
          {menuItems.map((item, index) => (
            <MenuItem
              key={index}
              icon={item.icon}
              label={item.label}
            />
          ))}
        </PopupWindow>
      )}
    </div>
  );
};
