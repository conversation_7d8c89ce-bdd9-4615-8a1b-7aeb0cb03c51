import React from 'react';
import {
  LayoutGrid,
  BarChart3,
  Calendar,
  FileText,
  Bell,
  Settings,
  Layers,
  HelpCircle
} from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';

export const VerticalMenu: React.FC = () => {
  const { theme } = useTheme();

  const menuItems = [
    { icon: <LayoutGrid className="w-6 h-6" />, label: 'Dashboard' },
    { icon: <BarChart3 className="w-6 h-6" />, label: 'Analytics' },
    { icon: <Calendar className="w-6 h-6" />, label: 'Calendar' },
    { icon: <FileText className="w-6 h-6" />, label: 'Documents' },
    { icon: <Bell className="w-6 h-6" />, label: 'Alerts' },
    { icon: <HelpCircle className="w-6 h-6" />, label: 'Help' },
    { icon: <Settings className="w-6 h-6" />, label: 'Settings' },
  ];

  // Create a menu item component for reuse
  const MenuItem = ({ icon, label }: { icon: React.ReactNode; label: string }) => (
    <div className="flex flex-col items-center group">
      <button
        className={`flex items-center justify-center w-10 h-10 ${theme.verticalMenuIconColor} ${theme.verticalMenuIconHover} transition-all duration-200`}
      >
        {icon}
      </button>
      <span className={`text-xs mt-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 ${theme.verticalMenuLabelColor}`}>
        {label}
      </span>
    </div>
  );

  return (
    <>
      {/* Logo in its own fixed container */}
      <div className="fixed left-0 top-0 z-50 w-24 flex justify-center pt-6 bg-transparent">
        <img
          src="/assets/images/logo.png" /* Update this extension if your logo is in a different format */
          alt="Logo"
          className="w-12 h-12"
        />
      </div>

      {/* Vertical menu */}
      <div className="fixed left-0 top-0 bottom-0 z-40">
        <div className={`${theme.verticalMenuBg} h-full w-24 flex flex-col py-6 pt-24`}>

        {/* All menu icons in a single container */}
        <div className="h-full flex flex-col items-center py-6">
          <div className="flex flex-col items-center space-y-6">
            {menuItems.map((item, index) => (
              <MenuItem key={index} icon={item.icon} label={item.label} />
            ))}
          </div>
        </div>
      </div>
    </div>
    </>
  );
};
