import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';

type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost';
type ButtonSize = 'sm' | 'md' | 'lg';

interface ButtonProps {
  children: React.ReactNode;
  variant?: ButtonVariant;
  size?: ButtonSize;
  onClick?: () => void;
  className?: string;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
  icon?: React.ReactNode;
}

export const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  onClick,
  className = '',
  disabled = false,
  type = 'button',
  icon
}) => {
  const { theme, darkMode } = useTheme();

  const getVariantClasses = (): string => {
    switch (variant) {
      case 'primary':
        return `${theme.buttonPrimary} ${theme.buttonPrimaryHover} ${theme.buttonText}`;
      case 'secondary':
        return `${theme.bgTertiary} ${theme.textPrimary} ${theme.hoverBg}`;
      case 'outline':
        return `border ${theme.borderPrimary} ${theme.textPrimary} hover:${theme.bgTertiary}`;
      case 'ghost':
        return `${theme.textPrimary} hover:${theme.bgTertiary}`;
      default:
        return `${theme.buttonPrimary} ${theme.buttonPrimaryHover} ${theme.buttonText}`;
    }
  };

  const getSizeClasses = (): string => {
    switch (size) {
      case 'sm':
        return 'px-2 py-1 text-xs';
      case 'md':
        return 'px-4 py-2 text-sm';
      case 'lg':
        return 'px-6 py-3 text-base';
      default:
        return 'px-4 py-2 text-sm';
    }
  };

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={`
        rounded-md font-medium transition-colors duration-200
        ${getVariantClasses()}
        ${getSizeClasses()}
        ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        ${className}
      `}
    >
      <div className="flex items-center justify-center gap-2">
        {icon && <span>{icon}</span>}
        {children}
      </div>
    </button>
  );
};
