import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';

interface IconButtonProps {
  icon: React.ReactNode;
  onClick?: () => void;
  label?: string;
  className?: string;
}

export const IconButton: React.FC<IconButtonProps> = ({
  icon,
  onClick,
  label,
  className = ''
}) => {
  const { theme } = useTheme();

  return (
    <button
      onClick={onClick}
      className={`group flex items-center gap-2 p-0.5 rounded-xl transition-all duration-200 hover:scale-105 ${theme.textMuted} ${theme.hoverText} ${className}`}
    >
      <div className={`p-1 rounded-xl transition-colors duration-200 ${theme.headerIconBg}`}>
        {icon}
      </div>
      {label && <span className={`text-xs font-medium ${theme.textSecondary}`}>{label}</span>}
    </button>
  );
};
