import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';

interface MenuItemProps {
  icon?: React.ReactNode;
  label: string;
  description?: string;
  onClick?: (e: React.MouseEvent) => void;
  isActive?: boolean;
  disabled?: boolean;
}

export const MenuItem: React.FC<MenuItemProps> = ({
  icon,
  label,
  description,
  onClick,
  isActive = false,
  disabled = false
}) => {
  const { theme } = useTheme();
  
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`w-full flex items-center gap-3 px-4 py-2 text-sm transition-colors duration-200 
        ${disabled 
          ? `${theme.textMuted} cursor-not-allowed` 
          : `${theme.textSecondary} ${theme.hoverText} hover:font-medium`
        }
        ${isActive ? theme.textBlue : ''}
      `}
    >
      {icon && (
        <div className="w-5 h-5 flex-shrink-0 flex items-center justify-center">
          {icon}
        </div>
      )}
      <div className="flex-1 text-left">
        <div>{label}</div>
        {description && (
          <div className={`text-xs font-normal ${theme.textMuted}`}>
            {description}
          </div>
        )}
      </div>
    </button>
  );
};
