import React, { useState, useRef, useEffect } from 'react';
import { useTheme } from '../../contexts/ThemeContext';

interface PopupWindowProps {
  isOpen: boolean;
  anchorRef: React.RefObject<HTMLElement>;
  title: string;
  subtitle?: string;
  children: React.ReactNode;
  width?: string;
  onClose?: () => void;
}

export const PopupWindow: React.FC<PopupWindowProps> = ({
  isOpen,
  anchorRef,
  title,
  subtitle,
  children,
  width = 'w-56',
  onClose
}) => {
  const { theme, darkMode } = useTheme();
  // Initialize position off-screen to prevent flashing at 0,0
  const [position, setPosition] = useState({ x: -1000, y: -1000 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const popupRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isOpen && anchorRef.current && popupRef.current) {
      const anchorRect = anchorRef.current.getBoundingClientRect();
      // Position below and to the left of the anchor element
      setPosition({
        x: Math.max(0, anchorRect.left - popupRef.current.offsetWidth + 40), // Position to the left
        y: anchorRect.bottom + 8
      });
    }
  }, [isOpen, anchorRef]);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (isDragging) {
        e.preventDefault();
        setPosition({
          x: e.clientX - dragOffset.x,
          y: e.clientY - dragOffset.y
        });
      }
    };

    const handleMouseUp = () => {
      setIsDragging(false);
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, dragOffset]);

  const handleDragStart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (popupRef.current) {
      setIsDragging(true);
      setDragOffset({
        x: e.clientX - position.x,
        y: e.clientY - position.y
      });
    }
  };

  if (!isOpen) return null;

  return (
    <div
      ref={popupRef}
      style={{
        position: 'fixed',
        left: `${position.x}px`,
        top: `${position.y}px`,
        zIndex: 9999,
        opacity: position.x === -1000 ? 0 : 1,
        transition: 'opacity 0.2s ease-in-out',
      }}
      className={`${width} rounded-md shadow-lg ${theme.popupBg} border ${theme.popupBorder} backdrop-blur-sm overflow-hidden`}
    >
      <div
        className={`px-4 py-3 ${theme.popupHeaderBg} ${theme.textPrimary} rounded-t-md cursor-grab sticky top-0 ${isDragging ? 'cursor-grabbing' : ''}`}
        onMouseDown={handleDragStart}
      >
        <div className="text-sm font-medium text-center">{title}</div>
        {subtitle && <div className="text-xs opacity-80 mt-0.5 text-center">{subtitle}</div>}
      </div>
      <hr className={`border-t ${theme.borderPrimary} mx-2 my-1`} />
      <div className="py-1 max-h-[70vh] overflow-y-auto">
        {children}
      </div>
    </div>
  );
};
