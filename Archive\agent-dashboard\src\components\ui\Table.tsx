import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';

interface TableProps {
  children: React.ReactNode;
  className?: string;
}

export const Table: React.FC<TableProps> = ({ children, className = '' }) => {
  const { theme } = useTheme();
  
  return (
    <div className={`overflow-x-auto ${className}`}>
      <table className={`w-full text-xs ${theme.textPrimary}`}>
        {children}
      </table>
    </div>
  );
};

interface TableHeadProps {
  children: React.ReactNode;
}

export const TableHead: React.FC<TableHeadProps> = ({ children }) => {
  const { theme } = useTheme();
  
  return (
    <thead className={`sticky top-0 z-10 ${theme.bgSecondary}`}>
      {children}
    </thead>
  );
};

interface TableBodyProps {
  children: React.ReactNode;
}

export const TableBody: React.FC<TableBodyProps> = ({ children }) => {
  return (
    <tbody className="relative">
      {children}
    </tbody>
  );
};

interface TableRowProps {
  children: React.ReactNode;
  isHeader?: boolean;
  onClick?: () => void;
}

export const TableRow: React.FC<TableRowProps> = ({ 
  children, 
  isHeader = false,
  onClick
}) => {
  const { theme } = useTheme();
  
  return (
    <tr 
      className={`transition-colors ${!isHeader ? theme.hoverBg : ''}`}
      onClick={onClick}
    >
      {children}
    </tr>
  );
};

interface TableCellProps {
  children: React.ReactNode;
  isHeader?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

export const TableCell: React.FC<TableCellProps> = ({ 
  children, 
  isHeader = false,
  className = '',
  style
}) => {
  const { theme } = useTheme();
  
  const Cell = isHeader ? 'th' : 'td';
  
  return (
    <Cell 
      className={`px-3 py-2 ${isHeader ? 'font-medium' : ''} ${className}`}
      style={style}
    >
      {children}
    </Cell>
  );
};

interface TableContainerProps {
  children: React.ReactNode;
}

export const TableContainer: React.FC<TableContainerProps> = ({ children }) => {
  const { theme } = useTheme();
  
  return (
    <div className={`rounded-2xl shadow-lg transition-colors duration-200 ${theme.bgSecondary}`}>
      {children}
    </div>
  );
};
