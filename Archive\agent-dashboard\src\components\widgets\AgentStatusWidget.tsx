import React from 'react';
import { Check<PERSON>ircle, AlertTriangle, <PERSON>Cir<PERSON>, Clock } from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';
import { WidgetContainer } from './WidgetContainer';
import { WidgetProps } from '../../types/dashboard';
import { mockData } from '../../data/mockData';

export const AgentStatusWidget: React.FC<WidgetProps> = (props) => {
  const { theme } = useTheme();
  
  // Calculate agent status from mock data
  const statusCounts = mockData.reduce((acc, device) => {
    const status = device.agentStatus.toLowerCase();
    acc[status] = (acc[status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const totalAgents = mockData.length;

  const statusTypes = [
    { 
      name: 'Connected', 
      count: statusCounts.connected || 0, 
      icon: CheckCircle, 
      color: 'text-green-500',
      bgColor: 'bg-green-100 dark:bg-green-900/20'
    },
    { 
      name: 'Outdated', 
      count: statusCounts.outdated || 0, 
      icon: AlertTriangle, 
      color: 'text-yellow-500',
      bgColor: 'bg-yellow-100 dark:bg-yellow-900/20'
    },
    { 
      name: 'Disconnected', 
      count: statusCounts.disconnected || 0, 
      icon: XCircle, 
      color: 'text-red-500',
      bgColor: 'bg-red-100 dark:bg-red-900/20'
    },
    { 
      name: 'Pending', 
      count: statusCounts.pending || 0, 
      icon: Clock, 
      color: 'text-blue-500',
      bgColor: 'bg-blue-100 dark:bg-blue-900/20'
    }
  ];

  const healthyPercentage = totalAgents > 0 ? 
    ((statusCounts.connected || 0) / totalAgents * 100).toFixed(1) : '0';

  return (
    <WidgetContainer {...props}>
      <div className="h-full flex flex-col">
        {/* Health Score */}
        <div className="text-center mb-4">
          <div className={`text-2xl font-bold ${
            parseFloat(healthyPercentage) >= 80 ? 'text-green-500' :
            parseFloat(healthyPercentage) >= 60 ? 'text-yellow-500' : 'text-red-500'
          }`}>
            {healthyPercentage}%
          </div>
          <div className={`text-sm ${theme.textSecondary}`}>
            Agent Health
          </div>
        </div>

        {/* Status Breakdown */}
        <div className="flex-1 space-y-2">
          {statusTypes.map((status) => {
            const Icon = status.icon;
            const percentage = totalAgents > 0 ? (status.count / totalAgents * 100).toFixed(1) : '0';
            
            return (
              <div key={status.name} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className={`p-1.5 rounded ${status.bgColor}`}>
                    <Icon size={14} className={status.color} />
                  </div>
                  <span className={`text-sm ${theme.textSecondary}`}>
                    {status.name}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span className={`text-sm font-medium ${theme.textPrimary}`}>
                    {status.count}
                  </span>
                  <span className={`text-xs ${theme.textMuted}`}>
                    ({percentage}%)
                  </span>
                </div>
              </div>
            );
          })}
        </div>

        {/* Quick Actions */}
        <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
          <button className={`w-full text-xs py-1.5 px-2 rounded ${theme.buttonPrimary} ${theme.buttonText} hover:opacity-90 transition-opacity`}>
            Refresh All Agents
          </button>
        </div>
      </div>
    </WidgetContainer>
  );
};
