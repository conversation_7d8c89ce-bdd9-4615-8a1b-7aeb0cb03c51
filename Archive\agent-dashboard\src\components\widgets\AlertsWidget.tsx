import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, AlertCircle, Info, CheckCircle, Clock } from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';
import { WidgetContainer } from './WidgetContainer';
import { WidgetProps } from '../../types/dashboard';
import { mockData } from '../../data/mockData';

interface Alert {
  id: string;
  type: 'critical' | 'warning' | 'info' | 'success';
  title: string;
  message: string;
  timestamp: Date;
  deviceName?: string;
}

export const AlertsWidget: React.FC<WidgetProps> = (props) => {
  const { theme } = useTheme();
  
  // Generate mock alerts based on device data
  const generateAlerts = (): Alert[] => {
    const alerts: Alert[] = [];
    
    // Add alerts for disconnected devices
    mockData.filter(d => d.agentStatus.toLowerCase() === 'disconnected').forEach((device, index) => {
      if (index < 3) { // Limit to 3 disconnected alerts
        alerts.push({
          id: `disconnect-${device.id}`,
          type: 'critical',
          title: 'Device Disconnected',
          message: `${device.deviceName} has lost connection`,
          timestamp: new Date(Date.now() - Math.random() * 3600000), // Random time within last hour
          deviceName: device.deviceName
        });
      }
    });
    
    // Add alerts for outdated devices
    mockData.filter(d => d.agentStatus.toLowerCase() === 'outdated').forEach((device, index) => {
      if (index < 2) { // Limit to 2 outdated alerts
        alerts.push({
          id: `outdated-${device.id}`,
          type: 'warning',
          title: 'Agent Outdated',
          message: `${device.deviceName} needs agent update`,
          timestamp: new Date(Date.now() - Math.random() * 7200000), // Random time within last 2 hours
          deviceName: device.deviceName
        });
      }
    });
    
    // Add some system alerts
    alerts.push({
      id: 'system-health',
      type: 'info',
      title: 'System Health Check',
      message: 'Daily system health check completed successfully',
      timestamp: new Date(Date.now() - 1800000) // 30 minutes ago
    });
    
    if (mockData.filter(d => d.agentStatus.toLowerCase() === 'connected').length > mockData.length * 0.8) {
      alerts.push({
        id: 'connectivity-good',
        type: 'success',
        title: 'Good Connectivity',
        message: 'Most devices are online and healthy',
        timestamp: new Date(Date.now() - 900000) // 15 minutes ago
      });
    }
    
    return alerts.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime()).slice(0, 5);
  };

  const alerts = generateAlerts();

  const getAlertIcon = (type: Alert['type']) => {
    switch (type) {
      case 'critical': return AlertTriangle;
      case 'warning': return AlertCircle;
      case 'info': return Info;
      case 'success': return CheckCircle;
      default: return Info;
    }
  };

  const getAlertColor = (type: Alert['type']) => {
    switch (type) {
      case 'critical': return 'text-red-500';
      case 'warning': return 'text-yellow-500';
      case 'info': return 'text-blue-500';
      case 'success': return 'text-green-500';
      default: return 'text-gray-500';
    }
  };

  const getAlertBg = (type: Alert['type']) => {
    switch (type) {
      case 'critical': return 'bg-red-100 dark:bg-red-900/20';
      case 'warning': return 'bg-yellow-100 dark:bg-yellow-900/20';
      case 'info': return 'bg-blue-100 dark:bg-blue-900/20';
      case 'success': return 'bg-green-100 dark:bg-green-900/20';
      default: return 'bg-gray-100 dark:bg-gray-900/20';
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return timestamp.toLocaleDateString();
  };

  const criticalCount = alerts.filter(a => a.type === 'critical').length;
  const warningCount = alerts.filter(a => a.type === 'warning').length;

  return (
    <WidgetContainer {...props}>
      <div className="h-full flex flex-col">
        {/* Alert Summary */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-1">
              <AlertTriangle size={16} className="text-red-500" />
              <span className={`text-sm font-medium ${theme.textPrimary}`}>
                {criticalCount}
              </span>
            </div>
            <div className="flex items-center gap-1">
              <AlertCircle size={16} className="text-yellow-500" />
              <span className={`text-sm font-medium ${theme.textPrimary}`}>
                {warningCount}
              </span>
            </div>
          </div>
          <div className={`text-xs ${theme.textMuted}`}>
            {alerts.length} total
          </div>
        </div>

        {/* Alerts List */}
        <div className="flex-1 space-y-2 overflow-y-auto">
          {alerts.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center">
              <CheckCircle size={32} className="text-green-500 mb-2" />
              <p className={`text-sm ${theme.textSecondary}`}>No active alerts</p>
              <p className={`text-xs ${theme.textMuted}`}>All systems running smoothly</p>
            </div>
          ) : (
            alerts.map((alert) => {
              const Icon = getAlertIcon(alert.type);
              
              return (
                <div key={alert.id} className={`p-3 rounded-lg ${getAlertBg(alert.type)} border-l-4 ${
                  alert.type === 'critical' ? 'border-red-500' :
                  alert.type === 'warning' ? 'border-yellow-500' :
                  alert.type === 'info' ? 'border-blue-500' :
                  'border-green-500'
                }`}>
                  <div className="flex items-start gap-2">
                    <Icon size={16} className={`${getAlertColor(alert.type)} mt-0.5 flex-shrink-0`} />
                    <div className="flex-1 min-w-0">
                      <div className={`text-sm font-medium ${theme.textPrimary} truncate`}>
                        {alert.title}
                      </div>
                      <div className={`text-xs ${theme.textSecondary} mt-1 line-clamp-2`}>
                        {alert.message}
                      </div>
                      <div className="flex items-center justify-between mt-2">
                        {alert.deviceName && (
                          <span className={`text-xs ${theme.textMuted} truncate`}>
                            {alert.deviceName}
                          </span>
                        )}
                        <div className="flex items-center gap-1 ml-auto">
                          <Clock size={10} className={theme.textMuted} />
                          <span className={`text-xs ${theme.textMuted}`}>
                            {formatTimestamp(alert.timestamp)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })
          )}
        </div>

        {/* Footer */}
        {alerts.length > 0 && (
          <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
            <button className={`w-full text-xs py-1.5 px-2 rounded ${theme.buttonSecondary} ${theme.textSecondary} hover:opacity-90 transition-opacity`}>
              View All Alerts
            </button>
          </div>
        )}
      </div>
    </WidgetContainer>
  );
};
