import React from 'react';
import { Monitor, Smartphone, Tablet, Server } from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';
import { WidgetContainer } from './WidgetContainer';
import { WidgetProps } from '../../types/dashboard';
import { mockData } from '../../data/mockData';

export const DeviceCountWidget: React.FC<WidgetProps> = (props) => {
  const { theme } = useTheme();
  
  // Calculate device counts from mock data
  const deviceCounts = mockData.reduce((acc, device) => {
    const type = device.type.toLowerCase();
    acc[type] = (acc[type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const totalDevices = mockData.length;

  const deviceTypes = [
    {
      name: 'Desktop',
      count: deviceCounts.desktop || 0,
      icon: Monitor,
      color: 'text-blue-500',
      bgColor: 'bg-blue-100 dark:bg-blue-900/20'
    },
    {
      name: 'Laptop',
      count: deviceCounts.laptop || 0,
      icon: Monitor,
      color: 'text-green-500',
      bgColor: 'bg-green-100 dark:bg-green-900/20'
    },
    {
      name: 'Server',
      count: deviceCounts.server || 0,
      icon: Server,
      color: 'text-purple-500',
      bgColor: 'bg-purple-100 dark:bg-purple-900/20'
    },
    {
      name: 'Mobile',
      count: deviceCounts.mobile || 0,
      icon: Smartphone,
      color: 'text-orange-500',
      bgColor: 'bg-orange-100 dark:bg-orange-900/20'
    },
    {
      name: 'Tablet',
      count: deviceCounts.tablet || 0,
      icon: Tablet,
      color: 'text-pink-500',
      bgColor: 'bg-pink-100 dark:bg-pink-900/20'
    }
  ];

  return (
    <WidgetContainer {...props}>
      <div className="h-full flex flex-col">
        {/* Total Count */}
        <div className="text-center mb-4">
          <div className={`text-3xl font-bold ${theme.textPrimary}`}>
            {totalDevices}
          </div>
          <div className={`text-sm ${theme.textSecondary}`}>
            Total Devices
          </div>
        </div>

        {/* Device Type Breakdown */}
        <div className="flex-1 space-y-2">
          {deviceTypes.map((type) => {
            const Icon = type.icon;
            const percentage = totalDevices > 0 ? (type.count / totalDevices * 100).toFixed(1) : '0';
            
            return (
              <div key={type.name} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className={`p-1.5 rounded ${type.bgColor}`}>
                    <Icon size={14} className={type.color} />
                  </div>
                  <span className={`text-sm ${theme.textSecondary}`}>
                    {type.name}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span className={`text-sm font-medium ${theme.textPrimary}`}>
                    {type.count}
                  </span>
                  <span className={`text-xs ${theme.textMuted}`}>
                    ({percentage}%)
                  </span>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </WidgetContainer>
  );
};
