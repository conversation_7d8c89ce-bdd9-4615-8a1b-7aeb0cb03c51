import React, { useState } from 'react';
import { ChevronLeft, ChevronRight, Search } from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';
import { WidgetContainer } from './WidgetContainer';
import { WidgetProps } from '../../types/dashboard';
import { mockData } from '../../data/mockData';

export const DeviceTableWidget: React.FC<WidgetProps> = (props) => {
  const { theme } = useTheme();
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  
  const pageSize = props.widget.config?.tablePageSize || 5;
  
  // Filter data based on search term
  const filteredData = mockData.filter(device => 
    device.deviceName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    device.os.toLowerCase().includes(searchTerm.toLowerCase()) ||
    device.type.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  const totalPages = Math.ceil(filteredData.length / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const paginatedData = filteredData.slice(startIndex, startIndex + pageSize);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'connected': return 'text-green-500';
      case 'disconnected': return 'text-red-500';
      case 'outdated': return 'text-yellow-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusBg = (status: string) => {
    switch (status.toLowerCase()) {
      case 'connected': return 'bg-green-100 dark:bg-green-900/20';
      case 'disconnected': return 'bg-red-100 dark:bg-red-900/20';
      case 'outdated': return 'bg-yellow-100 dark:bg-yellow-900/20';
      default: return 'bg-gray-100 dark:bg-gray-900/20';
    }
  };

  return (
    <WidgetContainer {...props}>
      <div className="h-full flex flex-col">
        {/* Search Bar */}
        <div className="mb-3">
          <div className="relative">
            <Search size={16} className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${theme.textMuted}`} />
            <input
              type="text"
              placeholder="Search devices..."
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setCurrentPage(1); // Reset to first page on search
              }}
              className={`w-full pl-10 pr-3 py-2 text-sm rounded-lg ${theme.bgTertiary} ${theme.borderPrimary} border ${theme.textPrimary} placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500`}
            />
          </div>
        </div>

        {/* Table */}
        <div className="flex-1 overflow-hidden">
          <div className="overflow-x-auto h-full">
            <table className="w-full text-sm">
              <thead>
                <tr className={`${theme.bgTertiary} border-b ${theme.borderPrimary}`}>
                  <th className={`text-left p-2 font-medium ${theme.textSecondary}`}>Device</th>
                  <th className={`text-left p-2 font-medium ${theme.textSecondary}`}>OS</th>
                  <th className={`text-left p-2 font-medium ${theme.textSecondary}`}>Status</th>
                </tr>
              </thead>
              <tbody>
                {paginatedData.map((device, index) => (
                  <tr key={device.id} className={`border-b ${theme.borderPrimary} hover:${theme.hoverBg} transition-colors`}>
                    <td className={`p-2 ${theme.textPrimary}`}>
                      <div>
                        <div className="font-medium truncate">{device.deviceName}</div>
                        <div className={`text-xs ${theme.textMuted} capitalize`}>{device.type}</div>
                      </div>
                    </td>
                    <td className={`p-2 ${theme.textSecondary}`}>
                      {device.os}
                    </td>
                    <td className="p-2">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusBg(device.agentStatus)} ${getStatusColor(device.agentStatus)}`}>
                        {device.agentStatus}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
            <div className={`text-xs ${theme.textMuted}`}>
              Showing {startIndex + 1}-{Math.min(startIndex + pageSize, filteredData.length)} of {filteredData.length}
            </div>
            
            <div className="flex items-center gap-1">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className={`p-1 rounded ${currentPage === 1 ? 'opacity-50 cursor-not-allowed' : `hover:${theme.hoverBg}`} ${theme.textMuted}`}
              >
                <ChevronLeft size={16} />
              </button>
              
              <span className={`px-2 text-xs ${theme.textSecondary}`}>
                {currentPage} / {totalPages}
              </span>
              
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className={`p-1 rounded ${currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : `hover:${theme.hoverBg}`} ${theme.textMuted}`}
              >
                <ChevronRight size={16} />
              </button>
            </div>
          </div>
        )}
      </div>
    </WidgetContainer>
  );
};
