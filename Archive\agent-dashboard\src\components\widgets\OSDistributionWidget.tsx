import React from 'react';
import { Monitor, Smartphone, Server } from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';
import { WidgetContainer } from './WidgetContainer';
import { WidgetProps } from '../../types/dashboard';
import { mockData } from '../../data/mockData';

export const OSDistributionWidget: React.FC<WidgetProps> = (props) => {
  const { theme } = useTheme();
  
  // Calculate OS distribution from mock data
  const osDistribution = mockData.reduce((acc, device) => {
    const os = device.os.toLowerCase();
    acc[os] = (acc[os] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const totalDevices = mockData.length;

  const osTypes = [
    { 
      name: 'Windows', 
      count: osDistribution.windows || 0, 
      icon: Monitor, 
      color: 'text-blue-500',
      bgColor: 'bg-blue-100 dark:bg-blue-900/20'
    },
    { 
      name: 'macOS', 
      count: osDistribution.macos || 0, 
      icon: Monitor, 
      color: 'text-gray-500',
      bgColor: 'bg-gray-100 dark:bg-gray-900/20'
    },
    { 
      name: 'Linux', 
      count: osDistribution.linux || 0, 
      icon: Server, 
      color: 'text-orange-500',
      bgColor: 'bg-orange-100 dark:bg-orange-900/20'
    },
    { 
      name: 'Ubuntu', 
      count: osDistribution.ubuntu || 0, 
      icon: Server, 
      color: 'text-red-500',
      bgColor: 'bg-red-100 dark:bg-red-900/20'
    },
    { 
      name: 'iOS', 
      count: osDistribution.ios || 0, 
      icon: Smartphone, 
      color: 'text-purple-500',
      bgColor: 'bg-purple-100 dark:bg-purple-900/20'
    },
    { 
      name: 'Android', 
      count: osDistribution.android || 0, 
      icon: Smartphone, 
      color: 'text-green-500',
      bgColor: 'bg-green-100 dark:bg-green-900/20'
    }
  ].filter(os => os.count > 0); // Only show OS types that exist

  return (
    <WidgetContainer {...props}>
      <div className="h-full flex flex-col">
        {/* Total Count */}
        <div className="text-center mb-4">
          <div className={`text-2xl font-bold ${theme.textPrimary}`}>
            {osTypes.length}
          </div>
          <div className={`text-sm ${theme.textSecondary}`}>
            Operating Systems
          </div>
        </div>

        {/* OS Distribution */}
        <div className="flex-1 space-y-2 max-h-48 overflow-y-auto">
          {osTypes.map((os) => {
            const Icon = os.icon;
            const percentage = totalDevices > 0 ? (os.count / totalDevices * 100).toFixed(1) : '0';
            
            return (
              <div key={os.name} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className={`p-1.5 rounded ${os.bgColor}`}>
                    <Icon size={14} className={os.color} />
                  </div>
                  <span className={`text-sm ${theme.textSecondary}`}>
                    {os.name}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span className={`text-sm font-medium ${theme.textPrimary}`}>
                    {os.count}
                  </span>
                  <span className={`text-xs ${theme.textMuted}`}>
                    ({percentage}%)
                  </span>
                </div>
              </div>
            );
          })}
        </div>

        {/* Progress bars for top 3 OS */}
        <div className="mt-4 space-y-2">
          {osTypes.slice(0, 3).map((os) => {
            const percentage = totalDevices > 0 ? (os.count / totalDevices * 100) : 0;
            
            return (
              <div key={`bar-${os.name}`} className="space-y-1">
                <div className="flex justify-between text-xs">
                  <span className={theme.textSecondary}>{os.name}</span>
                  <span className={theme.textMuted}>{percentage.toFixed(1)}%</span>
                </div>
                <div className={`w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5`}>
                  <div 
                    className={`h-1.5 rounded-full transition-all duration-300 ${
                      os.color.includes('blue') ? 'bg-blue-500' :
                      os.color.includes('gray') ? 'bg-gray-500' :
                      os.color.includes('orange') ? 'bg-orange-500' :
                      os.color.includes('red') ? 'bg-red-500' :
                      os.color.includes('purple') ? 'bg-purple-500' :
                      'bg-green-500'
                    }`}
                    style={{ width: `${percentage}%` }}
                  />
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </WidgetContainer>
  );
};
