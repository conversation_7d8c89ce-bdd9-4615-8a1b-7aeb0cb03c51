import React from 'react';
import { Activity, Cpu, HardDrive, Wifi, Al<PERSON><PERSON>riangle } from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';
import { WidgetContainer } from './WidgetContainer';
import { WidgetProps } from '../../types/dashboard';
import { mockData } from '../../data/mockData';

export const SystemHealthWidget: React.FC<WidgetProps> = (props) => {
  const { theme } = useTheme();
  
  // Calculate system health metrics
  const connectedDevices = mockData.filter(d => d.agentStatus.toLowerCase() === 'connected').length;
  const totalDevices = mockData.length;
  const healthPercentage = totalDevices > 0 ? (connectedDevices / totalDevices * 100) : 0;
  
  // Mock system metrics (in real app, these would come from actual system monitoring)
  const systemMetrics = [
    {
      name: 'CPU Usage',
      value: Math.floor(Math.random() * 40) + 20, // 20-60%
      icon: Cpu,
      color: 'text-blue-500',
      bgColor: 'bg-blue-100 dark:bg-blue-900/20',
      threshold: 80
    },
    {
      name: 'Memory',
      value: Math.floor(Math.random() * 30) + 45, // 45-75%
      icon: Activity,
      color: 'text-green-500',
      bgColor: 'bg-green-100 dark:bg-green-900/20',
      threshold: 85
    },
    {
      name: 'Storage',
      value: Math.floor(Math.random() * 25) + 35, // 35-60%
      icon: HardDrive,
      color: 'text-purple-500',
      bgColor: 'bg-purple-100 dark:bg-purple-900/20',
      threshold: 90
    },
    {
      name: 'Network',
      value: Math.floor(Math.random() * 20) + 75, // 75-95%
      icon: Wifi,
      color: 'text-orange-500',
      bgColor: 'bg-orange-100 dark:bg-orange-900/20',
      threshold: 95
    }
  ];

  const getHealthColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-500';
    if (percentage >= 60) return 'text-yellow-500';
    return 'text-red-500';
  };

  const getMetricColor = (value: number, threshold: number) => {
    if (value >= threshold) return 'text-red-500';
    if (value >= threshold * 0.8) return 'text-yellow-500';
    return 'text-green-500';
  };

  const getProgressColor = (value: number, threshold: number) => {
    if (value >= threshold) return 'bg-red-500';
    if (value >= threshold * 0.8) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  return (
    <WidgetContainer {...props}>
      <div className="h-full flex flex-col">
        {/* Overall Health Score */}
        <div className="text-center mb-4">
          <div className={`text-2xl font-bold ${getHealthColor(healthPercentage)}`}>
            {healthPercentage.toFixed(1)}%
          </div>
          <div className={`text-sm ${theme.textSecondary}`}>
            System Health
          </div>
          <div className={`text-xs ${theme.textMuted} mt-1`}>
            {connectedDevices}/{totalDevices} devices online
          </div>
        </div>

        {/* System Metrics */}
        <div className="flex-1 space-y-3">
          {systemMetrics.map((metric) => {
            const Icon = metric.icon;
            const isWarning = metric.value >= metric.threshold * 0.8;
            const isCritical = metric.value >= metric.threshold;
            
            return (
              <div key={metric.name} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className={`p-1.5 rounded ${metric.bgColor}`}>
                      <Icon size={14} className={metric.color} />
                    </div>
                    <span className={`text-sm ${theme.textSecondary}`}>
                      {metric.name}
                    </span>
                    {(isWarning || isCritical) && (
                      <AlertTriangle size={12} className={isCritical ? 'text-red-500' : 'text-yellow-500'} />
                    )}
                  </div>
                  <span className={`text-sm font-medium ${getMetricColor(metric.value, metric.threshold)}`}>
                    {metric.value}%
                  </span>
                </div>
                
                <div className={`w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5`}>
                  <div 
                    className={`h-1.5 rounded-full transition-all duration-300 ${getProgressColor(metric.value, metric.threshold)}`}
                    style={{ width: `${metric.value}%` }}
                  />
                </div>
              </div>
            );
          })}
        </div>

        {/* Status Summary */}
        <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between text-xs">
            <span className={theme.textMuted}>Last Updated</span>
            <span className={theme.textSecondary}>
              {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </span>
          </div>
        </div>
      </div>
    </WidgetContainer>
  );
};
