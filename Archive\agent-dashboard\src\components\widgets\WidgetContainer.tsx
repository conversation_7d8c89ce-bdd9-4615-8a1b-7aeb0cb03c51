import React, { useEffect, useState } from 'react';
import { X, Settings, Maximize2, Minimize2, RefreshCw } from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';
import { WidgetProps } from '../../types/dashboard';

interface WidgetContainerProps extends WidgetProps {
  children: React.ReactNode;
  isExpanded?: boolean;
  onExpand?: () => void;
  onRefresh?: () => void;
}

export const WidgetContainer: React.FC<WidgetContainerProps> = ({
  widget,
  children,
  onUpdate,
  onRemove,
  editable = false,
  isExpanded = false,
  onExpand,
  onRefresh
}) => {
  const { theme } = useTheme();
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Auto-refresh functionality
  useEffect(() => {
    if (!widget.config?.refreshInterval) return;

    const interval = setInterval(() => {
      setLastRefresh(new Date());
      if (onRefresh) {
        setIsRefreshing(true);
        onRefresh();
        setTimeout(() => setIsRefreshing(false), 500);
      }
    }, widget.config.refreshInterval);

    return () => clearInterval(interval);
  }, [widget.config?.refreshInterval, onRefresh]);

  const handleManualRefresh = () => {
    if (onRefresh && !isRefreshing) {
      setIsRefreshing(true);
      setLastRefresh(new Date());
      onRefresh();
      setTimeout(() => setIsRefreshing(false), 500);
    }
  };

  return (
    <div className={`h-full w-full ${theme.bgSecondary} ${theme.borderPrimary} border rounded-lg shadow-sm overflow-hidden transition-all duration-200 hover:shadow-md`}>
      {/* Widget Header */}
      <div className={`flex items-center justify-between p-3 ${theme.bgTertiary} ${theme.borderPrimary} border-b`}>
        <h3 className={`text-sm font-medium ${theme.textPrimary} truncate`}>
          {widget.config?.customTitle || widget.title}
        </h3>
        
        <div className="flex items-center gap-1">
          {/* Refresh button (always visible) */}
          <button
            onClick={handleManualRefresh}
            disabled={isRefreshing}
            className={`p-1 rounded ${theme.hoverBg} ${theme.textMuted} hover:${theme.textPrimary} transition-colors ${isRefreshing ? 'animate-spin' : ''}`}
            title="Refresh"
          >
            <RefreshCw size={14} />
          </button>

          {editable && (
            <>
              {onExpand && (
                <button
                  onClick={onExpand}
                  className={`p-1 rounded ${theme.hoverBg} ${theme.textMuted} hover:${theme.textPrimary} transition-colors`}
                  title={isExpanded ? "Minimize" : "Expand"}
                >
                  {isExpanded ? <Minimize2 size={14} /> : <Maximize2 size={14} />}
                </button>
              )}

              <button
                onClick={() => onUpdate && onUpdate({})}
                className={`p-1 rounded ${theme.hoverBg} ${theme.textMuted} hover:${theme.textPrimary} transition-colors`}
                title="Settings"
              >
                <Settings size={14} />
              </button>

              {onRemove && (
                <button
                  onClick={onRemove}
                  className={`p-1 rounded ${theme.hoverBg} ${theme.textMuted} hover:${theme.error} transition-colors`}
                  title="Remove Widget"
                >
                  <X size={14} />
                </button>
              )}
            </>
          )}
        </div>
      </div>

      {/* Widget Content */}
      <div className="p-3 h-[calc(100%-52px)] overflow-hidden">
        {children}
      </div>
    </div>
  );
};
