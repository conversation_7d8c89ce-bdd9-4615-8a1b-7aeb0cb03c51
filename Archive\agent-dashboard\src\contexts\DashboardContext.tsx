import React, { createContext, useContext, useState, useEffect } from 'react';
import { Layout } from 'react-grid-layout';
import { DashboardLayout, DashboardWidget, DEFAULT_WIDGET_SIZES } from '../types/dashboard';

interface DashboardContextType {
  currentLayout: DashboardLayout;
  layouts: DashboardLayout[];
  isEditing: boolean;
  setIsEditing: (editing: boolean) => void;
  switchLayout: (layoutId: string) => void;
  updateLayout: (layouts: { [key: string]: Layout[] }) => void;
  addWidget: (widget: DashboardWidget) => void;
  removeWidget: (widgetId: string) => void;
  updateWidget: (widgetId: string, updates: Partial<DashboardWidget>) => void;
  saveLayout: () => void;
  resetLayout: () => void;
}

const DashboardContext = createContext<DashboardContextType | undefined>(undefined);

// Default dashboard layout
const createDefaultLayout = (): DashboardLayout => ({
  id: 'default',
  name: 'Default Dashboard',
  isDefault: true,
  widgets: [
    {
      id: 'device-count-1',
      title: 'Device Overview',
      type: 'device-count',
      config: { showHeader: true, refreshInterval: 30000 }
    },
    {
      id: 'agent-status-1',
      title: 'Agent Status',
      type: 'agent-status',
      config: { showHeader: true, refreshInterval: 15000 }
    },
    {
      id: 'system-health-1',
      title: 'System Health',
      type: 'system-health',
      config: { showHeader: true, refreshInterval: 10000 }
    },
    {
      id: 'os-distribution-1',
      title: 'OS Distribution',
      type: 'os-distribution',
      config: { showHeader: true, refreshInterval: 60000 }
    },
    {
      id: 'alerts-1',
      title: 'Active Alerts',
      type: 'alerts',
      config: { showHeader: true, refreshInterval: 5000 }
    },
    {
      id: 'device-table-1',
      title: 'Recent Devices',
      type: 'device-table',
      config: { showHeader: true, refreshInterval: 30000, tablePageSize: 5 }
    }
  ],
  layouts: {
    lg: [
      { i: 'device-count-1', x: 0, y: 0, w: 3, h: 3, minW: 2, minH: 2 },
      { i: 'agent-status-1', x: 3, y: 0, w: 3, h: 3, minW: 2, minH: 2 },
      { i: 'system-health-1', x: 6, y: 0, w: 3, h: 3, minW: 2, minH: 2 },
      { i: 'os-distribution-1', x: 9, y: 0, w: 3, h: 4, minW: 3, minH: 3 },
      { i: 'alerts-1', x: 0, y: 3, w: 6, h: 4, minW: 4, minH: 3 },
      { i: 'device-table-1', x: 0, y: 7, w: 12, h: 5, minW: 6, minH: 4 }
    ],
    md: [
      { i: 'device-count-1', x: 0, y: 0, w: 3, h: 3, minW: 2, minH: 2 },
      { i: 'agent-status-1', x: 3, y: 0, w: 3, h: 3, minW: 2, minH: 2 },
      { i: 'system-health-1', x: 6, y: 0, w: 4, h: 3, minW: 2, minH: 2 },
      { i: 'os-distribution-1', x: 0, y: 3, w: 5, h: 4, minW: 3, minH: 3 },
      { i: 'alerts-1', x: 5, y: 3, w: 5, h: 4, minW: 4, minH: 3 },
      { i: 'device-table-1', x: 0, y: 7, w: 10, h: 5, minW: 6, minH: 4 }
    ],
    sm: [
      { i: 'device-count-1', x: 0, y: 0, w: 3, h: 3, minW: 2, minH: 2 },
      { i: 'agent-status-1', x: 3, y: 0, w: 3, h: 3, minW: 2, minH: 2 },
      { i: 'system-health-1', x: 0, y: 3, w: 3, h: 3, minW: 2, minH: 2 },
      { i: 'os-distribution-1', x: 3, y: 3, w: 3, h: 4, minW: 3, minH: 3 },
      { i: 'alerts-1', x: 0, y: 6, w: 6, h: 4, minW: 4, minH: 3 },
      { i: 'device-table-1', x: 0, y: 10, w: 6, h: 5, minW: 4, minH: 4 }
    ],
    xs: [
      { i: 'device-count-1', x: 0, y: 0, w: 2, h: 3, minW: 2, minH: 2 },
      { i: 'agent-status-1', x: 2, y: 0, w: 2, h: 3, minW: 2, minH: 2 },
      { i: 'system-health-1', x: 0, y: 3, w: 2, h: 3, minW: 2, minH: 2 },
      { i: 'os-distribution-1', x: 2, y: 3, w: 2, h: 4, minW: 2, minH: 3 },
      { i: 'alerts-1', x: 0, y: 6, w: 4, h: 4, minW: 3, minH: 3 },
      { i: 'device-table-1', x: 0, y: 10, w: 4, h: 5, minW: 3, minH: 4 }
    ],
    xxs: [
      { i: 'device-count-1', x: 0, y: 0, w: 2, h: 3, minW: 2, minH: 2 },
      { i: 'agent-status-1', x: 0, y: 3, w: 2, h: 3, minW: 2, minH: 2 },
      { i: 'system-health-1', x: 0, y: 6, w: 2, h: 3, minW: 2, minH: 2 },
      { i: 'os-distribution-1', x: 0, y: 9, w: 2, h: 4, minW: 2, minH: 3 },
      { i: 'alerts-1', x: 0, y: 13, w: 2, h: 4, minW: 2, minH: 3 },
      { i: 'device-table-1', x: 0, y: 17, w: 2, h: 5, minW: 2, minH: 4 }
    ]
  }
});

export const DashboardProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [layouts, setLayouts] = useState<DashboardLayout[]>([]);
  const [currentLayoutId, setCurrentLayoutId] = useState<string>('default');
  const [isEditing, setIsEditing] = useState(false);

  // Initialize with default layout
  useEffect(() => {
    const savedLayouts = localStorage.getItem('dashboardLayouts');
    if (savedLayouts) {
      try {
        const parsed = JSON.parse(savedLayouts);
        setLayouts(parsed);
      } catch (error) {
        console.error('Failed to parse saved layouts:', error);
        setLayouts([createDefaultLayout()]);
      }
    } else {
      setLayouts([createDefaultLayout()]);
    }
  }, []);

  const currentLayout = layouts.find(l => l.id === currentLayoutId) || createDefaultLayout();

  const switchLayout = (layoutId: string) => {
    setCurrentLayoutId(layoutId);
  };

  const updateLayout = (newLayouts: { [key: string]: Layout[] }) => {
    setLayouts(prev => prev.map(layout => 
      layout.id === currentLayoutId 
        ? { ...layout, layouts: newLayouts }
        : layout
    ));
  };

  const addWidget = (widget: DashboardWidget) => {
    const defaultSize = DEFAULT_WIDGET_SIZES[widget.type] || { w: 4, h: 3 };
    
    setLayouts(prev => prev.map(layout => {
      if (layout.id !== currentLayoutId) return layout;

      // Find next available position
      const existingLayouts = layout.layouts.lg || [];
      const maxY = existingLayouts.reduce((max, item) => Math.max(max, item.y + item.h), 0);

      const newLayoutItem: Layout = {
        i: widget.id,
        x: 0,
        y: maxY,
        w: defaultSize.w,
        h: defaultSize.h,
        minW: 2,
        minH: 2
      };

      return {
        ...layout,
        widgets: [...layout.widgets, widget],
        layouts: {
          ...layout.layouts,
          lg: [...existingLayouts, newLayoutItem],
          md: [...(layout.layouts.md || []), { ...newLayoutItem, w: Math.min(defaultSize.w, 8) }],
          sm: [...(layout.layouts.sm || []), { ...newLayoutItem, w: Math.min(defaultSize.w, 6) }],
          xs: [...(layout.layouts.xs || []), { ...newLayoutItem, w: Math.min(defaultSize.w, 4) }],
          xxs: [...(layout.layouts.xxs || []), { ...newLayoutItem, w: 2 }]
        }
      };
    }));
  };

  const removeWidget = (widgetId: string) => {
    setLayouts(prev => prev.map(layout => {
      if (layout.id !== currentLayoutId) return layout;

      return {
        ...layout,
        widgets: layout.widgets.filter(w => w.id !== widgetId),
        layouts: Object.fromEntries(
          Object.entries(layout.layouts).map(([breakpoint, items]) => [
            breakpoint,
            items.filter(item => item.i !== widgetId)
          ])
        )
      };
    }));
  };

  const updateWidget = (widgetId: string, updates: Partial<DashboardWidget>) => {
    setLayouts(prev => prev.map(layout => {
      if (layout.id !== currentLayoutId) return layout;

      return {
        ...layout,
        widgets: layout.widgets.map(w => 
          w.id === widgetId ? { ...w, ...updates } : w
        )
      };
    }));
  };

  const saveLayout = () => {
    localStorage.setItem('dashboardLayouts', JSON.stringify(layouts));
  };

  const resetLayout = () => {
    const defaultLayout = createDefaultLayout();
    setLayouts(prev => prev.map(layout => 
      layout.id === currentLayoutId ? defaultLayout : layout
    ));
  };

  return (
    <DashboardContext.Provider value={{
      currentLayout,
      layouts,
      isEditing,
      setIsEditing,
      switchLayout,
      updateLayout,
      addWidget,
      removeWidget,
      updateWidget,
      saveLayout,
      resetLayout
    }}>
      {children}
    </DashboardContext.Provider>
  );
};

export const useDashboard = () => {
  const context = useContext(DashboardContext);
  if (context === undefined) {
    throw new Error('useDashboard must be used within a DashboardProvider');
  }
  return context;
};
