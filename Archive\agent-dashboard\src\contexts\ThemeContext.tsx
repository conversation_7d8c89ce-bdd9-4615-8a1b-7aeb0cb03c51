import React, { createContext, useContext, useState, useEffect } from 'react';
import { lightTheme } from '../themes/lightTheme';
import { darkTheme } from '../themes/darkTheme';
import { Theme, ThemeContextType, CustomColors, AccentColor } from '../types/theme';

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Default color values for the color picker
const defaultLightColors: CustomColors = {
  primary: '#005499',
  background: '#FFFFFF',
  text: '#111827',
  header: '#FFFFFF',
  border: '#E5E7EB'
};

const defaultDarkColors: CustomColors = {
  primary: '#06b6d4', // cyan-500 - Vision UI primary
  background: '#0f172a', // slate-900 - Vision UI background
  text: '#e2e8f0', // slate-200 - Vision UI text
  header: '#1e293b', // slate-800 - Vision UI header
  border: '#475569' // slate-600 - Vision UI border
};

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [darkMode, setDarkMode] = useState(false);
  const [accentColor, setAccentColor] = useState<AccentColor>('blue');
  const [theme, setTheme] = useState<Theme>(lightTheme);

  // Color picker state
  const [selectedElement, setSelectedElement] = useState<keyof CustomColors>('primary');
  const [customLightColors, setCustomLightColors] = useState<CustomColors>(() => {
    const saved = localStorage.getItem('customLightColors');
    return saved ? JSON.parse(saved) : defaultLightColors;
  });

  const [customDarkColors, setCustomDarkColors] = useState<CustomColors>(() => {
    const saved = localStorage.getItem('customDarkColors');
    return saved ? JSON.parse(saved) : defaultDarkColors;
  });

  useEffect(() => {
    // Check for user preference or saved setting
    const savedDarkMode = localStorage.getItem('darkMode') === 'true';
    setDarkMode(savedDarkMode);

    // Load saved accent color
    const savedAccentColor = localStorage.getItem('accentColor') as AccentColor;
    if (savedAccentColor) {
      setAccentColor(savedAccentColor);
    }
  }, []);

  useEffect(() => {
    setTheme(darkMode ? darkTheme : lightTheme);
    localStorage.setItem('darkMode', String(darkMode));
  }, [darkMode]);

  useEffect(() => {
    localStorage.setItem('accentColor', accentColor);
  }, [accentColor]);

  const toggleDarkMode = () => {
    // Add a slight delay to ensure smooth transition
    document.documentElement.style.setProperty('--transition-duration', '300ms');
    setDarkMode(!darkMode);

    // Reset transition duration after theme change
    setTimeout(() => {
      document.documentElement.style.setProperty('--transition-duration', '200ms');
    }, 300);
  };

  const setThemeAccentColor = (color: AccentColor) => {
    setAccentColor(color);
  };

  // Color picker functions
  const handleColorChange = (color: string) => {
    const colors = darkMode ? customDarkColors : customLightColors;
    const setColors = darkMode ? setCustomDarkColors : setCustomLightColors;

    setColors(prev => ({
      ...prev,
      [selectedElement]: color
    }));

    // Apply the color change using CSS variables
    document.documentElement.style.setProperty(`--${selectedElement}-color`, color);
  };

  const resetToDefault = () => {
    const defaultColors = darkMode ? defaultDarkColors : defaultLightColors;

    // Reset CSS variables
    Object.entries(defaultColors).forEach(([key, value]) => {
      document.documentElement.style.setProperty(`--${key}-color`, value);
    });

    // Reset state
    if (darkMode) {
      setCustomDarkColors(defaultDarkColors);
      localStorage.removeItem('customDarkColors');
    } else {
      setCustomLightColors(defaultLightColors);
      localStorage.removeItem('customLightColors');
    }
  };

  const saveAsDefault = () => {
    if (darkMode) {
      localStorage.setItem('customDarkColors', JSON.stringify(customDarkColors));
    } else {
      localStorage.setItem('customLightColors', JSON.stringify(customLightColors));
    }
  };

  // Apply custom colors when theme changes
  useEffect(() => {
    const colors = darkMode ? customDarkColors : customLightColors;
    Object.entries(colors).forEach(([key, value]) => {
      document.documentElement.style.setProperty(`--${key}-color`, value);
    });
  }, [darkMode, customLightColors, customDarkColors]);

  return (
    <ThemeContext.Provider value={{
      theme,
      darkMode,
      toggleDarkMode,
      accentColor,
      setThemeAccentColor,
      customColors: darkMode ? customDarkColors : customLightColors,
      selectedElement,
      setSelectedElement,
      handleColorChange,
      resetToDefault,
      saveAsDefault
    }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
