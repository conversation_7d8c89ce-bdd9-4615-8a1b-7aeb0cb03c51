import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconRss.mjs
var IconRss = createReactComponent("outline", "rss", "IconRss", [["path", { "d": "M5 19m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0", "key": "svg-0" }], ["path", { "d": "M4 4a16 16 0 0 1 16 16", "key": "svg-1" }], ["path", { "d": "M4 11a9 9 0 0 1 9 9", "key": "svg-2" }]]);

export {
  IconRss
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconRss.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-PZXZHLSB.js.map
