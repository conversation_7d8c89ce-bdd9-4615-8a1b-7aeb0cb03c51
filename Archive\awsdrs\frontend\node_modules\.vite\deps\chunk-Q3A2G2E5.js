import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconBrandNytimes.mjs
var IconBrandNytimes = createReactComponent("outline", "brand-nytimes", "IconBrandNytimes", [["path", { "d": "M11.036 5.058a8 8 0 1 0 8.706 9.965", "key": "svg-0" }], ["path", { "d": "M12 21v-11l-7.5 4", "key": "svg-1" }], ["path", { "d": "M17.5 3a2.5 2.5 0 1 1 0 5l-11 -5a2.5 2.5 0 0 0 -.67 4.91", "key": "svg-2" }], ["path", { "d": "M9 12v8", "key": "svg-3" }], ["path", { "d": "M16 13h-.01", "key": "svg-4" }]]);

export {
  IconBrandNytimes
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconBrandNytimes.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-Q3A2G2E5.js.map
