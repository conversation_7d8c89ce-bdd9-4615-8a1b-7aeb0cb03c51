import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconSumOff.mjs
var IconSumOff = createReactComponent("outline", "sum-off", "IconSumOff", [["path", { "d": "M18 18a1 1 0 0 1 -1 1h-11l6 -7m-3 -7h8a1 1 0 0 1 1 1v2", "key": "svg-0" }], ["path", { "d": "M3 3l18 18", "key": "svg-1" }]]);

export {
  IconSumOff
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconSumOff.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-Q3GMWHXC.js.map
