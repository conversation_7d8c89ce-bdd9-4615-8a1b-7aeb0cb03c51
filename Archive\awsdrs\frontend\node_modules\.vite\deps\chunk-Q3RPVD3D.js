import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconSailboatOff.mjs
var IconSailboatOff = createReactComponent("outline", "sailboat-off", "IconSailboatOff", [["path", { "d": "M2 20a2.4 2.4 0 0 0 2 1a2.4 2.4 0 0 0 2 -1a2.4 2.4 0 0 1 2 -1a2.4 2.4 0 0 1 2 1a2.4 2.4 0 0 0 2 1a2.4 2.4 0 0 0 2 -1a2.4 2.4 0 0 1 2 -1a2.4 2.4 0 0 1 2 1a2.4 2.4 0 0 0 2 1a2.4 2.4 0 0 0 2 -1", "key": "svg-0" }], ["path", { "d": "M4 18l-1 -3h12m4 0h2l-.506 1.517", "key": "svg-1" }], ["path", { "d": "M11 11v1h1m4 0h2l-7 -9v4", "key": "svg-2" }], ["path", { "d": "M7.713 7.718l-1.713 4.282", "key": "svg-3" }], ["path", { "d": "M3 3l18 18", "key": "svg-4" }]]);

export {
  IconSailboatOff
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconSailboatOff.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-Q3RPVD3D.js.map
