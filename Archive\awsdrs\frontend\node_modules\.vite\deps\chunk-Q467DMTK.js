import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconCircleLetterH.mjs
var IconCircleLetterH = createReactComponent("outline", "circle-letter-h", "IconCircleLetterH", [["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-0" }], ["path", { "d": "M10 16v-8m4 0v8", "key": "svg-1" }], ["path", { "d": "M10 12h4", "key": "svg-2" }]]);

export {
  IconCircleLetterH
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconCircleLetterH.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-Q467DMTK.js.map
