import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconNumber37Small.mjs
var IconNumber37Small = createReactComponent("outline", "number-37-small", "IconNumber37Small", [["path", { "d": "M14 8h4l-2 8", "key": "svg-0" }], ["path", { "d": "M6 8h2.5a1.5 1.5 0 0 1 1.5 1.5v1a1.5 1.5 0 0 1 -1.5 1.5h-1.5h1.5a1.5 1.5 0 0 1 1.5 1.5v1a1.5 1.5 0 0 1 -1.5 1.5h-2.5", "key": "svg-1" }]]);

export {
  IconNumber37Small
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconNumber37Small.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-Q4D3C5YQ.js.map
