import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconEyeglass.mjs
var IconEyeglass = createReactComponent("outline", "eyeglass", "IconEyeglass", [["path", { "d": "M8 4h-2l-3 10", "key": "svg-0" }], ["path", { "d": "M16 4h2l3 10", "key": "svg-1" }], ["path", { "d": "M10 16l4 0", "key": "svg-2" }], ["path", { "d": "M21 16.5a3.5 3.5 0 0 1 -7 0v-2.5h7v2.5", "key": "svg-3" }], ["path", { "d": "M10 16.5a3.5 3.5 0 0 1 -7 0v-2.5h7v2.5", "key": "svg-4" }]]);

export {
  IconEyeglass
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconEyeglass.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-Q4NCHUSU.js.map
