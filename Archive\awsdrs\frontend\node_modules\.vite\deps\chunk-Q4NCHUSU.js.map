{"version": 3, "sources": ["../../@tabler/icons-react/src/icons/IconEyeglass.ts"], "sourcesContent": ["import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'eyeglass', 'IconEyeglass', [[\"path\",{\"d\":\"M8 4h-2l-3 10\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M16 4h2l3 10\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M10 16l4 0\",\"key\":\"svg-2\"}],[\"path\",{\"d\":\"M21 16.5a3.5 3.5 0 0 1 -7 0v-2.5h7v2.5\",\"key\":\"svg-3\"}],[\"path\",{\"d\":\"M10 16.5a3.5 3.5 0 0 1 -7 0v-2.5h7v2.5\",\"key\":\"svg-4\"}]]);"], "mappings": ";;;;;AACA,IAAA,eAAe,qBAAqB,WAAW,YAAY,gBAAgB,CAAC,CAAC,QAAO,EAAC,KAAI,iBAAgB,OAAM,QAAA,CAAQ,GAAE,CAAC,QAAO,EAAC,KAAI,gBAAe,OAAM,QAAQ,CAAA,GAAE,CAAC,QAAO,EAAC,KAAI,cAAa,OAAM,QAAO,CAAC,GAAE,CAAC,QAAO,EAAC,KAAI,0CAAyC,OAAM,QAAA,CAAQ,GAAE,CAAC,QAAO,EAAC,KAAI,0CAAyC,OAAM,QAAQ,CAAA,CAAC,CAAC;", "names": []}