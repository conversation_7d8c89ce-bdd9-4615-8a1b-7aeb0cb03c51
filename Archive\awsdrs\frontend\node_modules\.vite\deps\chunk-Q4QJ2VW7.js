import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconWashDryHang.mjs
var IconWashDryHang = createReactComponent("outline", "wash-dry-hang", "IconWashDryHang", [["path", { "d": "M3 3m0 3a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3z", "key": "svg-0" }], ["path", { "d": "M4 4.01c5.333 5.323 10.667 5.32 16 -.01", "key": "svg-1" }]]);

export {
  IconWashDryHang
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconWashDryHang.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-Q4QJ2VW7.js.map
