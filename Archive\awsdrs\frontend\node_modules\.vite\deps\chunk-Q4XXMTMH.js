import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconTimeline.mjs
var IconTimeline = createReactComponent("outline", "timeline", "IconTimeline", [["path", { "d": "M4 16l6 -7l5 5l5 -6", "key": "svg-0" }], ["path", { "d": "M15 14m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0", "key": "svg-1" }], ["path", { "d": "M10 9m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0", "key": "svg-2" }], ["path", { "d": "M4 16m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0", "key": "svg-3" }], ["path", { "d": "M20 8m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0", "key": "svg-4" }]]);

export {
  IconTimeline
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconTimeline.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-Q4XXMTMH.js.map
