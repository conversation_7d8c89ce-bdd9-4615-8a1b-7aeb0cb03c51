import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconBrandCodepen.mjs
var IconBrandCodepen = createReactComponent("outline", "brand-codepen", "IconBrandCodepen", [["path", { "d": "M3 15l9 6l9 -6l-9 -6l-9 6", "key": "svg-0" }], ["path", { "d": "M3 9l9 6l9 -6l-9 -6l-9 6", "key": "svg-1" }], ["path", { "d": "M3 9l0 6", "key": "svg-2" }], ["path", { "d": "M21 9l0 6", "key": "svg-3" }], ["path", { "d": "M12 3l0 6", "key": "svg-4" }], ["path", { "d": "M12 15l0 6", "key": "svg-5" }]]);

export {
  IconBrandCodepen
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconBrandCodepen.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-Q5VOH7HW.js.map
