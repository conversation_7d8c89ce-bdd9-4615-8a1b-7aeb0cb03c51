import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconBleachOff.mjs
var IconBleachOff = createReactComponent("outline", "bleach-off", "IconBleachOff", [["path", { "d": "M5 19h14m1.986 -1.977a2 2 0 0 0 -.146 -.773l-7.1 -12.25a2 2 0 0 0 -3.5 0l-.815 1.405m-1.488 2.568l-4.797 8.277a2 2 0 0 0 1.75 2.75", "key": "svg-0" }], ["path", { "d": "M3 3l18 18", "key": "svg-1" }]]);

export {
  IconBleachOff
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconBleachOff.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-Q5W3DRFP.js.map
