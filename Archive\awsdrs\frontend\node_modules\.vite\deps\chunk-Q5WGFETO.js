import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconFlag3Filled.mjs
var IconFlag3Filled = createReactComponent("filled", "flag-3-filled", "IconFlag3Filled", [["path", { "d": "M19 4c.852 0 1.297 .986 .783 1.623l-.076 .084l-3.792 3.793l3.792 3.793c.603 .602 .22 1.614 -.593 1.701l-.114 .006h-13v6a1 1 0 0 1 -.883 .993l-.117 .007a1 1 0 0 1 -.993 -.883l-.007 -.117v-16a1 1 0 0 1 .883 -.993l.117 -.007h14z", "key": "svg-0" }]]);

export {
  IconFlag3Filled
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconFlag3Filled.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-Q5WGFETO.js.map
