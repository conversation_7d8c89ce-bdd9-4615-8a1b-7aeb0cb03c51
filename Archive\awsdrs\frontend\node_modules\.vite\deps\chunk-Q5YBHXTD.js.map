{"version": 3, "sources": ["../../@tabler/icons-react/src/icons/IconAlignBoxTopCenterFilled.ts"], "sourcesContent": ["import createReactComponent from '../createReactComponent';\nexport default createReactComponent('filled', 'align-box-top-center-filled', 'IconAlignBoxTopCenterFilled', [[\"path\",{\"d\":\"M18.333 2c1.96 0 3.56 1.537 3.662 3.472l.005 .195v12.666c0 1.96 -1.537 3.56 -3.472 3.662l-.195 .005h-12.666a3.667 3.667 0 0 1 -3.662 -3.472l-.005 -.195v-12.666c0 -1.96 1.537 -3.56 3.472 -3.662l.195 -.005h12.666zm-6.333 3a1 1 0 0 0 -1 1v6l.007 .117a1 1 0 0 0 1.993 -.117v-6l-.007 -.117a1 1 0 0 0 -.993 -.883zm3 0a1 1 0 0 0 -1 1v4l.007 .117a1 1 0 0 0 1.993 -.117v-4l-.007 -.117a1 1 0 0 0 -.993 -.883zm-6 0a1 1 0 0 0 -1 1v2l.007 .117a1 1 0 0 0 1.993 -.117v-2l-.007 -.117a1 1 0 0 0 -.993 -.883z\",\"key\":\"svg-0\"}]]);"], "mappings": ";;;;;AACA,IAAe,8BAAA,qBAAqB,UAAU,+BAA+B,+BAA+B,CAAC,CAAC,QAAO,EAAC,KAAI,8eAA6e,OAAM,QAAO,CAAC,CAAC,CAAC;", "names": []}