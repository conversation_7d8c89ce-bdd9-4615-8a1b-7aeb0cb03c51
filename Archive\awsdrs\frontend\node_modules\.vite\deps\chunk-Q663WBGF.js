import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconPlaystationTriangle.mjs
var IconPlaystationTriangle = createReactComponent("outline", "playstation-triangle", "IconPlaystationTriangle", [["path", { "d": "M12 21a9 9 0 0 0 9 -9a9 9 0 0 0 -9 -9a9 9 0 0 0 -9 9a9 9 0 0 0 9 9z", "key": "svg-0" }], ["path", { "d": "M7.5 15h9l-4.5 -8z", "key": "svg-1" }]]);

export {
  IconPlaystationTriangle
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconPlaystationTriangle.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-Q663WBGF.js.map
