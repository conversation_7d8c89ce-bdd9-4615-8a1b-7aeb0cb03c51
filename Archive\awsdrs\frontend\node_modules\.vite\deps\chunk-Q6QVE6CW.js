import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconColumns3Filled.mjs
var IconColumns3Filled = createReactComponent("filled", "columns-3-filled", "IconColumns3Filled", [["path", { "d": "M4 2h2a1 1 0 0 1 1 1v18a1 1 0 0 1 -1 1h-2a2 2 0 0 1 -2 -2v-16a2 2 0 0 1 2 -2", "key": "svg-0" }], ["path", { "d": "M9 2m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v18a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z", "key": "svg-1" }], ["path", { "d": "M18 2h2a2 2 0 0 1 2 2v16a2 2 0 0 1 -2 2h-2a1 1 0 0 1 -1 -1v-18a1 1 0 0 1 1 -1", "key": "svg-2" }]]);

export {
  IconColumns3Filled
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconColumns3Filled.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-Q6QVE6CW.js.map
