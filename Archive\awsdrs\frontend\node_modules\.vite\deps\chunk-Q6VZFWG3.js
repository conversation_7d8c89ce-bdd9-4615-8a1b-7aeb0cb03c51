import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconBrightness.mjs
var IconBrightness = createReactComponent("outline", "brightness", "IconBrightness", [["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-0" }], ["path", { "d": "M12 3l0 18", "key": "svg-1" }], ["path", { "d": "M12 9l4.65 -4.65", "key": "svg-2" }], ["path", { "d": "M12 14.3l7.37 -7.37", "key": "svg-3" }], ["path", { "d": "M12 19.6l8.85 -8.85", "key": "svg-4" }]]);

export {
  IconBrightness
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconBrightness.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-Q6VZFWG3.js.map
