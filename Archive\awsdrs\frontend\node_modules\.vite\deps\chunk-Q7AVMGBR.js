import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconBrandMessenger.mjs
var IconBrandMessenger = createReactComponent("outline", "brand-messenger", "IconBrandMessenger", [["path", { "d": "M3 20l1.3 -3.9a9 8 0 1 1 3.4 2.9l-4.7 1", "key": "svg-0" }], ["path", { "d": "M8 13l3 -2l2 2l3 -2", "key": "svg-1" }]]);

export {
  IconBrandMessenger
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconBrandMessenger.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-Q7AVMGBR.js.map
