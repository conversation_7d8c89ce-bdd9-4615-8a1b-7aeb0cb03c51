import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconBrandVue.mjs
var IconBrandVue = createReactComponent("outline", "brand-vue", "IconBrandVue", [["path", { "d": "M16.5 4l-4.5 8l-4.5 -8", "key": "svg-0" }], ["path", { "d": "M3 4l9 16l9 -16", "key": "svg-1" }]]);

export {
  IconBrandVue
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconBrandVue.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-Q7QWSYLB.js.map
