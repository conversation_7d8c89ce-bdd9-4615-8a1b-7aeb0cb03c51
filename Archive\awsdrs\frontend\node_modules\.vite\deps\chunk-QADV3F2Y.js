import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconAntennaBars3.mjs
var IconAntennaBars3 = createReactComponent("outline", "antenna-bars-3", "IconAntennaBars3", [["path", { "d": "M6 18l0 -3", "key": "svg-0" }], ["path", { "d": "M10 18l0 -6", "key": "svg-1" }], ["path", { "d": "M14 18l0 .01", "key": "svg-2" }], ["path", { "d": "M18 18l0 .01", "key": "svg-3" }]]);

export {
  IconAntennaBars3
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconAntennaBars3.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-QADV3F2Y.js.map
