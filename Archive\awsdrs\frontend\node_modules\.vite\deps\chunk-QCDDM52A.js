import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconSquareRoundedLetterA.mjs
var IconSquareRoundedLetterA = createReactComponent("outline", "square-rounded-letter-a", "IconSquareRoundedLetterA", [["path", { "d": "M10 16v-6a2 2 0 1 1 4 0v6", "key": "svg-0" }], ["path", { "d": "M10 13h4", "key": "svg-1" }], ["path", { "d": "M12 3c7.2 0 9 1.8 9 9s-1.8 9 -9 9s-9 -1.8 -9 -9s1.8 -9 9 -9z", "key": "svg-2" }]]);

export {
  IconSquareRoundedLetterA
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconSquareRoundedLetterA.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-QCDDM52A.js.map
