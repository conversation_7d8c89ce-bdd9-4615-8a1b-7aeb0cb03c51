import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconArrowsDiagonal.mjs
var IconArrowsDiagonal = createReactComponent("outline", "arrows-diagonal", "IconArrowsDiagonal", [["path", { "d": "M16 4l4 0l0 4", "key": "svg-0" }], ["path", { "d": "M14 10l6 -6", "key": "svg-1" }], ["path", { "d": "M8 20l-4 0l0 -4", "key": "svg-2" }], ["path", { "d": "M4 20l6 -6", "key": "svg-3" }]]);

export {
  IconArrowsDiagonal
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconArrowsDiagonal.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-QCI5AHKF.js.map
