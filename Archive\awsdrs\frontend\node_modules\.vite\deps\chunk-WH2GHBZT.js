import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconParentheses.mjs
var IconParentheses = createReactComponent("outline", "parentheses", "IconParentheses", [["path", { "d": "M7 4a12.25 12.25 0 0 0 0 16", "key": "svg-0" }], ["path", { "d": "M17 4a12.25 12.25 0 0 1 0 16", "key": "svg-1" }]]);

export {
  IconParentheses
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconParentheses.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WH2GHBZT.js.map
