import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconDropletBolt.mjs
var IconDropletBolt = createReactComponent("outline", "droplet-bolt", "IconDropletBolt", [["path", { "d": "M18.628 12.076a6.653 6.653 0 0 0 -.564 -1.199l-4.89 -7.26c-.42 -.625 -1.287 -.803 -1.936 -.397a1.376 1.376 0 0 0 -.41 .397l-4.893 7.26c-1.695 2.838 -1.035 6.441 1.567 8.546c1.7 1.375 3.906 1.852 5.958 1.431", "key": "svg-0" }], ["path", { "d": "M19 16l-2 3h4l-2 3", "key": "svg-1" }]]);

export {
  IconDropletBolt
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconDropletBolt.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WHFAORAH.js.map
