import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconMobiledata.mjs
var IconMobiledata = createReactComponent("outline", "mobiledata", "IconMobiledata", [["path", { "d": "M16 12v-8", "key": "svg-0" }], ["path", { "d": "M8 20v-8", "key": "svg-1" }], ["path", { "d": "M13 7l3 -3l3 3", "key": "svg-2" }], ["path", { "d": "M5 17l3 3l3 -3", "key": "svg-3" }]]);

export {
  IconMobiledata
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconMobiledata.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WHGCBFBW.js.map
