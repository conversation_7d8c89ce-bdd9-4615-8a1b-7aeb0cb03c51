import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconReceiptRefund.mjs
var IconReceiptRefund = createReactComponent("outline", "receipt-refund", "IconReceiptRefund", [["path", { "d": "M5 21v-16a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v16l-3 -2l-2 2l-2 -2l-2 2l-2 -2l-3 2", "key": "svg-0" }], ["path", { "d": "M15 14v-2a2 2 0 0 0 -2 -2h-4l2 -2m0 4l-2 -2", "key": "svg-1" }]]);

export {
  IconReceiptRefund
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconReceiptRefund.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WHGWIRUA.js.map
