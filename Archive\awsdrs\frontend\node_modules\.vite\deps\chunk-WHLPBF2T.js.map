{"version": 3, "sources": ["../../@tabler/icons-react/src/icons/IconBuildingEstate.ts"], "sourcesContent": ["import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'building-estate', 'IconBuildingEstate', [[\"path\",{\"d\":\"M3 21h18\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M19 21v-4\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M19 17a2 2 0 0 0 2 -2v-2a2 2 0 1 0 -4 0v2a2 2 0 0 0 2 2z\",\"key\":\"svg-2\"}],[\"path\",{\"d\":\"M14 21v-14a3 3 0 0 0 -3 -3h-4a3 3 0 0 0 -3 3v14\",\"key\":\"svg-3\"}],[\"path\",{\"d\":\"M9 17v4\",\"key\":\"svg-4\"}],[\"path\",{\"d\":\"M8 13h2\",\"key\":\"svg-5\"}],[\"path\",{\"d\":\"M8 9h2\",\"key\":\"svg-6\"}]]);"], "mappings": ";;;;;AACA,IAAe,qBAAA,qBAAqB,WAAW,mBAAmB,sBAAsB,CAAC,CAAC,QAAO,EAAC,KAAI,YAAW,OAAM,QAAO,CAAC,GAAE,CAAC,QAAO,EAAC,KAAI,aAAY,OAAM,QAAO,CAAC,GAAE,CAAC,QAAO,EAAC,KAAI,4DAA2D,OAAM,QAAO,CAAC,GAAE,CAAC,QAAO,EAAC,KAAI,mDAAkD,OAAM,QAAO,CAAC,GAAE,CAAC,QAAO,EAAC,KAAI,WAAU,OAAM,QAAO,CAAC,GAAE,CAAC,QAAO,EAAC,KAAI,WAAU,OAAM,QAAO,CAAC,GAAE,CAAC,QAAO,EAAC,KAAI,UAAS,OAAM,QAAO,CAAC,CAAC,CAAC;", "names": []}