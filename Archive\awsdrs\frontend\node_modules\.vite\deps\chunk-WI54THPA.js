import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconChevronsRight.mjs
var IconChevronsRight = createReactComponent("outline", "chevrons-right", "IconChevronsRight", [["path", { "d": "M7 7l5 5l-5 5", "key": "svg-0" }], ["path", { "d": "M13 7l5 5l-5 5", "key": "svg-1" }]]);

export {
  IconChevronsRight
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconChevronsRight.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WI54THPA.js.map
