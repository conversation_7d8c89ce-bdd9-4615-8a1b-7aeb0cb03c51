import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconZodiacTaurus.mjs
var IconZodiacTaurus = createReactComponent("outline", "zodiac-taurus", "IconZodiacTaurus", [["path", { "d": "M6 3a6 6 0 0 0 12 0", "key": "svg-0" }], ["path", { "d": "M12 15m-6 0a6 6 0 1 0 12 0a6 6 0 1 0 -12 0", "key": "svg-1" }]]);

export {
  IconZodiacTaurus
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconZodiacTaurus.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WI6OG2HQ.js.map
