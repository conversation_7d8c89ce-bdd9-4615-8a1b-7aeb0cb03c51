import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconArrowBadgeRightFilled.mjs
var IconArrowBadgeRightFilled = createReactComponent("filled", "arrow-badge-right-filled", "IconArrowBadgeRightFilled", [["path", { "d": "M7 6l-.112 .006a1 1 0 0 0 -.669 1.619l3.501 4.375l-3.5 4.375a1 1 0 0 0 .78 1.625h6a1 1 0 0 0 .78 -.375l4 -5a1 1 0 0 0 0 -1.25l-4 -5a1 1 0 0 0 -.78 -.375h-6z", "key": "svg-0" }]]);

export {
  IconArrowBadgeRightFilled
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconArrowBadgeRightFilled.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WIBHMDOX.js.map
