import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconRewindForward40.mjs
var IconRewindForward40 = createReactComponent("outline", "rewind-forward-40", "IconRewindForward40", [["path", { "d": "M5.007 16.478a6 6 0 0 1 3.993 -10.478h11", "key": "svg-0" }], ["path", { "d": "M15 15.5v3a1.5 1.5 0 0 0 3 0v-3a1.5 1.5 0 0 0 -3 0z", "key": "svg-1" }], ["path", { "d": "M17 9l3 -3l-3 -3", "key": "svg-2" }], ["path", { "d": "M9 14v2a1 1 0 0 0 1 1h1", "key": "svg-3" }], ["path", { "d": "M12 14v6", "key": "svg-4" }]]);

export {
  IconRewindForward40
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconRewindForward40.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WIYEMLB4.js.map
