import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconCircleLetterV.mjs
var IconCircleLetterV = createReactComponent("outline", "circle-letter-v", "IconCircleLetterV", [["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-0" }], ["path", { "d": "M10 8l2 8l2 -8", "key": "svg-1" }]]);

export {
  IconCircleLetterV
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconCircleLetterV.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WJGQUGHE.js.map
