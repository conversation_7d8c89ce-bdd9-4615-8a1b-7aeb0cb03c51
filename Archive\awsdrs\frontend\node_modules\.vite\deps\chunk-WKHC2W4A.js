import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconArrowMoveDown.mjs
var IconArrowMoveDown = createReactComponent("outline", "arrow-move-down", "IconArrowMoveDown", [["path", { "d": "M12 11v10", "key": "svg-0" }], ["path", { "d": "M9 18l3 3l3 -3", "key": "svg-1" }], ["path", { "d": "M12 5m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-2" }]]);

export {
  IconArrowMoveDown
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconArrowMoveDown.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WKHC2W4A.js.map
