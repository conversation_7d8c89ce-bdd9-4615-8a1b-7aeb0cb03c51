import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconShareplay.mjs
var IconShareplay = createReactComponent("outline", "shareplay", "IconShareplay", [["path", { "d": "M18 18a3 3 0 0 0 3 -3v-8a3 3 0 0 0 -3 -3h-12a3 3 0 0 0 -3 3v8a3 3 0 0 0 3 3", "key": "svg-0" }], ["path", { "d": "M9 20h6l-3 -5z", "key": "svg-1" }]]);

export {
  IconShareplay
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconShareplay.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WL66MF5F.js.map
