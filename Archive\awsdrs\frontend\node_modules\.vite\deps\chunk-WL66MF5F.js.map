{"version": 3, "sources": ["../../@tabler/icons-react/src/icons/IconShareplay.ts"], "sourcesContent": ["import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'shareplay', 'IconShareplay', [[\"path\",{\"d\":\"M18 18a3 3 0 0 0 3 -3v-8a3 3 0 0 0 -3 -3h-12a3 3 0 0 0 -3 3v8a3 3 0 0 0 3 3\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M9 20h6l-3 -5z\",\"key\":\"svg-1\"}]]);"], "mappings": ";;;;;AACA,IAAA,gBAAe,qBAAqB,WAAW,aAAa,iBAAiB,CAAC,CAAC,QAAO,EAAC,KAAI,+EAA8E,OAAM,QAAA,CAAQ,GAAE,CAAC,QAAO,EAAC,KAAI,kBAAiB,OAAM,QAAQ,CAAA,CAAC,CAAC;", "names": []}