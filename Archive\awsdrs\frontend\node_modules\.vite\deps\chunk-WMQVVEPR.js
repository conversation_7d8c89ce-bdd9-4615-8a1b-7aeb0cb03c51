import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconHtml.mjs
var IconHtml = createReactComponent("outline", "html", "IconHtml", [["path", { "d": "M13 16v-8l2 5l2 -5v8", "key": "svg-0" }], ["path", { "d": "M1 16v-8", "key": "svg-1" }], ["path", { "d": "M5 8v8", "key": "svg-2" }], ["path", { "d": "M1 12h4", "key": "svg-3" }], ["path", { "d": "M7 8h4", "key": "svg-4" }], ["path", { "d": "M9 8v8", "key": "svg-5" }], ["path", { "d": "M20 8v8h3", "key": "svg-6" }]]);

export {
  IconHtml
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconHtml.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WMQVVEPR.js.map
