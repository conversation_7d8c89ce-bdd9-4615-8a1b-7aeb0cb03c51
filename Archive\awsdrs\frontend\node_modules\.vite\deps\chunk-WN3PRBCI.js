import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconArrowsMoveVertical.mjs
var IconArrowsMoveVertical = createReactComponent("outline", "arrows-move-vertical", "IconArrowsMoveVertical", [["path", { "d": "M9 18l3 3l3 -3", "key": "svg-0" }], ["path", { "d": "M12 15v6", "key": "svg-1" }], ["path", { "d": "M15 6l-3 -3l-3 3", "key": "svg-2" }], ["path", { "d": "M12 3v6", "key": "svg-3" }]]);

export {
  IconArrowsMoveVertical
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconArrowsMoveVertical.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WN3PRBCI.js.map
