import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconEraser.mjs
var IconEraser = createReactComponent("outline", "eraser", "IconEraser", [["path", { "d": "M19 20h-10.5l-4.21 -4.3a1 1 0 0 1 0 -1.41l10 -10a1 1 0 0 1 1.41 0l5 5a1 1 0 0 1 0 1.41l-9.2 9.3", "key": "svg-0" }], ["path", { "d": "M18 13.3l-6.3 -6.3", "key": "svg-1" }]]);

export {
  IconEraser
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconEraser.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WNCJAHN2.js.map
