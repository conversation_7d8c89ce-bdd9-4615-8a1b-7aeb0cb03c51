import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconUserHexagon.mjs
var IconUserHexagon = createReactComponent("outline", "user-hexagon", "IconUserHexagon", [["path", { "d": "M12 13a3 3 0 1 0 0 -6a3 3 0 0 0 0 6z", "key": "svg-0" }], ["path", { "d": "M6.201 18.744a4 4 0 0 1 3.799 -2.744h4a4 4 0 0 1 3.798 2.741", "key": "svg-1" }], ["path", { "d": "M19.875 6.27c.7 .398 1.13 1.143 1.125 1.948v7.284c0 .809 -.443 1.555 -1.158 1.948l-6.75 4.27a2.269 2.269 0 0 1 -2.184 0l-6.75 -4.27a2.225 2.225 0 0 1 -1.158 -1.948v-7.285c0 -.809 .443 -1.554 1.158 -1.947l6.75 -3.98a2.33 2.33 0 0 1 2.25 0l6.75 3.98h-.033z", "key": "svg-2" }]]);

export {
  IconUserHexagon
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconUserHexagon.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WNDJ5GET.js.map
