import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconFileTypeTs.mjs
var IconFileTypeTs = createReactComponent("outline", "file-type-ts", "IconFileTypeTs", [["path", { "d": "M14 3v4a1 1 0 0 0 1 1h4", "key": "svg-0" }], ["path", { "d": "M5 12v-7a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2h-1", "key": "svg-1" }], ["path", { "d": "M14 3v4a1 1 0 0 0 1 1h4", "key": "svg-2" }], ["path", { "d": "M9 20.25c0 .414 .336 .75 .75 .75h1.25a1 1 0 0 0 1 -1v-1a1 1 0 0 0 -1 -1h-1a1 1 0 0 1 -1 -1v-1a1 1 0 0 1 1 -1h1.25a.75 .75 0 0 1 .75 .75", "key": "svg-3" }], ["path", { "d": "M3.5 15h3", "key": "svg-4" }], ["path", { "d": "M5 15v6", "key": "svg-5" }]]);

export {
  IconFileTypeTs
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconFileTypeTs.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WNKGVARD.js.map
