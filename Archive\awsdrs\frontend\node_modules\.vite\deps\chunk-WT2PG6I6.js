import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconHttpConnect.mjs
var IconHttpConnect = createReactComponent("outline", "http-connect", "IconHttpConnect", [["path", { "d": "M7 10a2 2 0 1 0 -4 0v4a2 2 0 1 0 4 0", "key": "svg-0" }], ["path", { "d": "M17 16v-8l4 8v-8", "key": "svg-1" }], ["path", { "d": "M12 8a2 2 0 0 1 2 2v4a2 2 0 1 1 -4 0v-4a2 2 0 0 1 2 -2", "key": "svg-2" }]]);

export {
  IconHttpConnect
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconHttpConnect.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WT2PG6I6.js.map
