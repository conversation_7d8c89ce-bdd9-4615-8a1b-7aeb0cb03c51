import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconBarbellFilled.mjs
var IconBarbellFilled = createReactComponent("filled", "barbell-filled", "IconBarbellFilled", [["path", { "d": "M4 7a1 1 0 0 1 1 1v8a1 1 0 0 1 -2 0v-3h-1a1 1 0 0 1 0 -2h1v-3a1 1 0 0 1 1 -1", "key": "svg-0" }], ["path", { "d": "M20 7a1 1 0 0 1 1 1v3h1a1 1 0 0 1 0 2h-1v3a1 1 0 0 1 -2 0v-8a1 1 0 0 1 1 -1", "key": "svg-1" }], ["path", { "d": "M16 5a2 2 0 0 1 2 2v10a2 2 0 1 1 -4 0v-4h-4v4a2 2 0 1 1 -4 0v-10a2 2 0 1 1 4 0v4h4v-4a2 2 0 0 1 2 -2", "key": "svg-2" }]]);

export {
  IconBarbellFilled
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconBarbellFilled.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WTPG2S44.js.map
