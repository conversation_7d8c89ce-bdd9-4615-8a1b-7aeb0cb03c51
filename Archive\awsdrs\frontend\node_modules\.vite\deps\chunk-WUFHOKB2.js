import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconFlag.mjs
var IconFlag = createReactComponent("outline", "flag", "IconFlag", [["path", { "d": "M5 5a5 5 0 0 1 7 0a5 5 0 0 0 7 0v9a5 5 0 0 1 -7 0a5 5 0 0 0 -7 0v-9z", "key": "svg-0" }], ["path", { "d": "M5 21v-7", "key": "svg-1" }]]);

export {
  IconFlag
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconFlag.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WUFHOKB2.js.map
