import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconSort09.mjs
var IconSort09 = createReactComponent("outline", "sort-0-9", "IconSort09", [["path", { "d": "M11 12h2", "key": "svg-0" }], ["path", { "d": "M4 10v4a2 2 0 1 0 4 0v-4a2 2 0 1 0 -4 0z", "key": "svg-1" }], ["path", { "d": "M16 15a1 1 0 0 0 1 1h2a1 1 0 0 0 1 -1v-6a1 1 0 0 0 -1 -1h-2a1 1 0 0 0 -1 1v2a1 1 0 0 0 1 1h3", "key": "svg-2" }]]);

export {
  IconSort09
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconSort09.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WUPIFTUU.js.map
