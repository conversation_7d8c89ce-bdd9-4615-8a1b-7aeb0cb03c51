import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconPointerUp.mjs
var IconPointerUp = createReactComponent("outline", "pointer-up", "IconPointerUp", [["path", { "d": "M15.984 13.428l-1.206 -1.206l3.113 -2.09a1.2 1.2 0 0 0 -.309 -2.228l-13.582 -3.904l3.904 13.563a1.2 1.2 0 0 0 2.228 .308l2.09 -3.093l1.217 1.217", "key": "svg-0" }], ["path", { "d": "M19 22v-6", "key": "svg-1" }], ["path", { "d": "M22 19l-3 -3l-3 3", "key": "svg-2" }]]);

export {
  IconPointerUp
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconPointerUp.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WUSBQSMZ.js.map
