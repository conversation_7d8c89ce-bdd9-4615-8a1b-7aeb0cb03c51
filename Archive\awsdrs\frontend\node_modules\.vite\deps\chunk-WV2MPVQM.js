import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconInnerShadowTopLeft.mjs
var IconInnerShadowTopLeft = createReactComponent("outline", "inner-shadow-top-left", "IconInnerShadowTopLeft", [["path", { "d": "M12 3a9 9 0 1 1 0 18a9 9 0 0 1 0 -18z", "key": "svg-0" }], ["path", { "d": "M6 12a6 6 0 0 1 6 -6", "key": "svg-1" }]]);

export {
  IconInnerShadowTopLeft
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconInnerShadowTopLeft.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WV2MPVQM.js.map
