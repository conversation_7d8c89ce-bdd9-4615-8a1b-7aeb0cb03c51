import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconZip.mjs
var IconZip = createReactComponent("outline", "zip", "IconZip", [["path", { "d": "M16 16v-8h2a2 2 0 1 1 0 4h-2", "key": "svg-0" }], ["path", { "d": "M12 8v8", "key": "svg-1" }], ["path", { "d": "M4 8h4l-4 8h4", "key": "svg-2" }]]);

export {
  IconZip
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconZip.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WVF5KHEA.js.map
