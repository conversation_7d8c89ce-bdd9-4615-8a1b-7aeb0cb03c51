import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconLetterWSmall.mjs
var IconLetterWSmall = createReactComponent("outline", "letter-w-small", "IconLetterWSmall", [["path", { "d": "M9 8l1 8l2 -5l2 5l1 -8", "key": "svg-0" }]]);

export {
  IconLetterWSmall
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconLetterWSmall.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WVGADVZZ.js.map
