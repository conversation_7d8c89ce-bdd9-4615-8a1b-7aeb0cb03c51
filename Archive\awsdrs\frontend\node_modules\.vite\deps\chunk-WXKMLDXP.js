import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconMessageReport.mjs
var IconMessageReport = createReactComponent("outline", "message-report", "IconMessageReport", [["path", { "d": "M18 4a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-5l-5 3v-3h-2a3 3 0 0 1 -3 -3v-8a3 3 0 0 1 3 -3h12z", "key": "svg-0" }], ["path", { "d": "M12 8v3", "key": "svg-1" }], ["path", { "d": "M12 14v.01", "key": "svg-2" }]]);

export {
  IconMessageReport
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconMessageReport.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WXKMLDXP.js.map
