import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconArrowBadgeDown.mjs
var IconArrowBadgeDown = createReactComponent("outline", "arrow-badge-down", "IconArrowBadgeDown", [["path", { "d": "M17 13v-6l-5 4l-5 -4v6l5 4z", "key": "svg-0" }]]);

export {
  IconArrowBadgeDown
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconArrowBadgeDown.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WXKREPYX.js.map
