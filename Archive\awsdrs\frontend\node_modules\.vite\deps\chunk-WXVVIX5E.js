import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconBasketStar.mjs
var IconBasketStar = createReactComponent("outline", "basket-star", "IconBasketStar", [["path", { "d": "M17 10l-2 -6", "key": "svg-0" }], ["path", { "d": "M7 10l2 -6", "key": "svg-1" }], ["path", { "d": "M10.5 20h-3.256a3 3 0 0 1 -2.965 -2.544l-1.255 -7.152a2 2 0 0 1 1.977 -2.304h13.999a2 2 0 0 1 1.977 2.304l-.133 .757", "key": "svg-2" }], ["path", { "d": "M13.596 12.794a2 2 0 0 0 -3.377 2.116", "key": "svg-3" }], ["path", { "d": "M17.8 20.817l-2.172 1.138a.392 .392 0 0 1 -.568 -.41l.415 -2.411l-1.757 -1.707a.389 .389 0 0 1 .217 -.665l2.428 -.352l1.086 -2.193a.392 .392 0 0 1 .702 0l1.086 2.193l2.428 .352a.39 .39 0 0 1 .217 .665l-1.757 1.707l.414 2.41a.39 .39 0 0 1 -.567 .411l-2.172 -1.138z", "key": "svg-4" }]]);

export {
  IconBasketStar
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconBasketStar.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WXVVIX5E.js.map
