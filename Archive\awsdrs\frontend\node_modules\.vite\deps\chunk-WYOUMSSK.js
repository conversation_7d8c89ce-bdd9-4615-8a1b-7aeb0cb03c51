import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconArrowLeftRight.mjs
var IconArrowLeftRight = createReactComponent("outline", "arrow-left-right", "IconArrowLeftRight", [["path", { "d": "M17 13l4 -4l-4 -4", "key": "svg-0" }], ["path", { "d": "M7 13l-4 -4l4 -4", "key": "svg-1" }], ["path", { "d": "M12 14a5 5 0 0 1 5 -5h4", "key": "svg-2" }], ["path", { "d": "M12 19v-5a5 5 0 0 0 -5 -5h-4", "key": "svg-3" }]]);

export {
  IconArrowLeftRight
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconArrowLeftRight.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WYOUMSSK.js.map
