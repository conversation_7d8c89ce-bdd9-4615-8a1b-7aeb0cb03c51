import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconConfucius.mjs
var IconConfucius = createReactComponent("outline", "confucius", "IconConfucius", [["path", { "d": "M9 19l3 2v-18", "key": "svg-0" }], ["path", { "d": "M4 10l8 -2", "key": "svg-1" }], ["path", { "d": "M4 18l8 -10", "key": "svg-2" }], ["path", { "d": "M20 18l-8 -8l8 -4", "key": "svg-3" }]]);

export {
  IconConfucius
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconConfucius.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WZAQUCRG.js.map
