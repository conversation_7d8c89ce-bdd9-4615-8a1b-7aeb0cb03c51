import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconArrowBadgeLeft.mjs
var IconArrowBadgeLeft = createReactComponent("outline", "arrow-badge-left", "IconArrowBadgeLeft", [["path", { "d": "M11 17h6l-4 -5l4 -5h-6l-4 5z", "key": "svg-0" }]]);

export {
  IconArrowBadgeLeft
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconArrowBadgeLeft.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-X24Y742W.js.map
