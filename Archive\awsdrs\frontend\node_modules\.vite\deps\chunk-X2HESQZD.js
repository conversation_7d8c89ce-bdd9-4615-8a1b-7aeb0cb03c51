import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconPin.mjs
var IconPin = createReactComponent("outline", "pin", "IconPin", [["path", { "d": "M15 4.5l-4 4l-4 1.5l-1.5 1.5l7 7l1.5 -1.5l1.5 -4l4 -4", "key": "svg-0" }], ["path", { "d": "M9 15l-4.5 4.5", "key": "svg-1" }], ["path", { "d": "M14.5 4l5.5 5.5", "key": "svg-2" }]]);

export {
  IconPin
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconPin.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-X2HESQZD.js.map
