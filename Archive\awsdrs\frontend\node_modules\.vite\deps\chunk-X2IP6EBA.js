import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconEaseInOutControlPoints.mjs
var IconEaseInOutControlPoints = createReactComponent("outline", "ease-in-out-control-points", "IconEaseInOutControlPoints", [["path", { "d": "M17 20a2 2 0 1 0 4 0a2 2 0 0 0 -4 0z", "key": "svg-0" }], ["path", { "d": "M17 20h-2", "key": "svg-1" }], ["path", { "d": "M7 4a2 2 0 1 1 -4 0a2 2 0 0 1 4 0z", "key": "svg-2" }], ["path", { "d": "M7 4h2", "key": "svg-3" }], ["path", { "d": "M14 4h-2", "key": "svg-4" }], ["path", { "d": "M12 20h-2", "key": "svg-5" }], ["path", { "d": "M3 20c8 0 10 -16 18 -16", "key": "svg-6" }]]);

export {
  IconEaseInOutControlPoints
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconEaseInOutControlPoints.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-X2IP6EBA.js.map
