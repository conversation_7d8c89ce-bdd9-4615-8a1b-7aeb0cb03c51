import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconFilter2Up.mjs
var IconFilter2Up = createReactComponent("outline", "filter-2-up", "IconFilter2Up", [["path", { "d": "M4 6h16", "key": "svg-0" }], ["path", { "d": "M6 12h12", "key": "svg-1" }], ["path", { "d": "M9 18h3", "key": "svg-2" }], ["path", { "d": "M19 22v-6m0 0l3 3m-3 -3l-3 3", "key": "svg-3" }]]);

export {
  IconFilter2Up
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconFilter2Up.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-X3C4WUQX.js.map
