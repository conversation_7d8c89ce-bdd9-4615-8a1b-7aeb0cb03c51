import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconCircuitChangeover.mjs
var IconCircuitChangeover = createReactComponent("outline", "circuit-changeover", "IconCircuitChangeover", [["path", { "d": "M2 12h2", "key": "svg-0" }], ["path", { "d": "M20 7h2", "key": "svg-1" }], ["path", { "d": "M6 12m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-2" }], ["path", { "d": "M18 7m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-3" }], ["path", { "d": "M20 17h2", "key": "svg-4" }], ["path", { "d": "M18 17m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-5" }], ["path", { "d": "M7.5 10.5l8.5 -3.5", "key": "svg-6" }]]);

export {
  IconCircuitChangeover
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconCircuitChangeover.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-X3GHFRM5.js.map
