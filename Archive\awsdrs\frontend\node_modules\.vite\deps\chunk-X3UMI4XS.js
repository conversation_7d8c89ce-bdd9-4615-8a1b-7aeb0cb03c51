import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconChevronCompactRight.mjs
var IconChevronCompactRight = createReactComponent("outline", "chevron-compact-right", "IconChevronCompactRight", [["path", { "d": "M11 4l3 8l-3 8", "key": "svg-0" }]]);

export {
  IconChevronCompactRight
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconChevronCompactRight.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-X3UMI4XS.js.map
