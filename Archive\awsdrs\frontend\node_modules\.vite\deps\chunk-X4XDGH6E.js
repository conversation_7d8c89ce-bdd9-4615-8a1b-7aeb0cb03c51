import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconSlashes.mjs
var IconSlashes = createReactComponent("outline", "slashes", "IconSlashes", [["path", { "d": "M14 5l-10 14", "key": "svg-0" }], ["path", { "d": "M20 5l-10 14", "key": "svg-1" }]]);

export {
  IconSlashes
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconSlashes.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-X4XDGH6E.js.map
