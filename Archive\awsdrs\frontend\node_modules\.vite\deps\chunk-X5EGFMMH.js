import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconNumber76Small.mjs
var IconNumber76Small = createReactComponent("outline", "number-76-small", "IconNumber76Small", [["path", { "d": "M18 9a1 1 0 0 0 -1 -1h-2a1 1 0 0 0 -1 1v6a1 1 0 0 0 1 1h2a1 1 0 0 0 1 -1v-2a1 1 0 0 0 -1 -1h-3", "key": "svg-0" }], ["path", { "d": "M6 8h4l-2 8", "key": "svg-1" }]]);

export {
  IconNumber76Small
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconNumber76Small.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-X5EGFMMH.js.map
