import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconMailbox.mjs
var IconMailbox = createReactComponent("outline", "mailbox", "IconMailbox", [["path", { "d": "M10 21v-6.5a3.5 3.5 0 0 0 -7 0v6.5h18v-6a4 4 0 0 0 -4 -4h-10.5", "key": "svg-0" }], ["path", { "d": "M12 11v-8h4l2 2l-2 2h-4", "key": "svg-1" }], ["path", { "d": "M6 15h1", "key": "svg-2" }]]);

export {
  IconMailbox
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconMailbox.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-X5QXKVDB.js.map
