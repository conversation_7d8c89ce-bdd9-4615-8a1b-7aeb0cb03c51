import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconArrowDownFromArc.mjs
var IconArrowDownFromArc = createReactComponent("outline", "arrow-down-from-arc", "IconArrowDownFromArc", [["path", { "d": "M12 15v-12", "key": "svg-0" }], ["path", { "d": "M16 7l-4 -4l-4 4", "key": "svg-1" }], ["path", { "d": "M3 12a9 9 0 0 0 18 0", "key": "svg-2" }]]);

export {
  IconArrowDownFromArc
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconArrowDownFromArc.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-X5XPJJYX.js.map
