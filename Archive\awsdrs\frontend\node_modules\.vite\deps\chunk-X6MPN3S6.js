import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconSquareRoundedArrowRight.mjs
var IconSquareRoundedArrowRight = createReactComponent("outline", "square-rounded-arrow-right", "IconSquareRoundedArrowRight", [["path", { "d": "M12 16l4 -4l-4 -4", "key": "svg-0" }], ["path", { "d": "M8 12h8", "key": "svg-1" }], ["path", { "d": "M12 3c7.2 0 9 1.8 9 9s-1.8 9 -9 9s-9 -1.8 -9 -9s1.8 -9 9 -9z", "key": "svg-2" }]]);

export {
  IconSquareRoundedArrowRight
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconSquareRoundedArrowRight.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-X6MPN3S6.js.map
