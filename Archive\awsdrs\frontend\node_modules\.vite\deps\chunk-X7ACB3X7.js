import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconHeartBroken.mjs
var IconHeartBroken = createReactComponent("outline", "heart-broken", "IconHeartBroken", [["path", { "d": "M19.5 12.572l-7.5 7.428l-7.5 -7.428a5 5 0 1 1 7.5 -6.566a5 5 0 1 1 7.5 6.572", "key": "svg-0" }], ["path", { "d": "M12 6l-2 4l4 3l-2 4v3", "key": "svg-1" }]]);

export {
  IconHeartBroken
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconHeartBroken.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-X7ACB3X7.js.map
