import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconSquareLetterZ.mjs
var IconSquareLetterZ = createReactComponent("outline", "square-letter-z", "IconSquareLetterZ", [["path", { "d": "M3 3m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z", "key": "svg-0" }], ["path", { "d": "M10 8h4l-4 8h4", "key": "svg-1" }]]);

export {
  IconSquareLetterZ
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconSquareLetterZ.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XASD7MGB.js.map
