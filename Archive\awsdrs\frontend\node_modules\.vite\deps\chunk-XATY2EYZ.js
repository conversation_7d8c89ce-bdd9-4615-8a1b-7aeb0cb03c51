import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconDeviceTabletExclamation.mjs
var IconDeviceTabletExclamation = createReactComponent("outline", "device-tablet-exclamation", "IconDeviceTabletExclamation", [["path", { "d": "M15 21h-9a1 1 0 0 1 -1 -1v-16a1 1 0 0 1 1 -1h12a1 1 0 0 1 1 1v8", "key": "svg-0" }], ["path", { "d": "M11 17a1 1 0 1 0 2 0a1 1 0 0 0 -2 0", "key": "svg-1" }], ["path", { "d": "M19 16v3", "key": "svg-2" }], ["path", { "d": "M19 22v.01", "key": "svg-3" }]]);

export {
  IconDeviceTabletExclamation
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconDeviceTabletExclamation.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XATY2EYZ.js.map
