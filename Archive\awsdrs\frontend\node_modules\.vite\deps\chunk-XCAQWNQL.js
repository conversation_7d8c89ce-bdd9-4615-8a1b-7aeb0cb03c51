import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconClockHour10.mjs
var IconClockHour10 = createReactComponent("outline", "clock-hour-10", "IconClockHour10", [["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-0" }], ["path", { "d": "M12 12l-3 -2", "key": "svg-1" }], ["path", { "d": "M12 7v5", "key": "svg-2" }]]);

export {
  IconClockHour10
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconClockHour10.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XCAQWNQL.js.map
