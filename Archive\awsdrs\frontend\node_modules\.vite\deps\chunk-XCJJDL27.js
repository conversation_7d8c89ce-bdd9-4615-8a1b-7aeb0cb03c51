import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconBrandCss3.mjs
var IconBrandCss3 = createReactComponent("outline", "brand-css3", "IconBrandCss3", [["path", { "d": "M20 4l-2 14.5l-6 2l-6 -2l-2 -14.5z", "key": "svg-0" }], ["path", { "d": "M8.5 8h7l-4.5 4h4l-.5 3.5l-2.5 .75l-2.5 -.75l-.1 -.5", "key": "svg-1" }]]);

export {
  IconBrandCss3
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconBrandCss3.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XCJJDL27.js.map
