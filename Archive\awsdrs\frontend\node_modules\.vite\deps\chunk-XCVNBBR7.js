import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconBoxAlignRightFilled.mjs
var IconBoxAlignRightFilled = createReactComponent("filled", "box-align-right-filled", "IconBoxAlignRightFilled", [["path", { "d": "M18.998 3.003h-5a1 1 0 0 0 -1 1v16a1 1 0 0 0 1 1h5a2 2 0 0 0 2 -2v-14a2 2 0 0 0 -2 -2z", "key": "svg-0" }], ["path", { "d": "M9.008 19.003a1 1 0 0 1 .117 1.993l-.127 .007a1 1 0 0 1 -.117 -1.993l.127 -.007z", "key": "svg-1" }], ["path", { "d": "M4.008 19.003a1 1 0 0 1 .117 1.993l-.128 .007a1 1 0 0 1 -.117 -1.993l.128 -.007z", "key": "svg-2" }], ["path", { "d": "M4.008 14.002a1 1 0 0 1 .117 1.993l-.128 .007a1 1 0 0 1 -.117 -1.993l.128 -.007z", "key": "svg-3" }], ["path", { "d": "M4.008 8.002a1 1 0 0 1 .117 1.993l-.128 .007a1 1 0 0 1 -.117 -1.993l.128 -.007z", "key": "svg-4" }], ["path", { "d": "M4.008 3.002a1 1 0 0 1 .117 1.993l-.128 .007a1 1 0 0 1 -.117 -1.993l.128 -.007z", "key": "svg-5" }], ["path", { "d": "M9.008 3.002a1 1 0 0 1 .117 1.993l-.127 .007a1 1 0 0 1 -.117 -1.993l.127 -.007z", "key": "svg-6" }]]);

export {
  IconBoxAlignRightFilled
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconBoxAlignRightFilled.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XCVNBBR7.js.map
