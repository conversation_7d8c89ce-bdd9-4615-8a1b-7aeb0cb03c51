import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconArrowDown.mjs
var IconArrowDown = createReactComponent("outline", "arrow-down", "IconArrowDown", [["path", { "d": "M12 5l0 14", "key": "svg-0" }], ["path", { "d": "M18 13l-6 6", "key": "svg-1" }], ["path", { "d": "M6 13l6 6", "key": "svg-2" }]]);

export {
  IconArrowDown
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconArrowDown.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XDEQ46WK.js.map
