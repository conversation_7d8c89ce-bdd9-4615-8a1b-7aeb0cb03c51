import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconDeviceDesktopPlus.mjs
var IconDeviceDesktopPlus = createReactComponent("outline", "device-desktop-plus", "IconDeviceDesktopPlus", [["path", { "d": "M13.5 16h-9.5a1 1 0 0 1 -1 -1v-10a1 1 0 0 1 1 -1h16a1 1 0 0 1 1 1v7.5", "key": "svg-0" }], ["path", { "d": "M7 20h5", "key": "svg-1" }], ["path", { "d": "M9 16v4", "key": "svg-2" }], ["path", { "d": "M16 19h6", "key": "svg-3" }], ["path", { "d": "M19 16v6", "key": "svg-4" }]]);

export {
  IconDeviceDesktopPlus
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconDeviceDesktopPlus.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XDLFANFC.js.map
