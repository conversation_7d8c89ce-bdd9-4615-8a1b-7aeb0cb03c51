{"version": 3, "sources": ["../../@tabler/icons-react/src/icons/IconDeviceDesktopPlus.ts"], "sourcesContent": ["import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'device-desktop-plus', 'IconDeviceDesktopPlus', [[\"path\",{\"d\":\"M13.5 16h-9.5a1 1 0 0 1 -1 -1v-10a1 1 0 0 1 1 -1h16a1 1 0 0 1 1 1v7.5\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M7 20h5\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M9 16v4\",\"key\":\"svg-2\"}],[\"path\",{\"d\":\"M16 19h6\",\"key\":\"svg-3\"}],[\"path\",{\"d\":\"M19 16v6\",\"key\":\"svg-4\"}]]);"], "mappings": ";;;;;AACA,IAAA,wBAAe,qBAAqB,WAAW,uBAAuB,yBAAyB,CAAC,CAAC,QAAO,EAAC,KAAI,yEAAwE,OAAM,QAAA,CAAQ,GAAE,CAAC,QAAO,EAAC,KAAI,WAAU,OAAM,QAAQ,CAAA,GAAE,CAAC,QAAO,EAAC,KAAI,WAAU,OAAM,QAAO,CAAC,GAAE,CAAC,QAAO,EAAC,KAAI,YAAW,OAAM,QAAA,CAAQ,GAAE,CAAC,QAAO,EAAC,KAAI,YAAW,OAAM,QAAQ,CAAA,CAAC,CAAC;", "names": []}