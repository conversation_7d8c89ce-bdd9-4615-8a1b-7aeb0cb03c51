{"version": 3, "sources": ["../../@tabler/icons-react/src/icons/IconCircleLetterRFilled.ts"], "sourcesContent": ["import createReactComponent from '../createReactComponent';\nexport default createReactComponent('filled', 'circle-letter-r-filled', 'IconCircleLetterRFilled', [[\"path\",{\"d\":\"M12 2c5.523 0 10 4.477 10 10s-4.477 10 -10 10s-10 -4.477 -10 -10s4.477 -10 10 -10m0 5h-2a1 1 0 0 0 -1 1v8a1 1 0 0 0 1 1l.117 -.007a1 1 0 0 0 .883 -.993v-2.332l2.2 2.932a1 1 0 0 0 1.4 .2l.096 -.081a1 1 0 0 0 .104 -1.319l-1.903 -2.538l.115 -.037a3.001 3.001 0 0 0 -1.012 -5.825m0 2a1 1 0 0 1 0 2h-1v-2z\",\"key\":\"svg-0\"}]]);"], "mappings": ";;;;;AACA,IAAe,0BAAA,qBAAqB,UAAU,0BAA0B,2BAA2B,CAAC,CAAC,QAAO,EAAC,KAAI,gTAA+S,OAAM,QAAO,CAAC,CAAC,CAAC;", "names": []}