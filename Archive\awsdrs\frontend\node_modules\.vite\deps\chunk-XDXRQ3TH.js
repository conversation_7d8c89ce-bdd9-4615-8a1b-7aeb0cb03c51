import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconVenus.mjs
var IconVenus = createReactComponent("outline", "venus", "IconVenus", [["path", { "d": "M12 9m-5 0a5 5 0 1 0 10 0a5 5 0 1 0 -10 0", "key": "svg-0" }], ["path", { "d": "M12 14l0 7", "key": "svg-1" }], ["path", { "d": "M9 18l6 0", "key": "svg-2" }]]);

export {
  IconVenus
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconVenus.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XDXRQ3TH.js.map
