import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconBatteryVertical.mjs
var IconBatteryVertical = createReactComponent("outline", "battery-vertical", "IconBatteryVertical", [["path", { "d": "M7 18v-11a2 2 0 0 1 2 -2h.5a.5 .5 0 0 0 .5 -.5a.5 .5 0 0 1 .5 -.5h3a.5 .5 0 0 1 .5 .5a.5 .5 0 0 0 .5 .5h.5a2 2 0 0 1 2 2v11a2 2 0 0 1 -2 2h-6a2 2 0 0 1 -2 -2", "key": "svg-0" }]]);

export {
  IconBatteryVertical
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconBatteryVertical.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XE6ZLOHA.js.map
