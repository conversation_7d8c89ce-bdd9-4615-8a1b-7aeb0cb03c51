import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconSquareRoundedLetterG.mjs
var IconSquareRoundedLetterG = createReactComponent("outline", "square-rounded-letter-g", "IconSquareRoundedLetterG", [["path", { "d": "M14 8h-2a2 2 0 0 0 -2 2v4a2 2 0 0 0 2 2h2v-4h-1", "key": "svg-0" }], ["path", { "d": "M12 3c7.2 0 9 1.8 9 9s-1.8 9 -9 9s-9 -1.8 -9 -9s1.8 -9 9 -9z", "key": "svg-1" }]]);

export {
  IconSquareRoundedLetterG
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconSquareRoundedLetterG.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XEHGG3VP.js.map
