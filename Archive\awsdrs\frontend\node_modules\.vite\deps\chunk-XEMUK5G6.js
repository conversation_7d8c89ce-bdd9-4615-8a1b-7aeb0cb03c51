import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconCirclePercentage.mjs
var IconCirclePercentage = createReactComponent("outline", "circle-percentage", "IconCirclePercentage", [["path", { "d": "M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0", "key": "svg-0" }], ["path", { "d": "M9 15.075l6 -6", "key": "svg-1" }], ["path", { "d": "M9 9.105v.015", "key": "svg-2" }], ["path", { "d": "M15 15.12v.015", "key": "svg-3" }]]);

export {
  IconCirclePercentage
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconCirclePercentage.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XEMUK5G6.js.map
