import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconCpu2.mjs
var IconCpu2 = createReactComponent("outline", "cpu-2", "IconCpu2", [["path", { "d": "M5 5m0 1a1 1 0 0 1 1 -1h12a1 1 0 0 1 1 1v12a1 1 0 0 1 -1 1h-12a1 1 0 0 1 -1 -1z", "key": "svg-0" }], ["path", { "d": "M8 10v-2h2m6 6v2h-2m-4 0h-2v-2m8 -4v-2h-2", "key": "svg-1" }], ["path", { "d": "M3 10h2", "key": "svg-2" }], ["path", { "d": "M3 14h2", "key": "svg-3" }], ["path", { "d": "M10 3v2", "key": "svg-4" }], ["path", { "d": "M14 3v2", "key": "svg-5" }], ["path", { "d": "M21 10h-2", "key": "svg-6" }], ["path", { "d": "M21 14h-2", "key": "svg-7" }], ["path", { "d": "M14 21v-2", "key": "svg-8" }], ["path", { "d": "M10 21v-2", "key": "svg-9" }]]);

export {
  IconCpu2
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconCpu2.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XEURZ46J.js.map
