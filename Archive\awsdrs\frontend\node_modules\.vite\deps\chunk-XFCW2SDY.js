import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconCashMove.mjs
var IconCashMove = createReactComponent("outline", "cash-move", "IconCashMove", [["path", { "d": "M7 15h-3a1 1 0 0 1 -1 -1v-8a1 1 0 0 1 1 -1h12a1 1 0 0 1 1 1v3", "key": "svg-0" }], ["path", { "d": "M12 19h-4a1 1 0 0 1 -1 -1v-8a1 1 0 0 1 1 -1h12a1 1 0 0 1 1 1v2.5", "key": "svg-1" }], ["path", { "d": "M15.92 13.437a2 2 0 1 0 -2.472 2.486", "key": "svg-2" }], ["path", { "d": "M16 19h6", "key": "svg-3" }], ["path", { "d": "M19 16l3 3l-3 3", "key": "svg-4" }]]);

export {
  IconCashMove
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconCashMove.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XFCW2SDY.js.map
