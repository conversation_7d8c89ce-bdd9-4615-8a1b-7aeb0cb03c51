import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconShieldCancel.mjs
var IconShieldCancel = createReactComponent("outline", "shield-cancel", "IconShieldCancel", [["path", { "d": "M12.277 20.925c-.092 .026 -.184 .051 -.277 .075a12 12 0 0 1 -8.5 -15a12 12 0 0 0 8.5 -3a12 12 0 0 0 8.5 3a12 12 0 0 1 .145 6.232", "key": "svg-0" }], ["path", { "d": "M19 19m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0", "key": "svg-1" }], ["path", { "d": "M17 21l4 -4", "key": "svg-2" }]]);

export {
  IconShieldCancel
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconShieldCancel.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XFDUC5MZ.js.map
