import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconWallet.mjs
var IconWallet = createReactComponent("outline", "wallet", "IconWallet", [["path", { "d": "M17 8v-3a1 1 0 0 0 -1 -1h-10a2 2 0 0 0 0 4h12a1 1 0 0 1 1 1v3m0 4v3a1 1 0 0 1 -1 1h-12a2 2 0 0 1 -2 -2v-12", "key": "svg-0" }], ["path", { "d": "M20 12v4h-4a2 2 0 0 1 0 -4h4", "key": "svg-1" }]]);

export {
  IconWallet
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconWallet.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XFGQ4YUB.js.map
