import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconOlympics.mjs
var IconOlympics = createReactComponent("outline", "olympics", "IconOlympics", [["path", { "d": "M6 9m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0", "key": "svg-0" }], ["path", { "d": "M18 9m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0", "key": "svg-1" }], ["path", { "d": "M12 9m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0", "key": "svg-2" }], ["path", { "d": "M9 15m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0", "key": "svg-3" }], ["path", { "d": "M15 15m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0", "key": "svg-4" }]]);

export {
  IconOlympics
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconOlympics.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XGNPYES5.js.map
