import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconNumber70Small.mjs
var IconNumber70Small = createReactComponent("outline", "number-70-small", "IconNumber70Small", [["path", { "d": "M14 10v4a2 2 0 1 0 4 0v-4a2 2 0 1 0 -4 0", "key": "svg-0" }], ["path", { "d": "M6 8h4l-2 8", "key": "svg-1" }]]);

export {
  IconNumber70Small
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconNumber70Small.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XH4OQ2VV.js.map
