import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconMessagePause.mjs
var IconMessagePause = createReactComponent("outline", "message-pause", "IconMessagePause", [["path", { "d": "M8 9h8", "key": "svg-0" }], ["path", { "d": "M8 13h6", "key": "svg-1" }], ["path", { "d": "M13 18l-5 3v-3h-2a3 3 0 0 1 -3 -3v-8a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v6", "key": "svg-2" }], ["path", { "d": "M17 17v5", "key": "svg-3" }], ["path", { "d": "M21 17v5", "key": "svg-4" }]]);

export {
  IconMessagePause
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconMessagePause.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XH6RJGIB.js.map
