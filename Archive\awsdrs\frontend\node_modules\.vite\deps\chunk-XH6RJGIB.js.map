{"version": 3, "sources": ["../../@tabler/icons-react/src/icons/IconMessagePause.ts"], "sourcesContent": ["import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'message-pause', 'IconMessagePause', [[\"path\",{\"d\":\"M8 9h8\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M8 13h6\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M13 18l-5 3v-3h-2a3 3 0 0 1 -3 -3v-8a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v6\",\"key\":\"svg-2\"}],[\"path\",{\"d\":\"M17 17v5\",\"key\":\"svg-3\"}],[\"path\",{\"d\":\"M21 17v5\",\"key\":\"svg-4\"}]]);"], "mappings": ";;;;;AACA,IAAA,mBAAe,qBAAqB,WAAW,iBAAiB,oBAAoB,CAAC,CAAC,QAAO,EAAC,KAAI,UAAS,OAAM,QAAA,CAAQ,GAAE,CAAC,QAAO,EAAC,KAAI,WAAU,OAAM,QAAQ,CAAA,GAAE,CAAC,QAAO,EAAC,KAAI,0EAAyE,OAAM,QAAO,CAAC,GAAE,CAAC,QAAO,EAAC,KAAI,YAAW,OAAM,QAAA,CAAQ,GAAE,CAAC,QAAO,EAAC,KAAI,YAAW,OAAM,QAAQ,CAAA,CAAC,CAAC;", "names": []}