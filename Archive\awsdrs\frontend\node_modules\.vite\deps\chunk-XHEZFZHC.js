import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconInputCheck.mjs
var IconInputCheck = createReactComponent("outline", "input-check", "IconInputCheck", [["path", { "d": "M20 13v-4a2 2 0 0 0 -2 -2h-12a2 2 0 0 0 -2 2v5a2 2 0 0 0 2 2h6", "key": "svg-0" }], ["path", { "d": "M15 19l2 2l4 -4", "key": "svg-1" }]]);

export {
  IconInputCheck
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconInputCheck.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XHEZFZHC.js.map
