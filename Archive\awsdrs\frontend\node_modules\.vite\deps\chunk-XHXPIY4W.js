import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconTableShare.mjs
var IconTableShare = createReactComponent("outline", "table-share", "IconTableShare", [["path", { "d": "M12 21h-7a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v8", "key": "svg-0" }], ["path", { "d": "M3 10h18", "key": "svg-1" }], ["path", { "d": "M10 3v18", "key": "svg-2" }], ["path", { "d": "M16 22l5 -5", "key": "svg-3" }], ["path", { "d": "M21 21.5v-4.5h-4.5", "key": "svg-4" }]]);

export {
  IconTableShare
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconTableShare.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XHXPIY4W.js.map
