{"version": 3, "sources": ["../../@tabler/icons-react/src/icons/IconTableShare.ts"], "sourcesContent": ["import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'table-share', 'IconTableShare', [[\"path\",{\"d\":\"M12 21h-7a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v8\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M3 10h18\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M10 3v18\",\"key\":\"svg-2\"}],[\"path\",{\"d\":\"M16 22l5 -5\",\"key\":\"svg-3\"}],[\"path\",{\"d\":\"M21 21.5v-4.5h-4.5\",\"key\":\"svg-4\"}]]);"], "mappings": ";;;;;AACA,IAAA,iBAAe,qBAAqB,WAAW,eAAe,kBAAkB,CAAC,CAAC,QAAO,EAAC,KAAI,mEAAkE,OAAM,QAAA,CAAQ,GAAE,CAAC,QAAO,EAAC,KAAI,YAAW,OAAM,QAAQ,CAAA,GAAE,CAAC,QAAO,EAAC,KAAI,YAAW,OAAM,QAAO,CAAC,GAAE,CAAC,QAAO,EAAC,KAAI,eAAc,OAAM,QAAA,CAAQ,GAAE,CAAC,QAAO,EAAC,KAAI,sBAAqB,OAAM,QAAQ,CAAA,CAAC,CAAC;", "names": []}