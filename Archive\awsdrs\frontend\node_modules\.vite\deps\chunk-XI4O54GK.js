import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconCoinYuan.mjs
var IconCoinYuan = createReactComponent("outline", "coin-yuan", "IconCoinYuan", [["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-0" }], ["path", { "d": "M9 13h6", "key": "svg-1" }], ["path", { "d": "M9 8l3 4.5", "key": "svg-2" }], ["path", { "d": "M15 8l-3 4.5v4.5", "key": "svg-3" }]]);

export {
  IconCoinYuan
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconCoinYuan.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XI4O54GK.js.map
