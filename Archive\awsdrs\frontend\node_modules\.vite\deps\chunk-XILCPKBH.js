import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconInnerShadowTopRightFilled.mjs
var IconInnerShadowTopRightFilled = createReactComponent("filled", "inner-shadow-top-right-filled", "IconInnerShadowTopRightFilled", [["path", { "d": "M12 2c5.523 0 10 4.477 10 10s-4.477 10 -10 10s-10 -4.477 -10 -10s4.477 -10 10 -10zm0 3a1 1 0 0 0 0 2a5 5 0 0 1 5 5a1 1 0 0 0 2 0a7 7 0 0 0 -7 -7z", "key": "svg-0" }]]);

export {
  IconInnerShadowTopRightFilled
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconInnerShadowTopRightFilled.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XILCPKBH.js.map
