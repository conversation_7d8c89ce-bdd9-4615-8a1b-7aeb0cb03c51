{"version": 3, "sources": ["../../@tabler/icons-react/src/icons/IconZodiacGemini.ts"], "sourcesContent": ["import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'zodiac-gemini', 'IconZodiacGemini', [[\"path\",{\"d\":\"M3 3a21 21 0 0 0 18 0\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M3 21a21 21 0 0 1 18 0\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M7 4.5l0 15\",\"key\":\"svg-2\"}],[\"path\",{\"d\":\"M17 4.5l0 15\",\"key\":\"svg-3\"}]]);"], "mappings": ";;;;;AACA,IAAA,mBAAe,qBAAqB,WAAW,iBAAiB,oBAAoB,CAAC,CAAC,QAAO,EAAC,KAAI,yBAAwB,OAAM,QAAA,CAAQ,GAAE,CAAC,QAAO,EAAC,KAAI,0BAAyB,OAAM,QAAQ,CAAA,GAAE,CAAC,QAAO,EAAC,KAAI,eAAc,OAAM,QAAO,CAAC,GAAE,CAAC,QAAO,EAAC,KAAI,gBAAe,OAAM,QAAQ,CAAA,CAAC,CAAC;", "names": []}