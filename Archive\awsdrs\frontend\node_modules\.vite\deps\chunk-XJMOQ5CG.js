import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconBracketsOff.mjs
var IconBracketsOff = createReactComponent("outline", "brackets-off", "IconBracketsOff", [["path", { "d": "M5 5v15h3", "key": "svg-0" }], ["path", { "d": "M16 4h3v11m0 4v1h-3", "key": "svg-1" }], ["path", { "d": "M3 3l18 18", "key": "svg-2" }]]);

export {
  IconBracketsOff
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconBracketsOff.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XJMOQ5CG.js.map
