import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconBounceLeft.mjs
var IconBounceLeft = createReactComponent("outline", "bounce-left", "IconBounceLeft", [["path", { "d": "M20 15.5c-3 -1 -5.5 -.5 -8 4.5c-.5 -3 -1.5 -5.5 -3 -8", "key": "svg-0" }], ["path", { "d": "M6 9a2 2 0 1 1 0 -4a2 2 0 0 1 0 4z", "key": "svg-1" }]]);

export {
  IconBounceLeft
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconBounceLeft.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XKF37KHZ.js.map
