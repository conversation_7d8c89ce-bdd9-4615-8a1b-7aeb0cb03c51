import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconDotsDiagonal2.mjs
var IconDotsDiagonal2 = createReactComponent("outline", "dots-diagonal-2", "IconDotsDiagonal2", [["path", { "d": "M7 7m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0", "key": "svg-0" }], ["path", { "d": "M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0", "key": "svg-1" }], ["path", { "d": "M17 17m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0", "key": "svg-2" }]]);

export {
  IconDotsDiagonal2
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconDotsDiagonal2.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XKHVXCPG.js.map
