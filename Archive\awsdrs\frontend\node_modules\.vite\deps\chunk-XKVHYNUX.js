import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconNavigationPin.mjs
var IconNavigationPin = createReactComponent("outline", "navigation-pin", "IconNavigationPin", [["path", { "d": "M16.002 11.676l-4.002 -8.676l-7.97 17.275c-.07 .2 -.017 .424 .135 .572c.15 .148 .374 .193 .57 .116l7.265 -2.463", "key": "svg-0" }], ["path", { "d": "M21.121 20.121a3 3 0 1 0 -4.242 0c.418 .419 1.125 1.045 2.121 1.879c1.051 -.89 1.759 -1.516 2.121 -1.879z", "key": "svg-1" }], ["path", { "d": "M19 18v.01", "key": "svg-2" }]]);

export {
  IconNavigationPin
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconNavigationPin.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XKVHYNUX.js.map
