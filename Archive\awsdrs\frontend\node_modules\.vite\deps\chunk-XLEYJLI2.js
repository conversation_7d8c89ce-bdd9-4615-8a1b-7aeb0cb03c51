import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconDevicesPause.mjs
var IconDevicesPause = createReactComponent("outline", "devices-pause", "IconDevicesPause", [["path", { "d": "M13 19v-10a1 1 0 0 1 1 -1h6a1 1 0 0 1 1 1v4", "key": "svg-0" }], ["path", { "d": "M18 8v-3a1 1 0 0 0 -1 -1h-13a1 1 0 0 0 -1 1v12a1 1 0 0 0 1 1h9", "key": "svg-1" }], ["path", { "d": "M17 17v5", "key": "svg-2" }], ["path", { "d": "M21 17v5", "key": "svg-3" }], ["path", { "d": "M16 9h2", "key": "svg-4" }]]);

export {
  IconDevicesPause
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconDevicesPause.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XLEYJLI2.js.map
