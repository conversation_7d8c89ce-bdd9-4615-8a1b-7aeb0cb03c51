{"version": 3, "sources": ["../../@tabler/icons-react/src/icons/IconDevicesPause.ts"], "sourcesContent": ["import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'devices-pause', 'IconDevicesPause', [[\"path\",{\"d\":\"M13 19v-10a1 1 0 0 1 1 -1h6a1 1 0 0 1 1 1v4\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M18 8v-3a1 1 0 0 0 -1 -1h-13a1 1 0 0 0 -1 1v12a1 1 0 0 0 1 1h9\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M17 17v5\",\"key\":\"svg-2\"}],[\"path\",{\"d\":\"M21 17v5\",\"key\":\"svg-3\"}],[\"path\",{\"d\":\"M16 9h2\",\"key\":\"svg-4\"}]]);"], "mappings": ";;;;;AACA,IAAA,mBAAe,qBAAqB,WAAW,iBAAiB,oBAAoB,CAAC,CAAC,QAAO,EAAC,KAAI,+CAA8C,OAAM,QAAA,CAAQ,GAAE,CAAC,QAAO,EAAC,KAAI,kEAAiE,OAAM,QAAQ,CAAA,GAAE,CAAC,QAAO,EAAC,KAAI,YAAW,OAAM,QAAO,CAAC,GAAE,CAAC,QAAO,EAAC,KAAI,YAAW,OAAM,QAAA,CAAQ,GAAE,CAAC,QAAO,EAAC,KAAI,WAAU,OAAM,QAAQ,CAAA,CAAC,CAAC;", "names": []}