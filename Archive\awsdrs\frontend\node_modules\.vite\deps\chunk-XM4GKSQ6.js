import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconBellQuestion.mjs
var IconBellQuestion = createReactComponent("outline", "bell-question", "IconBellQuestion", [["path", { "d": "M13.5 17h-9.5a4 4 0 0 0 2 -3v-3a7 7 0 0 1 4 -6a2 2 0 1 1 4 0a7 7 0 0 1 4 6", "key": "svg-0" }], ["path", { "d": "M9 17v1a3 3 0 0 0 5.914 .716", "key": "svg-1" }], ["path", { "d": "M19 22v.01", "key": "svg-2" }], ["path", { "d": "M19 19a2.003 2.003 0 0 0 .914 -3.782a1.98 1.98 0 0 0 -2.414 .483", "key": "svg-3" }]]);

export {
  IconBellQuestion
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconBellQuestion.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XM4GKSQ6.js.map
