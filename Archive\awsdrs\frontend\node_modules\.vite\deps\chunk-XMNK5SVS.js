import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconHelpOff.mjs
var IconHelpOff = createReactComponent("outline", "help-off", "IconHelpOff", [["path", { "d": "M5.641 5.631a9 9 0 1 0 12.719 12.738m1.68 -2.318a9 9 0 0 0 -12.074 -12.098", "key": "svg-0" }], ["path", { "d": "M12 17v.01", "key": "svg-1" }], ["path", { "d": "M12 13.5a1.5 1.5 0 0 1 .394 -1.1m2.106 -1.9a2.6 2.6 0 0 0 -3.347 -3.361", "key": "svg-2" }], ["path", { "d": "M3 3l18 18", "key": "svg-3" }]]);

export {
  IconHelpOff
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconHelpOff.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XMNK5SVS.js.map
