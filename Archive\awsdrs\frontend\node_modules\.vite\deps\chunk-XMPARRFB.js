import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconLayersIntersect2.mjs
var IconLayersIntersect2 = createReactComponent("outline", "layers-intersect-2", "IconLayersIntersect2", [["path", { "d": "M8 4m0 2a2 2 0 0 1 2 -2h8a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-8a2 2 0 0 1 -2 -2z", "key": "svg-0" }], ["path", { "d": "M4 8m0 2a2 2 0 0 1 2 -2h8a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-8a2 2 0 0 1 -2 -2z", "key": "svg-1" }], ["path", { "d": "M9 15l6 -6", "key": "svg-2" }]]);

export {
  IconLayersIntersect2
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconLayersIntersect2.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XMPARRFB.js.map
