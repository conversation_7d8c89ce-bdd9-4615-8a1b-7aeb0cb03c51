import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconBrandPagekit.mjs
var IconBrandPagekit = createReactComponent("outline", "brand-pagekit", "IconBrandPagekit", [["path", { "d": "M12.077 20h-5.077v-16h11v14h-5.077", "key": "svg-0" }]]);

export {
  IconBrandPagekit
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconBrandPagekit.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XNXYD5E2.js.map
