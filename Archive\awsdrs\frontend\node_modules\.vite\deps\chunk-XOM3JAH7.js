import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconBlockquote.mjs
var IconBlockquote = createReactComponent("outline", "blockquote", "IconBlockquote", [["path", { "d": "M6 15h15", "key": "svg-0" }], ["path", { "d": "M21 19h-15", "key": "svg-1" }], ["path", { "d": "M15 11h6", "key": "svg-2" }], ["path", { "d": "M21 7h-6", "key": "svg-3" }], ["path", { "d": "M9 9h1a1 1 0 1 1 -1 1v-2.5a2 2 0 0 1 2 -2", "key": "svg-4" }], ["path", { "d": "M3 9h1a1 1 0 1 1 -1 1v-2.5a2 2 0 0 1 2 -2", "key": "svg-5" }]]);

export {
  IconBlockquote
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconBlockquote.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XOM3JAH7.js.map
