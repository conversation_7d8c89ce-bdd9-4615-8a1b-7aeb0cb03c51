import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconPlayCardA.mjs
var IconPlayCardA = createReactComponent("outline", "play-card-a", "IconPlayCardA", [["path", { "d": "M19 5v14a2 2 0 0 1 -2 2h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2", "key": "svg-0" }], ["path", { "d": "M8 6h.01", "key": "svg-1" }], ["path", { "d": "M16 18h.01", "key": "svg-2" }], ["path", { "d": "M10 15v-4a2 2 0 1 1 4 0v4", "key": "svg-3" }], ["path", { "d": "M10 13h4", "key": "svg-4" }]]);

export {
  IconPlayCardA
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconPlayCardA.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XPE3LIPF.js.map
