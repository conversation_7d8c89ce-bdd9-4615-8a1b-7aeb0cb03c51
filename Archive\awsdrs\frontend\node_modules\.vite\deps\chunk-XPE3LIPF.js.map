{"version": 3, "sources": ["../../@tabler/icons-react/src/icons/IconPlayCardA.ts"], "sourcesContent": ["import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'play-card-a', 'IconPlayCardA', [[\"path\",{\"d\":\"M19 5v14a2 2 0 0 1 -2 2h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M8 6h.01\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M16 18h.01\",\"key\":\"svg-2\"}],[\"path\",{\"d\":\"M10 15v-4a2 2 0 1 1 4 0v4\",\"key\":\"svg-3\"}],[\"path\",{\"d\":\"M10 13h4\",\"key\":\"svg-4\"}]]);"], "mappings": ";;;;;AACA,IAAA,gBAAe,qBAAqB,WAAW,eAAe,iBAAiB,CAAC,CAAC,QAAO,EAAC,KAAI,mFAAkF,OAAM,QAAA,CAAQ,GAAE,CAAC,QAAO,EAAC,KAAI,YAAW,OAAM,QAAQ,CAAA,GAAE,CAAC,QAAO,EAAC,KAAI,cAAa,OAAM,QAAO,CAAC,GAAE,CAAC,QAAO,EAAC,KAAI,6BAA4B,OAAM,QAAA,CAAQ,GAAE,CAAC,QAAO,EAAC,KAAI,YAAW,OAAM,QAAQ,CAAA,CAAC,CAAC;", "names": []}