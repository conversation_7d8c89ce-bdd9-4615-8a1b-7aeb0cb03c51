import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconColumns2Filled.mjs
var IconColumns2Filled = createReactComponent("filled", "columns-2-filled", "IconColumns2Filled", [["path", { "d": "M4 2h6a1 1 0 0 1 1 1v18a1 1 0 0 1 -1 1h-6a2 2 0 0 1 -2 -2v-16a2 2 0 0 1 2 -2", "key": "svg-0" }], ["path", { "d": "M14 2h6a2 2 0 0 1 2 2v16a2 2 0 0 1 -2 2h-6a1 1 0 0 1 -1 -1v-18a1 1 0 0 1 1 -1", "key": "svg-1" }]]);

export {
  IconColumns2Filled
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconColumns2Filled.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XQFFOXPK.js.map
