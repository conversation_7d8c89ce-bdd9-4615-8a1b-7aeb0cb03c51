import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconChartColumn.mjs
var IconChartColumn = createReactComponent("outline", "chart-column", "IconChartColumn", [["path", { "d": "M4 20h3", "key": "svg-0" }], ["path", { "d": "M17 20h3", "key": "svg-1" }], ["path", { "d": "M10.5 20h3", "key": "svg-2" }], ["path", { "d": "M4 16h3", "key": "svg-3" }], ["path", { "d": "M17 16h3", "key": "svg-4" }], ["path", { "d": "M10.5 16h3", "key": "svg-5" }], ["path", { "d": "M4 12h3", "key": "svg-6" }], ["path", { "d": "M17 12h3", "key": "svg-7" }], ["path", { "d": "M10.5 12h3", "key": "svg-8" }], ["path", { "d": "M4 8h3", "key": "svg-9" }], ["path", { "d": "M17 8h3", "key": "svg-10" }], ["path", { "d": "M4 4h3", "key": "svg-11" }]]);

export {
  IconChartColumn
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconChartColumn.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XQQ2JKNR.js.map
