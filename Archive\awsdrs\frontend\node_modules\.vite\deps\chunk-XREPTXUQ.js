import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconCell.mjs
var IconCell = createReactComponent("outline", "cell", "IconCell", [["path", { "d": "M8 4l-4 2v5l4 2l4 -2v-5z", "key": "svg-0" }], ["path", { "d": "M12 11l4 2l4 -2v-5l-4 -2l-4 2", "key": "svg-1" }], ["path", { "d": "M8 13v5l4 2l4 -2v-5", "key": "svg-2" }]]);

export {
  IconCell
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconCell.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XREPTXUQ.js.map
