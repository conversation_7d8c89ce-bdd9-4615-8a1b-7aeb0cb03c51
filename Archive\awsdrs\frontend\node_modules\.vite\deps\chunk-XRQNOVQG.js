import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconArrowUpDashed.mjs
var IconArrowUpDashed = createReactComponent("outline", "arrow-up-dashed", "IconArrowUpDashed", [["path", { "d": "M12 5v6m0 3v1.5m0 3v.5", "key": "svg-0" }], ["path", { "d": "M18 11l-6 -6", "key": "svg-1" }], ["path", { "d": "M6 11l6 -6", "key": "svg-2" }]]);

export {
  IconArrowUpDashed
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconArrowUpDashed.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XRQNOVQG.js.map
