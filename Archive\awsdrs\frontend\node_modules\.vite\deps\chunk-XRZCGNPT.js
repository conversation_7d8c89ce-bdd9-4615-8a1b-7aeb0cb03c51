import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconAtom2.mjs
var IconAtom2 = createReactComponent("outline", "atom-2", "IconAtom2", [["path", { "d": "M12 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0", "key": "svg-0" }], ["path", { "d": "M12 21l0 .01", "key": "svg-1" }], ["path", { "d": "M3 9l0 .01", "key": "svg-2" }], ["path", { "d": "M21 9l0 .01", "key": "svg-3" }], ["path", { "d": "M8 20.1a9 9 0 0 1 -5 -7.1", "key": "svg-4" }], ["path", { "d": "M16 20.1a9 9 0 0 0 5 -7.1", "key": "svg-5" }], ["path", { "d": "M6.2 5a9 9 0 0 1 11.4 0", "key": "svg-6" }]]);

export {
  IconAtom2
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconAtom2.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XRZCGNPT.js.map
