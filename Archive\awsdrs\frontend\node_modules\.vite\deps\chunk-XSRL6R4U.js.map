{"version": 3, "sources": ["../../@tabler/icons-react/src/icons/IconDualScreenFilled.ts"], "sourcesContent": ["import createReactComponent from '../createReactComponent';\nexport default createReactComponent('filled', 'dual-screen-filled', 'IconDualScreenFilled', [[\"path\",{\"d\":\"M19 3a1 1 0 0 1 1 1v15a1 1 0 0 1 -1 1h-5v2a1 1 0 0 1 -1.351 .936l-8 -3a1 1 0 0 1 -.649 -.936v-15a1 1 0 0 1 .212 -.616l.068 -.079l.078 -.072l.066 -.05l.092 -.058l.065 -.033l.1 -.04l.099 -.028l.046 -.01l.108 -.013l.066 -.001zm-5.649 3.064a1 1 0 0 1 .649 .936v11h4v-13h-7.486z\",\"key\":\"svg-0\"}]]);"], "mappings": ";;;;;AACA,IAAe,uBAAA,qBAAqB,UAAU,sBAAsB,wBAAwB,CAAC,CAAC,QAAO,EAAC,KAAI,qRAAoR,OAAM,QAAO,CAAC,CAAC,CAAC;", "names": []}