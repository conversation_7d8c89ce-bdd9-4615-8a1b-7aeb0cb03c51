import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconCircleDashedNumber0.mjs
var IconCircleDashedNumber0 = createReactComponent("outline", "circle-dashed-number-0", "IconCircleDashedNumber0", [["path", { "d": "M8.56 3.69a9 9 0 0 0 -2.92 1.95", "key": "svg-0" }], ["path", { "d": "M3.69 8.56a9 9 0 0 0 -.69 3.44", "key": "svg-1" }], ["path", { "d": "M3.69 15.44a9 9 0 0 0 1.95 2.92", "key": "svg-2" }], ["path", { "d": "M8.56 20.31a9 9 0 0 0 3.44 .69", "key": "svg-3" }], ["path", { "d": "M15.44 20.31a9 9 0 0 0 2.92 -1.95", "key": "svg-4" }], ["path", { "d": "M20.31 15.44a9 9 0 0 0 .69 -3.44", "key": "svg-5" }], ["path", { "d": "M20.31 8.56a9 9 0 0 0 -1.95 -2.92", "key": "svg-6" }], ["path", { "d": "M15.44 3.69a9 9 0 0 0 -3.44 -.69", "key": "svg-7" }], ["path", { "d": "M10 10v4a2 2 0 1 0 4 0v-4a2 2 0 1 0 -4 0z", "key": "svg-8" }]]);

export {
  IconCircleDashedNumber0
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconCircleDashedNumber0.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XSVHCMY5.js.map
