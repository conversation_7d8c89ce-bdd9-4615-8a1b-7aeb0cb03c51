import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconBoxAlignTopFilled.mjs
var IconBoxAlignTopFilled = createReactComponent("filled", "box-align-top-filled", "IconBoxAlignTopFilled", [["path", { "d": "M19 3.005h-14a2 2 0 0 0 -2 2v5a1 1 0 0 0 1 1h16a1 1 0 0 0 1 -1v-5a2 2 0 0 0 -2 -2z", "key": "svg-0" }], ["path", { "d": "M4 13.995a1 1 0 0 1 .993 .883l.007 .127a1 1 0 0 1 -1.993 .117l-.007 -.127a1 1 0 0 1 1 -1z", "key": "svg-1" }], ["path", { "d": "M4 18.995a1 1 0 0 1 .993 .883l.007 .127a1 1 0 0 1 -1.993 .117l-.007 -.127a1 1 0 0 1 1 -1z", "key": "svg-2" }], ["path", { "d": "M9 18.995a1 1 0 0 1 .993 .883l.007 .127a1 1 0 0 1 -1.993 .117l-.007 -.127a1 1 0 0 1 1 -1z", "key": "svg-3" }], ["path", { "d": "M15 18.995a1 1 0 0 1 .993 .883l.007 .127a1 1 0 0 1 -1.993 .117l-.007 -.127a1 1 0 0 1 1 -1z", "key": "svg-4" }], ["path", { "d": "M20 18.995a1 1 0 0 1 .993 .883l.007 .127a1 1 0 0 1 -1.993 .117l-.007 -.127a1 1 0 0 1 1 -1z", "key": "svg-5" }], ["path", { "d": "M20 13.995a1 1 0 0 1 .993 .883l.007 .127a1 1 0 0 1 -1.993 .117l-.007 -.127a1 1 0 0 1 1 -1z", "key": "svg-6" }]]);

export {
  IconBoxAlignTopFilled
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconBoxAlignTopFilled.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XUGBIITO.js.map
