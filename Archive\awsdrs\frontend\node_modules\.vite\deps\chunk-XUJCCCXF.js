import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconCircleNumber4.mjs
var IconCircleNumber4 = createReactComponent("outline", "circle-number-4", "IconCircleNumber4", [["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-0" }], ["path", { "d": "M10 8v3a1 1 0 0 0 1 1h3", "key": "svg-1" }], ["path", { "d": "M14 8v8", "key": "svg-2" }]]);

export {
  IconCircleNumber4
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconCircleNumber4.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XUJCCCXF.js.map
