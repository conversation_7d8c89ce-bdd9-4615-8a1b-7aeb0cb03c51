import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconVideoMinus.mjs
var IconVideoMinus = createReactComponent("outline", "video-minus", "IconVideoMinus", [["path", { "d": "M15 10l4.553 -2.276a1 1 0 0 1 1.447 .894v6.764a1 1 0 0 1 -1.447 .894l-4.553 -2.276v-4z", "key": "svg-0" }], ["path", { "d": "M3 6m0 2a2 2 0 0 1 2 -2h8a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-8a2 2 0 0 1 -2 -2z", "key": "svg-1" }], ["path", { "d": "M7 12l4 0", "key": "svg-2" }]]);

export {
  IconVideoMinus
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconVideoMinus.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XUKXZU4P.js.map
