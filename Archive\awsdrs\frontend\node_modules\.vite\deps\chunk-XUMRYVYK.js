import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconCircleArrowUpRight.mjs
var IconCircleArrowUpRight = createReactComponent("outline", "circle-arrow-up-right", "IconCircleArrowUpRight", [["path", { "d": "M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0", "key": "svg-0" }], ["path", { "d": "M15 9l-6 6", "key": "svg-1" }], ["path", { "d": "M15 15v-6h-6", "key": "svg-2" }]]);

export {
  IconCircleArrowUpRight
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconCircleArrowUpRight.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XUMRYVYK.js.map
