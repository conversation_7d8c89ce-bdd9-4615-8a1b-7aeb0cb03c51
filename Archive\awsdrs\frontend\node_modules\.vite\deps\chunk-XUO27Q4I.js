import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconDeviceIpadStar.mjs
var IconDeviceIpadStar = createReactComponent("outline", "device-ipad-star", "IconDeviceIpadStar", [["path", { "d": "M11 21h-5a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v5.5", "key": "svg-0" }], ["path", { "d": "M9 18h1", "key": "svg-1" }], ["path", { "d": "M17.8 20.817l-2.172 1.138a.392 .392 0 0 1 -.568 -.41l.415 -2.411l-1.757 -1.707a.389 .389 0 0 1 .217 -.665l2.428 -.352l1.086 -2.193a.392 .392 0 0 1 .702 0l1.086 2.193l2.428 .352a.39 .39 0 0 1 .217 .665l-1.757 1.707l.414 2.41a.39 .39 0 0 1 -.567 .411l-2.172 -1.138z", "key": "svg-2" }]]);

export {
  IconDeviceIpadStar
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconDeviceIpadStar.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XUO27Q4I.js.map
