import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconBatteryVerticalFilled.mjs
var IconBatteryVerticalFilled = createReactComponent("filled", "battery-vertical-filled", "IconBatteryVerticalFilled", [["path", { "d": "M13.5 3a1.5 1.5 0 0 1 1.395 .948l.018 .052h.087a3 3 0 0 1 2.995 2.824l.005 .176v11a3 3 0 0 1 -3 3h-6a3 3 0 0 1 -3 -3v-11a3 3 0 0 1 3 -3h.086l.019 -.052a1.5 1.5 0 0 1 1.25 -.941l.145 -.007z", "key": "svg-0" }]]);

export {
  IconBatteryVerticalFilled
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconBatteryVerticalFilled.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XUXGUS6C.js.map
