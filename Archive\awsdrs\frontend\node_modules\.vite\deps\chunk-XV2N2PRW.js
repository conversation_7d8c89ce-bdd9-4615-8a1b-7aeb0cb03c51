import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconClockStar.mjs
var IconClockStar = createReactComponent("outline", "clock-star", "IconClockStar", [["path", { "d": "M20.982 11.436a9 9 0 1 0 -9.966 9.51", "key": "svg-0" }], ["path", { "d": "M17.8 20.817l-2.172 1.138a.392 .392 0 0 1 -.568 -.41l.415 -2.411l-1.757 -1.707a.389 .389 0 0 1 .217 -.665l2.428 -.352l1.086 -2.193a.392 .392 0 0 1 .702 0l1.086 2.193l2.428 .352a.39 .39 0 0 1 .217 .665l-1.757 1.707l.414 2.41a.39 .39 0 0 1 -.567 .411l-2.172 -1.138z", "key": "svg-1" }], ["path", { "d": "M12 7v5l1 1", "key": "svg-2" }]]);

export {
  IconClockStar
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconClockStar.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XV2N2PRW.js.map
