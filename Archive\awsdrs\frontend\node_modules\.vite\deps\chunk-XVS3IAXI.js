import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconFlagCancel.mjs
var IconFlagCancel = createReactComponent("outline", "flag-cancel", "IconFlagCancel", [["path", { "d": "M13.342 14.941a4.993 4.993 0 0 1 -1.342 -.941a5 5 0 0 0 -7 0v-9a5 5 0 0 1 7 0a5 5 0 0 0 7 0v7", "key": "svg-0" }], ["path", { "d": "M5 21v-7", "key": "svg-1" }], ["path", { "d": "M19 19m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0", "key": "svg-2" }], ["path", { "d": "M17 21l4 -4", "key": "svg-3" }]]);

export {
  IconFlagCancel
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconFlagCancel.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XVS3IAXI.js.map
