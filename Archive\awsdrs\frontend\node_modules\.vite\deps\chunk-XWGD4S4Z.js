import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconMoodLookUp.mjs
var IconMoodLookUp = createReactComponent("outline", "mood-look-up", "IconMoodLookUp", [["path", { "d": "M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0", "key": "svg-0" }], ["path", { "d": "M9 8h.01", "key": "svg-1" }], ["path", { "d": "M15 8h.01", "key": "svg-2" }], ["path", { "d": "M11 12h2", "key": "svg-3" }]]);

export {
  IconMoodLookUp
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconMoodLookUp.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XWGD4S4Z.js.map
