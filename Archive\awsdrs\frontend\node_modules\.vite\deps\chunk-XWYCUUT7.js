import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconEyePause.mjs
var IconEyePause = createReactComponent("outline", "eye-pause", "IconEyePause", [["path", { "d": "M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0", "key": "svg-0" }], ["path", { "d": "M13.022 17.945a9.308 9.308 0 0 1 -1.022 .055c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6c-.195 .325 -.394 .636 -.596 .935", "key": "svg-1" }], ["path", { "d": "M17 17v5", "key": "svg-2" }], ["path", { "d": "M21 17v5", "key": "svg-3" }]]);

export {
  IconEyePause
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconEyePause.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XWYCUUT7.js.map
