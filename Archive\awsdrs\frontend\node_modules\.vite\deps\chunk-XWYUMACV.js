import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconJson.mjs
var IconJson = createReactComponent("outline", "json", "IconJson", [["path", { "d": "M20 16v-8l3 8v-8", "key": "svg-0" }], ["path", { "d": "M15 8a2 2 0 0 1 2 2v4a2 2 0 1 1 -4 0v-4a2 2 0 0 1 2 -2z", "key": "svg-1" }], ["path", { "d": "M1 8h3v6.5a1.5 1.5 0 0 1 -3 0v-.5", "key": "svg-2" }], ["path", { "d": "M7 15a1 1 0 0 0 1 1h1a1 1 0 0 0 1 -1v-2a1 1 0 0 0 -1 -1h-1a1 1 0 0 1 -1 -1v-2a1 1 0 0 1 1 -1h1a1 1 0 0 1 1 1", "key": "svg-3" }]]);

export {
  IconJson
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconJson.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XWYUMACV.js.map
