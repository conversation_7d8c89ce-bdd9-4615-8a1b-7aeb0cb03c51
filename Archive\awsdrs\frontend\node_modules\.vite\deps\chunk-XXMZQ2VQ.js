import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconBlade.mjs
var IconBlade = createReactComponent("outline", "blade", "IconBlade", [["path", { "d": "M17.707 3.707l2.586 2.586a1 1 0 0 1 0 1.414l-.586 .586a1 1 0 0 0 0 1.414l.586 .586a1 1 0 0 1 0 1.414l-8.586 8.586a1 1 0 0 1 -1.414 0l-.586 -.586a1 1 0 0 0 -1.414 0l-.586 .586a1 1 0 0 1 -1.414 0l-2.586 -2.586a1 1 0 0 1 0 -1.414l.586 -.586a1 1 0 0 0 0 -1.414l-.586 -.586a1 1 0 0 1 0 -1.414l8.586 -8.586a1 1 0 0 1 1.414 0l.586 .586a1 1 0 0 0 1.414 0l.586 -.586a1 1 0 0 1 1.414 0z", "key": "svg-0" }], ["path", { "d": "M8 16l3.2 -3.2", "key": "svg-1" }], ["path", { "d": "M12.8 11.2l3.2 -3.2", "key": "svg-2" }], ["path", { "d": "M14 8l2 2", "key": "svg-3" }], ["path", { "d": "M8 14l2 2", "key": "svg-4" }], ["path", { "d": "M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0", "key": "svg-5" }]]);

export {
  IconBlade
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconBlade.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XXMZQ2VQ.js.map
