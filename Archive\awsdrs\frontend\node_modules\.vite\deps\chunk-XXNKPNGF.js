import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconArrowBarToUp.mjs
var IconArrowBarToUp = createReactComponent("outline", "arrow-bar-to-up", "IconArrowBarToUp", [["path", { "d": "M12 10l0 10", "key": "svg-0" }], ["path", { "d": "M12 10l4 4", "key": "svg-1" }], ["path", { "d": "M12 10l-4 4", "key": "svg-2" }], ["path", { "d": "M4 4l16 0", "key": "svg-3" }]]);

export {
  IconArrowBarToUp
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconArrowBarToUp.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XXNKPNGF.js.map
