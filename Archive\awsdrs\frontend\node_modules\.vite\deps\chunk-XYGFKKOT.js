import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconTextDecrease.mjs
var IconTextDecrease = createReactComponent("outline", "text-decrease", "IconTextDecrease", [["path", { "d": "M4 19v-10.5a3.5 3.5 0 1 1 7 0v10.5", "key": "svg-0" }], ["path", { "d": "M4 13h7", "key": "svg-1" }], ["path", { "d": "M21 12h-6", "key": "svg-2" }]]);

export {
  IconTextDecrease
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconTextDecrease.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XYGFKKOT.js.map
