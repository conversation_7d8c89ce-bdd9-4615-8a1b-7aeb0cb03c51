{"version": 3, "sources": ["../../@tabler/icons-react/src/icons/IconWindOff.ts"], "sourcesContent": ["import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'wind-off', 'IconWindOff', [[\"path\",{\"d\":\"M5 8h3m4 0h1.5a2.5 2.5 0 1 0 -2.34 -3.24\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M3 12h9\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M16 12h2.5a2.5 2.5 0 0 1 1.801 4.282\",\"key\":\"svg-2\"}],[\"path\",{\"d\":\"M4 16h5.5a2.5 2.5 0 1 1 -2.34 3.24\",\"key\":\"svg-3\"}],[\"path\",{\"d\":\"M3 3l18 18\",\"key\":\"svg-4\"}]]);"], "mappings": ";;;;;AACA,IAAA,cAAe,qBAAqB,WAAW,YAAY,eAAe,CAAC,CAAC,QAAO,EAAC,KAAI,4CAA2C,OAAM,QAAA,CAAQ,GAAE,CAAC,QAAO,EAAC,KAAI,WAAU,OAAM,QAAQ,CAAA,GAAE,CAAC,QAAO,EAAC,KAAI,wCAAuC,OAAM,QAAO,CAAC,GAAE,CAAC,QAAO,EAAC,KAAI,sCAAqC,OAAM,QAAA,CAAQ,GAAE,CAAC,QAAO,EAAC,KAAI,cAAa,OAAM,QAAQ,CAAA,CAAC,CAAC;", "names": []}