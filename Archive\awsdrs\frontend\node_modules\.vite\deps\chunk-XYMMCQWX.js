import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconNumber22Small.mjs
var IconNumber22Small = createReactComponent("outline", "number-22-small", "IconNumber22Small", [["path", { "d": "M14 8h3a1 1 0 0 1 1 1v2a1 1 0 0 1 -1 1h-2a1 1 0 0 0 -1 1v2a1 1 0 0 0 1 1h3", "key": "svg-0" }], ["path", { "d": "M6 8h3a1 1 0 0 1 1 1v2a1 1 0 0 1 -1 1h-2a1 1 0 0 0 -1 1v2a1 1 0 0 0 1 1h3", "key": "svg-1" }]]);

export {
  IconNumber22Small
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconNumber22Small.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XYMMCQWX.js.map
