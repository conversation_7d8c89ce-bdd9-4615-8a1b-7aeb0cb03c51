import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconNumber34Small.mjs
var IconNumber34Small = createReactComponent("outline", "number-34-small", "IconNumber34Small", [["path", { "d": "M14 8v3a1 1 0 0 0 1 1h3", "key": "svg-0" }], ["path", { "d": "M18 8v8", "key": "svg-1" }], ["path", { "d": "M6 8h2.5a1.5 1.5 0 0 1 1.5 1.5v1a1.5 1.5 0 0 1 -1.5 1.5h-1.5h1.5a1.5 1.5 0 0 1 1.5 1.5v1a1.5 1.5 0 0 1 -1.5 1.5h-2.5", "key": "svg-2" }]]);

export {
  IconNumber34Small
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconNumber34Small.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XYNX2E2Y.js.map
