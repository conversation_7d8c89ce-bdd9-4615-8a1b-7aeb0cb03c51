import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconWind.mjs
var IconWind = createReactComponent("outline", "wind", "IconWind", [["path", { "d": "M5 8h8.5a2.5 2.5 0 1 0 -2.34 -3.24", "key": "svg-0" }], ["path", { "d": "M3 12h15.5a2.5 2.5 0 1 1 -2.34 3.24", "key": "svg-1" }], ["path", { "d": "M4 16h5.5a2.5 2.5 0 1 1 -2.34 3.24", "key": "svg-2" }]]);

export {
  IconWind
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconWind.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XYR4Z3MF.js.map
