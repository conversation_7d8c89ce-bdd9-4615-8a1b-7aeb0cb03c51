import {
  createReactComponent
} from "./chunk-6RXP7JBB.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconDeviceTabletPause.mjs
var IconDeviceTabletPause = createReactComponent("outline", "device-tablet-pause", "IconDeviceTabletPause", [["path", { "d": "M13 21h-7a1 1 0 0 1 -1 -1v-16a1 1 0 0 1 1 -1h12a1 1 0 0 1 1 1v9.5", "key": "svg-0" }], ["path", { "d": "M17 17v5", "key": "svg-1" }], ["path", { "d": "M21 17v5", "key": "svg-2" }], ["path", { "d": "M11 17a1 1 0 1 0 2 0a1 1 0 0 0 -2 0", "key": "svg-3" }]]);

export {
  IconDeviceTabletPause
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconDeviceTabletPause.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-XZAAPPNG.js.map
