#!/bin/bash

# Installation script for Ubuntu 24.04 VM Setup Script
# This script sets up the main setup script to run automatically on first login
# Run this script as btaylor-admin user on your XenServer template before creating VMs
# IMPORTANT: This script expects vm-setup.sh to already exist in the home directory

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[HEADER]${NC} $1"
}

# Check if running as btaylor-admin
if [[ "$USER" != "btaylor-admin" ]]; then
    print_error "This script must be run as btaylor-admin user"
    exit 1
fi

print_header "Ubuntu 24.04 VM Setup Script Auto-run Configuration"
echo

# Check if vm-setup.sh already exists
SETUP_SCRIPT="$HOME/vm-setup.sh"

if [[ ! -f "$SETUP_SCRIPT" ]]; then
    print_error "vm-setup.sh not found in home directory!"
    print_error "Please create vm-setup.sh first before running this installer"
    echo
    print_status "Expected location: $SETUP_SCRIPT"
    print_status "You should:"
    echo "  1. Create vm-setup.sh with the main script content"
    echo "  2. Make it executable: chmod +x ~/vm-setup.sh"
    echo "  3. Then run this installer"
    exit 1
fi

# Check if script is executable
if [[ ! -x "$SETUP_SCRIPT" ]]; then
    print_warning "vm-setup.sh is not executable, fixing permissions..."
    chmod +x "$SETUP_SCRIPT"
    print_status "Made vm-setup.sh executable"
fi

# Show current script info
script_size=$(wc -l < "$SETUP_SCRIPT")
print_status "Found vm-setup.sh ($script_size lines)"

# Verify it's the correct script by checking for key functions
if ! grep -q "show_main_menu" "$SETUP_SCRIPT"; then
    print_warning "vm-setup.sh doesn't appear to contain the expected content"
    read -p "Continue anyway? (y/n): " continue_anyway
    if [[ ! $continue_anyway =~ ^[Yy]$ ]]; then
        print_status "Installation cancelled"
        exit 0
    fi
fi

# Configure auto-run to run in foreground on first login
print_status "Configuring auto-run on first login..."

# Remove any existing auto-run configurations and completion markers
sed -i '/# Auto-run VM setup script/,/fi/d' ~/.bashrc
rm -f ~/.config/systemd/user/vm-setup.service
rm -f ~/.config/autostart/vm-setup.desktop

# Remove completion marker so auto-run will work
if [[ -f ~/.vm-setup-completed ]]; then
    rm -f ~/.vm-setup-completed
    print_status "Removed completion marker - auto-run will be enabled"
fi

# Stop any running systemd services
systemctl --user stop vm-setup.service 2>/dev/null || true
systemctl --user disable vm-setup.service 2>/dev/null || true

# Add new configuration to .bashrc
cat >> ~/.bashrc << 'BASHRC_EOF'

# Auto-run VM setup script on first login
if [[ -f "$HOME/vm-setup.sh" ]] && [[ ! -f "$HOME/.vm-setup-completed" ]] && [[ $- == *i* ]] && [[ -t 0 ]]; then
    echo -e "\n\033[1;33m=== Ubuntu 24.04 VM Setup ===\033[0m"
    echo "This appears to be the first login. Starting setup..."
    "$HOME/vm-setup.sh"
fi
BASHRC_EOF

print_status "Auto-run configured to run in foreground on first login"

# Enable the service
if systemctl --user daemon-reload 2>/dev/null; then
    systemctl --user enable vm-setup.service 2>/dev/null
    print_status "Created and enabled systemd user service"
else
    print_warning "Could not enable systemd user service (this is normal for some environments)"
fi

# Create desktop autostart entry (for GUI sessions)
if [[ -n "$DISPLAY" ]] || [[ -n "$WAYLAND_DISPLAY" ]]; then
    print_status "Creating desktop autostart entry..."
    
    mkdir -p ~/.config/autostart
    
    cat > ~/.config/autostart/vm-setup.desktop << 'DESKTOP_EOF'
[Desktop Entry]
Type=Application
Name=VM Setup Script
Comment=Ubuntu 24.04 VM Initial Setup
Exec=gnome-terminal --title="VM Setup" -- /home/<USER>/vm-setup.sh
Hidden=false
NoDisplay=false
X-GNOME-Autostart-enabled=true
X-GNOME-Autostart-Delay=5
DESKTOP_EOF
    
    print_status "Created desktop autostart entry"
else
    print_status "No GUI detected, skipping desktop autostart entry"
fi

# Create helpful aliases
print_status "Adding helpful aliases..."

if ! grep -q "# VM Setup Aliases" ~/.bashrc; then
    cat >> ~/.bashrc << 'ALIAS_EOF'

# VM Setup Aliases
alias vm-setup='/home/<USER>/vm-setup.sh'
alias vm-info='hostnamectl && echo && ip addr show && echo && df -h'
alias vm-log='tail -f /var/log/vm-setup.log'
alias vm-status='systemctl --user status vm-setup.service 2>/dev/null || echo "Systemd service not available"'
ALIAS_EOF
    print_status "Added helpful aliases"
else
    print_status "Aliases already exist"
fi

# Test the main script
print_status "Testing vm-setup.sh script..."
if bash -n "$SETUP_SCRIPT"; then
    print_status "Script syntax is valid"
else
    print_error "Script has syntax errors!"
    print_warning "Please fix the script before cloning VMs"
fi

print_header "Installation Complete!"
echo
print_status "The VM setup script has been configured to run automatically"
print_status "on the next login for the btaylor-admin user."
echo
print_status "What happens next:"
echo "  1. The setup script will run automatically when btaylor-admin logs in"
echo "  2. User will see a menu-driven interface for configuration"
echo "  3. After completion, auto-run will be disabled (completion marker created)"
echo "  4. Script can still be run manually with: ./vm-setup.sh or 'vm-setup' alias"
echo "  5. To re-enable auto-run: run this installer again (removes completion marker)"
echo
print_status "Available aliases after next login:"
echo "  vm-setup    - Run the setup script manually"
echo "  vm-info     - Show system information"  
echo "  vm-log      - View setup log"
echo "  vm-status   - Check systemd service status"
echo
print_status "Template is ready for cloning!"
echo
print_warning "Remember to:"
echo "  - Test the setup on a clone before using in production"
echo "  - Customize the SSH keys section for your environment"
echo "  - Adjust firewall rules based on your requirements"
echo "  - Configure any additional software needed for your use case"
echo
print_status "Current vm-setup.sh: $script_size lines, executable: $(test -x "$SETUP_SCRIPT" && echo "Yes" || echo "No")"

read -p "Press [Enter] to complete installation..."