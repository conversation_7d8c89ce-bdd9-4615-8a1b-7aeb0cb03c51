#!/bin/bash

# Ubuntu 24.04 VM Initial Setup Script (with XenServer Storage Management and full Network Configuration)
# Based on vm-setup-3.sh, with XenServer Storage Management menu from vm-setup-36.sh and full original network logic
# Author: System Administrator
# Version: 1.3
# Description: Interactive menu-driven setup script for Ubuntu 24.04 VMs cloned from XenServer template, with XenServer Storage Management and full network configuration

# Check if we're being sourced or run directly
if [[ "$0" == "${BASH_SOURCE[0]}" ]]; then
    # Script is being run directly
    :
else
    # Script is being sourced (e.g., from .bashrc)
    # Don't run the menu automatically, just define the functions
    return 0
fi

# Ensure script is running in an interactive terminal
if [[ ! -t 0 ]] || [[ ! -t 1 ]] || [[ -z "$TERM" ]]; then
    echo "This script must be run in an interactive terminal." >&2
    echo "Please run it from a terminal or SSH session." >&2
    exit 1
fi

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Global variables
SCRIPT_NAME="vm-setup.sh"
LOG_FILE="/var/log/vm-setup.log"
CONFIG_FILE="$HOME/.vm-setup-config"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[HEADER]${NC} $1"
}

# Function to log actions
log_action() {
    local message="$1"
    # Only log to file, not to console
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $message" | sudo tee -a "$LOG_FILE" > /dev/null
}

# Function to check if running as intended user
check_user() {
    if [[ "$USER" != "btaylor-admin" ]]; then
        print_error "This script should be run as btaylor-admin user"
        exit 1
    fi
}

# Function to setup logging
setup_logging() {
    # Create log directory if it doesn't exist
    sudo mkdir -p "$(dirname "$LOG_FILE")"
    sudo touch "$LOG_FILE"
    sudo chown btaylor-admin:btaylor-admin "$LOG_FILE"
    # Rotate log if it gets too large (max 1MB)
    local log_size=$(stat -c %s "$LOG_FILE" 2>/dev/null || echo 0)
    if [[ $log_size -gt 1048576 ]]; then  # 1MB in bytes
        mv "$LOG_FILE" "${LOG_FILE}.old"
        touch "$LOG_FILE"
        chown btaylor-admin:btaylor-admin "$LOG_FILE"
    fi
    log_action "VM Setup Script Started [PID:$$]"
}

# Function to display main menu
show_main_menu() {
    clear
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║${WHITE}        Ubuntu 24.04 VM Initial Setup Script              ${CYAN}║${NC}"
    echo -e "${CYAN}║${WHITE}                   XenServer Template                     ${CYAN}║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════╝${NC}"
    echo
    echo -e "${WHITE}Current System Information:${NC}"
    echo -e "  Hostname: ${GREEN}$(hostname)${NC}"
    echo -e "  IP Address: ${GREEN}$(ip route get ******* | awk 'NR==1 {print $7}' 2>/dev/null || echo \"No IP\")${NC}"
    echo -e "  User: ${GREEN}$USER${NC}"
    echo
    echo -e "${WHITE}Setup Options:${NC}"
    echo -e "  ${YELLOW}1)${NC}  System Update & Upgrade"
    echo -e "  ${YELLOW}2)${NC}  Configure Hostname"
    echo -e "  ${YELLOW}3)${NC}  Network Configuration"
    echo -e "  ${YELLOW}4)${NC}  Configure SSH Keys"
    echo -e "  ${YELLOW}5)${NC}  Harden SSH Configuration"
    echo -e "  ${YELLOW}6)${NC}  Undo SSH Hardening"
    echo -e "  ${YELLOW}7)${NC}  Time and Date Settings"
    echo -e "  ${YELLOW}8)${NC}  Install Essential Software"
    echo -e "  ${YELLOW}9)${NC}  Security Hardening"
    echo -e "  ${YELLOW}10)${NC} Configure Passwordless Sudo"
    echo -e "  ${YELLOW}11)${NC} Setup Monitoring & Logging"
    echo -e "  ${YELLOW}12)${NC} Performance Optimization"
    echo -e "  ${YELLOW}13)${NC} XenServer Storage Management"
    echo -e "  ${YELLOW}14)${NC} Complete Setup (All Above)"
    echo -e "  ${YELLOW}15)${NC} System Information"
    echo -e "  ${YELLOW}16)${NC} Configure Firewall (UFW)"
    echo -e "  ${YELLOW}17)${NC} View Setup Log"
    echo
    echo -e "  ${RED}0)${NC}  Exit"
    echo
}

# Function to pause and wait for user input
pause() {
    echo
    read -p "Press [Enter] to continue..."
}

# Function to update system
update_system() {
    print_header "System Update & Upgrade"
    echo
    print_status "Updating package lists..."
    sudo apt update
    print_status "Upgrading system packages..."
    sudo apt upgrade -y
    print_status "Installing unattended upgrades..."
    sudo apt install -y unattended-upgrades
    sudo dpkg-reconfigure -plow unattended-upgrades
    print_status "Cleaning up..."
    sudo apt autoremove -y
    sudo apt autoclean
    if [ -f /var/run/reboot-required ]; then
        print_warning "System reboot is required after kernel updates"
        read -p "Would you like to reboot now? (y/n): " reboot_choice
        if [[ $reboot_choice =~ ^[Yy]$ ]]; then
            sudo reboot
        fi
    fi
    log_action "System update completed"
    print_status "System update completed successfully"
}

# Function to get current DNS servers from netplan configuration
get_current_dns() {
    local interface=$1
    local dns_servers=""
    
    if [ -z "$interface" ]; then
        interface=$(ip route get ******* 2>/dev/null | awk '{print $5; exit}')
    fi
    
    if [ -z "$interface" ] || [ ! -d "/etc/netplan" ]; then
        echo ""
        return 1
    fi
    
    # Try to read DNS from systemd-resolve first
    if command -v systemd-resolve &> /dev/null; then
        dns_servers=$(systemd-resolve --status | grep -A 5 "DNS Servers" | grep -v "DNS\|^$" | awk '{print $2}' | tr '\n' ',' | sed 's/,$//')
        [ -n "$dns_servers" ] && echo "$dns_servers" && return 0
    fi
    
    # Fall back to reading from netplan files with sudo
    for f in /etc/netplan/*.yaml; do
        if [ ! -f "$f" ] || [ ! -r "$f" ]; then
            continue
        fi
        
        # Use sudo to read the file if we don't have permission
        local file_content
        file_content=$(sudo cat "$f" 2>/dev/null)
        if [ $? -ne 0 ]; then
            continue
        fi
        
        # Try to extract DNS using grep from the file content
        dns_servers=$(echo "$file_content" | 
                     grep -A 10 "$interface:" | 
                     grep -A 5 "nameservers:" | 
                     grep -oP '(?<=addresses: \[).+?(?=\])' | 
                     tr -d ' ' | 
                     tr ',' '\n' | 
                     grep -v '^$' | 
                     tr '\n' ',' | 
                     sed 's/,$//')
                     
        [ -n "$dns_servers" ] && echo "$dns_servers" && return 0
    done
    
    # If we still don't have DNS, try reading from resolv.conf
    if [ -f "/run/systemd/resolve/resolv.conf" ]; then
        dns_servers=$(grep ^nameserver /run/systemd/resolve/resolv.conf 2>/dev/null | awk '{print $2}' | tr '\n' ',' | sed 's/,$//')
        [ -n "$dns_servers" ] && echo "$dns_servers" && return 0
    fi
    
    # Last resort: check /etc/resolv.conf
    if [ -f "/etc/resolv.conf" ]; then
        dns_servers=$(grep ^nameserver /etc/resolv.conf 2>/dev/null | awk '{print $2}' | tr '\n' ',' | sed 's/,$//')
        [ -n "$dns_servers" ] && echo "$dns_servers" && return 0
    fi
    
    echo ""
    return 1
}

# Function to configure hostname
configure_hostname() {
    print_header "Configure Hostname"
    current_hostname=$(hostname)
    print_status "Current hostname: $current_hostname"
    read -p "Enter new hostname: " new_hostname
    if [[ ! "$new_hostname" =~ ^[a-zA-Z0-9-]+$ ]]; then
        print_error "Invalid hostname. Use only letters, numbers, and hyphens."
        return 1
    fi
    print_status "Setting hostname to: $new_hostname"
    sudo hostnamectl set-hostname "$new_hostname"
    print_status "Updating /etc/hosts file..."
    sudo sed -i "s/*********.*/*********\t$new_hostname/" /etc/hosts
    log_action "Hostname changed from $current_hostname to $new_hostname"
    print_status "Hostname configuration completed"
}

# --- FULL NETWORK CONFIGURATION LOGIC FROM vm-setup-3.sh ---

configure_static_ip() {
    print_header "Network Configuration"
    echo
    # Get current network info
    interface=$(ip route | awk '/default/ {print $5}' | head -1)
    current_ip=$(ip route get ******* | awk 'NR==1 {print $7}' 2>/dev/null || echo "No IP")
    current_gateway=$(ip route | awk '/default/ {print $3}' | head -1)
    ipv6_status=$(cat /proc/sys/net/ipv6/conf/all/disable_ipv6 2>/dev/null || echo "0")
    # Show current network status
    print_status "Current Network Status:"
    echo -e "  Interface: ${GREEN}$interface${NC}"
    echo -e "  IP Address: ${GREEN}$current_ip${NC}"
    echo -e "  Gateway: ${GREEN}$current_gateway${NC}"
    echo -e "  IPv6: ${GREEN}$( [[ "$ipv6_status" == "0" ]] && echo "Enabled" || echo "Disabled" )${NC}"
    echo
    while true; do
        echo "Network Configuration Options:"
        echo "  1) Configure DHCP (Automatic IP)"
        echo "  2) Configure Static IP"
        echo "  3) Configure IPv6"
        echo "  4) Show Network Information"
        echo "  5) Test Network Connectivity"
        echo "  0) Return to Main Menu"
        echo
        read -p "Select network option [0-5]: " network_choice
        case $network_choice in
            1)
                configure_dhcp
                ;;
            2)
                configure_static_ip_settings
                ;;
            3)
                configure_ipv6_settings
                ;;
            4)
                show_network_information
                ;;
            5)
                test_network_connectivity
                ;;
            0)
                return 0
                ;;
            *)
                print_error "Invalid option. Please select 0-5."
                ;;
        esac
        echo
        pause
    done
}

configure_dhcp() {
    print_header "Configure DHCP"
    interface=$(ip route | awk '/default/ {print $5}' | head -1)
    
    # Get current DNS settings using our dedicated function
    current_dns=$(get_current_dns "$interface")
    
    # If no DNS found, use some sensible defaults
    if [ -z "$current_dns" ]; then
        current_dns="*******,*******"
        print_warning "No DNS servers found. Using default DNS: $current_dns"
    fi
    
    print_status "Current network interface: $interface"
    print_status "Current DNS servers: $current_dns"
    echo
    
    read -p "Enter DNS servers (comma-separated, e.g., *******,*******) [${current_dns:-None}]: " dns_servers_input
    dns_servers="${dns_servers_input:-$current_dns}"
    
    # Archive all existing netplan YAMLs
    print_status "Archiving existing netplan YAML files..."
    sudo mkdir -p /etc/netplan/backup
    for netplan_file in /etc/netplan/*.yaml; do
        if [[ -f "$netplan_file" ]]; then
            sudo mv "$netplan_file" /etc/netplan/backup/
        fi
    done
    
    # Create a temporary file with secure permissions
    temp_file=$(mktemp)
    chmod 600 "$temp_file"
    
    # Write DHCP config to temporary file
    cat > "$temp_file" <<EOF
network:
  version: 2
  ethernets:
    $interface:
      dhcp4: true
      dhcp6: false
EOF
    
    # Append DNS configuration if provided
    if [[ -n "$dns_servers" ]]; then
        cat >> "$temp_file" <<EOF
      nameservers:
        addresses: [${dns_servers//,/\, }]
EOF
    fi
    
    # Move the temporary file to the final location with proper permissions
    sudo cp "$temp_file" /etc/netplan/01-netcfg.yaml
    sudo chmod 600 /etc/netplan/01-netcfg.yaml
    rm -f "$temp_file"
    sudo netplan apply
    if [[ $? -eq 0 ]]; then
        print_status "DHCP configuration applied."
        echo "  Interface: $interface"
        echo "  Method: DHCP"
        log_action "Network configured for DHCP"
    else
        print_error "DHCP configuration failed. Reverting changes..."
        for backup_file in /etc/netplan/backup/*.yaml; do
            if [[ -f "$backup_file" ]]; then
                sudo mv "$backup_file" /etc/netplan/
            fi
        done
        sudo netplan apply 2>/dev/null
        print_error "DHCP configuration failed"
    fi
}

configure_static_ip_settings() {
    print_header "Configure Static IP"
    echo
    interface=$(ip route | awk '/default/ {print $5}' | head -1)
    current_ip=$(ip route get ******* | awk 'NR==1 {print $7}' 2>/dev/null)
    current_gateway=$(ip route | awk '/default/ {print $3}' | head -1)
    # Get current network configuration
    current_ip=$(ip -4 addr show dev "$interface" | grep -oP '(?<=inet\s)\d+(\.\d+){3}/\d+' | cut -d'/' -f1)
    current_cidr=$(ip -4 addr show dev "$interface" | grep -oP '(?<=inet\s)\d+(\.\d+){3}/\d+' | cut -d'/' -f2)
    current_gateway=$(ip route | awk '/default/ {print $3; exit}')
    
    # Get current DNS settings using our dedicated function
    current_dns=$(get_current_dns "$interface")
    
    # If no DNS found, use some sensible defaults
    if [ -z "$current_dns" ]; then
        current_dns="*******,*******"
        print_warning "No DNS servers found. Using default DNS: $current_dns"
    fi
    
    # Display current settings
    print_status "Current network interface: $interface"
    print_status "Current IP address: $current_ip"
    print_status "Current subnet mask (CIDR): /${current_cidr:-24}"
    print_status "Current gateway: $current_gateway"
    print_status "Current DNS servers: ${current_dns:-None configured}"
    echo
    
    # Get new values with current as defaults
    read -p "Enter static IP address [${current_ip:-*************}]: " static_ip
    static_ip=${static_ip:-$current_ip}
    
    read -p "Enter subnet mask in CIDR format [${current_cidr:-24}]: " subnet_mask
    subnet_mask=${subnet_mask:-$current_cidr}
    
    read -p "Enter gateway IP address [${current_gateway:-***********}]: " gateway_ip
    gateway_ip=${gateway_ip:-$current_gateway}
    
    read -p "Enter DNS servers (comma-separated, e.g., *******,*******) [${current_dns:-None}]: " dns_servers_input
    dns_servers="${dns_servers_input:-$current_dns}"
    # Archive all existing netplan YAMLs
    print_status "Archiving existing netplan YAML files..."
    sudo mkdir -p /etc/netplan/backup
    for netplan_file in /etc/netplan/*.yaml; do
        if [[ -f "$netplan_file" ]]; then
            sudo mv "$netplan_file" /etc/netplan/backup/
        fi
    done
    # Create a temporary file with secure permissions
    temp_file=$(mktemp)
    chmod 600 "$temp_file"
    
    # Write configuration to the temporary file
    cat > "$temp_file" <<EOF
network:
  version: 2
  ethernets:
    $interface:
      addresses: [$static_ip/$subnet_mask]
      dhcp4: false
      routes:
        - to: default
          via: $gateway_ip
EOF
    
    # Append DNS configuration if provided
    if [[ -n "$dns_servers" ]]; then
        cat >> "$temp_file" <<EOF
      nameservers:
        addresses: [${dns_servers//,/\, }]
EOF
    fi
    
    # Move the temporary file to the final location with proper permissions
    sudo cp "$temp_file" /etc/netplan/01-netcfg.yaml
    sudo chmod 600 /etc/netplan/01-netcfg.yaml
    rm -f "$temp_file"
    sudo netplan apply
    if [[ $? -eq 0 ]]; then
        print_status "Static IP configuration applied."
        echo "  Interface: $interface"
        echo "  IP Address: $static_ip/$subnet_mask"
        echo "  Default Route: $gateway_ip"
        echo "  DNS: $dns_servers"
        log_action "Static IP configured: $static_ip/$subnet_mask, Default Route: $gateway_ip, DNS: $dns_servers"
    else
        print_error "Static IP configuration failed. Reverting changes..."
        for backup_file in /etc/netplan/backup/*.yaml; do
            if [[ -f "$backup_file" ]]; then
                sudo mv "$backup_file" /etc/netplan/
            fi
        done
        sudo netplan apply 2>/dev/null
        print_error "Static IP configuration failed"
    fi
}

configure_ipv6_settings() {
    print_header "Configure IPv6"
    echo
    while true; do
        echo "IPv6 Configuration Options:"
        echo "  1) Enable IPv6"
        echo "  2) Disable IPv6"
        echo "  0) Return"
        echo
        read -p "Select IPv6 option [0-2]: " ipv6_choice
        case $ipv6_choice in
            1)
                enable_ipv6
                ;;
            2)
                disable_ipv6
                ;;
            0)
                return 0
                ;;
            *)
                print_error "Invalid option. Please select 0-2."
                ;;
        esac
        pause
    done
}

disable_ipv6() {
    print_header "Disable IPv6"
    echo
    # Disable at runtime
    sudo sysctl -w net.ipv6.conf.all.disable_ipv6=1
    sudo sysctl -w net.ipv6.conf.default.disable_ipv6=1
    sudo sysctl -w net.ipv6.conf.lo.disable_ipv6=1
    # Make persistent
    sudo tee /etc/sysctl.d/99-disable-ipv6.conf > /dev/null <<EOF
net.ipv6.conf.all.disable_ipv6 = 1
net.ipv6.conf.default.disable_ipv6 = 1
net.ipv6.conf.lo.disable_ipv6 = 1
EOF
    sudo sysctl --system
    print_status "IPv6 disabled (runtime and persistent)."
    log_action "IPv6 disabled (runtime and persistent)"
}

enable_ipv6() {
    print_header "Enable IPv6"
    echo
    # Remove persistent disable config if it exists
    if [[ -f /etc/sysctl.d/99-disable-ipv6.conf ]]; then
        sudo rm /etc/sysctl.d/99-disable-ipv6.conf
        print_status "Removed persistent IPv6 disable config."
    fi
    # Enable at runtime
    sudo sysctl -w net.ipv6.conf.all.disable_ipv6=0
    sudo sysctl -w net.ipv6.conf.default.disable_ipv6=0
    sudo sysctl -w net.ipv6.conf.lo.disable_ipv6=0
    sudo sysctl --system
    print_status "IPv6 enabled (runtime and persistent)."
    log_action "IPv6 enabled (runtime and persistent)"
}

show_network_information() {
    print_header "Network Information"
    echo
    ip addr show
    ip route show
    cat /etc/netplan/*.yaml 2>/dev/null
}

test_network_connectivity() {
    print_header "Test Network Connectivity"
    echo
    read -p "Enter host/IP to ping (default: *******): " ping_target
    ping_target=${ping_target:-*******}
    ping -c 4 "$ping_target"
    if [[ $? -eq 0 ]]; then
        print_status "Ping successful"
    else
        print_error "Ping failed"
    fi
}

# --- END NETWORK CONFIGURATION LOGIC ---

# Function to configure SSH keys
configure_ssh_keys() {
    print_header "Configure SSH Keys"
    read -p "Do you want to generate new SSH keys? [y/n]: " generate_keys
    if [[ $generate_keys =~ ^[Yy]$ ]]; then
        read -p "Enter email for SSH key comment: " email
        ssh-keygen -t ed25519 -C "$email"
        print_status "SSH keys generated successfully"
    fi
    read -p "Do you want to add a public key for remote access? [y/n]: " add_remote_key
    if [[ $add_remote_key =~ ^[Yy]$ ]]; then
        echo "Please paste the public key:"
        read -r public_key
        mkdir -p ~/.ssh
        chmod 700 ~/.ssh
        echo "$public_key" >> ~/.ssh/authorized_keys
        chmod 600 ~/.ssh/authorized_keys
        print_status "Public key added to authorized_keys"
        log_action "SSH public key added for remote access"
    fi
}

# Function to harden SSH configuration
harden_ssh() {
    # This function applies SSH security hardening:
    # - Prompts for and sets a new SSH port (default 22)
    # - Optionally disables password authentication (PasswordAuthentication no)
    # - Optionally disables root login (PermitRootLogin no)
    # - Reloads SSH daemon to apply changes
    # - Creates a backup of /etc/ssh/sshd_config before changes
    # NOTE: Disabling password authentication without SSH keys may prevent file transfers via scp/sftp unless keys are set up.
    print_header "Harden SSH Configuration"
    echo
    print_status "Configuring SSH security settings..."
    sudo cp /etc/ssh/sshd_config /etc/ssh/sshd_config.bak-$(date +%F-%H%M%S)
    read -p "Enter new SSH port (default 22): " ssh_port
    if [[ -z "$ssh_port" ]]; then
        ssh_port=22
    fi
    sudo sed -i "s/^#Port .*/Port $ssh_port/;s/^Port .*/Port $ssh_port/" /etc/ssh/sshd_config
    read -p "Disable password authentication? (y/n): " disable_pass
    if [[ $disable_pass =~ ^[Yy]$ ]]; then
        sudo sed -i "s/^#PasswordAuthentication .*/PasswordAuthentication no/;s/^PasswordAuthentication .*/PasswordAuthentication no/" /etc/ssh/sshd_config
    fi
    read -p "Disable root login? (y/n): " disable_root
    if [[ $disable_root =~ ^[Yy]$ ]]; then
        sudo sed -i "s/^#PermitRootLogin .*/PermitRootLogin no/;s/^PermitRootLogin .*/PermitRootLogin no/" /etc/ssh/sshd_config
    fi
    sudo systemctl reload sshd
    print_status "SSH hardening complete."
    log_action "SSH configuration hardened (port $ssh_port, password auth: $disable_pass, root login: $disable_root)"
}

# Function to configure firewall
configure_firewall() {
    print_header "Configure Firewall (UFW)"
    echo
    print_status "Installing UFW if needed..."
    sudo apt install -y ufw
    sudo ufw allow OpenSSH
    read -p "Enter any additional ports to allow (comma-separated, e.g. 80,443), or leave blank: " extra_ports
    if [[ -n "$extra_ports" ]]; then
        IFS=',' read -ra PORTS <<< "$extra_ports"
        for port in "${PORTS[@]}"; do
            sudo ufw allow $port
        done
    fi
    sudo ufw enable
    sudo ufw status verbose
    log_action "Firewall configured (UFW)"
    print_status "Firewall configuration complete."
}

# Function to set timezone
set_timezone() {
    print_header "Set Timezone"
    echo
    print_status "Current timezone: $(timedatectl | grep 'Time zone')"
    read -p "Enter desired timezone (e.g. America/New_York): " tz
    if [[ -n "$tz" ]]; then
        if sudo timedatectl set-timezone "$tz"; then
            print_status "Timezone set to $tz"
            log_action "Timezone set to $tz"
        else
            print_error "Failed to set timezone. Please check the timezone format and try again."
        fi
    else
        print_warning "No timezone entered. Skipping."
    fi
}

# Function to set date and time
set_datetime() {
    print_header "Set Date and Time"
    echo
    print_status "Current date and time: $(date)"
    echo "1) Set date and time manually (YYYY-MM-DD HH:MM:SS)"
    echo "2) Sync with NTP and set automatically"
    read -p "Choose an option [1-2]: " datetime_choice
    
    case $datetime_choice in
        1)
            read -p "Enter date and time (YYYY-MM-DD HH:MM:SS): " datetime
            if [[ -n "$datetime" ]]; then
                if sudo timedatectl set-time "$datetime"; then
                    print_status "System date and time set to: $(date)"
                    log_action "System date and time set manually to $datetime"
                else
                    print_error "Failed to set date and time. Please check the format and try again."
                fi
            fi
            ;;
        2)
            configure_ntp
            ;;
        *)
            print_error "Invalid option. Returning to menu."
            ;;
    esac
}

# Function to configure NTP
configure_ntp() {
    print_header "Configure NTP"
    echo
    print_status "Current NTP status: $(timedatectl show -p NTP --value)"
    echo "1) Enable NTP synchronization"
    echo "2) Disable NTP synchronization"
    echo "3) Set custom NTP servers"
    read -p "Choose an option [1-3]: " ntp_choice
    
    case $ntp_choice in
        1)
            sudo timedatectl set-ntp true
            print_status "NTP synchronization enabled."
            log_action "NTP synchronization enabled"
            ;;
        2)
            sudo timedatectl set-ntp false
            print_status "NTP synchronization disabled."
            log_action "NTP synchronization disabled"
            ;;
        3)
            read -p "Enter NTP servers (space-separated, e.g., 0.ubuntu.pool.ntp.org 1.ubuntu.pool.ntp.org): " ntp_servers
            if [[ -n "$ntp_servers" ]]; then
                # Backup current config
                sudo cp /etc/systemd/timesyncd.conf /etc/systemd/timesyncd.conf.backup.$(date +%s)
                # Set new NTP servers
                echo -e "[Time]\nNTP=${ntp_servers// / }" | sudo tee /etc/systemd/timesyncd.conf > /dev/null
                sudo systemctl restart systemd-timesyncd
                sudo timedatectl set-ntp true
                print_status "NTP servers updated to: ${ntp_servers// /, }"
                log_action "NTP servers updated to: ${ntp_servers// /, }"
            fi
            ;;
        *)
            print_error "Invalid option. Returning to menu."
            ;;
    esac
}

# Time and Date Management Menu
time_date_menu() {
    while true; do
        print_header "Time and Date Management"
        echo
        echo "Current system time: $(date)"
        echo "Current timezone: $(timedatectl | grep 'Time zone' | cut -d':' -f2-)"
        echo "NTP status: $(timedatectl show -p NTP --value)"
        echo
        echo "1) Set Timezone"
        echo "2) Set Date and Time"
        echo "3) Configure NTP"
        echo "4) View Current Time/Date Info"
        echo "0) Return to Main Menu"
        echo
        read -p "Select an option [0-4]: " time_choice
        
        case $time_choice in
            1) set_timezone ;;
            2) set_datetime ;;
            3) configure_ntp ;;
            4) 
                clear
                timedatectl status
                echo -e "\nPress Enter to continue..."
                read
                ;;
            0) return 0 ;;
            *) print_error "Invalid option. Please try again." ;;
        esac
        
        # Pause to show output before returning to menu
        if [[ "$time_choice" != "0" ]]; then
            echo -e "\nPress Enter to continue..."
            read
        fi
    done
}

# Function to install essential software
install_essential_software() {
    print_header "Install Essential Software"
    echo
    declare -A basic_tools=(
        ["curl"]="Command line tool for transferring data"
        ["wget"]="Network downloader"
        ["git"]="Version control system"
        ["vim"]="Advanced text editor"
        ["nano"]="Simple text editor"
        ["tree"]="Directory structure display"
        ["unzip"]="Extract ZIP archives"
        ["zip"]="Create ZIP archives"
        ["p7zip-full"]="7-Zip archive support"
    )
    declare -A system_tools=(
        ["htop"]="Interactive process viewer"
        ["iotop"]="I/O usage monitor"
        ["ncdu"]="Disk usage analyzer"
        ["lsof"]="List open files"
        ["strace"]="System call tracer"
        ["sysstat"]="System performance tools"
        ["logrotate"]="Log file rotation"
        ["fail2ban"]="Intrusion prevention"
    )
    declare -A dev_tools=(
        ["build-essential"]="Compiler and build tools"
        ["python3-pip"]="Python package manager"
        ["python3-venv"]="Python virtual environments"
        ["jq"]="JSON processor"
        ["shellcheck"]="Shell script analysis"
        ["gcc"]="GNU Compiler Collection"
        ["make"]="Build automation tool"
    )
    declare -A security_tools=(
        ["ufw"]="Uncomplicated Firewall"
        ["gnupg"]="GNU Privacy Guard"
        ["apt-transport-https"]="APT HTTPS transport"
        ["ca-certificates"]="CA certificates"
    )
    while true; do
        echo
        echo "Select software category to install/check:"
        echo "  1) Basic Tools"
        echo "  2) System Tools"
        echo "  3) Development Tools"
        echo "  4) Security Tools"
        echo "  5) Install All"
        echo "  0) Return to Main Menu"
        read -p "Choice: " sw_choice
        case $sw_choice in
            1)
                for pkg in "${!basic_tools[@]}"; do
                    if is_package_installed "$pkg"; then
                        echo -e "${GREEN}✓${NC} $pkg"
                    else
                        print_status "Installing $pkg..."
                        sudo apt install -y "$pkg"
                    fi
                done
                ;;
            2)
                for pkg in "${!system_tools[@]}"; do
                    if is_package_installed "$pkg"; then
                        echo -e "${GREEN}✓${NC} $pkg"
                    else
                        print_status "Installing $pkg..."
                        sudo apt install -y "$pkg"
                    fi
                done
                ;;
            3)
                for pkg in "${!dev_tools[@]}"; do
                    if is_package_installed "$pkg"; then
                        echo -e "${GREEN}✓${NC} $pkg"
                    else
                        print_status "Installing $pkg..."
                        sudo apt install -y "$pkg"
                    fi
                done
                ;;
            4)
                for pkg in "${!security_tools[@]}"; do
                    if is_package_installed "$pkg"; then
                        echo -e "${GREEN}✓${NC} $pkg"
                    else
                        print_status "Installing $pkg..."
                        sudo apt install -y "$pkg"
                    fi
                done
                ;;
            5)
                all_essential=("${!basic_tools[@]}" "${!system_tools[@]}" "${!dev_tools[@]}" "${!security_tools[@]}")
                for pkg in "${all_essential[@]}"; do
                    if is_package_installed "$pkg"; then
                        echo -e "${GREEN}✓${NC} $pkg"
                    else
                        print_status "Installing $pkg..."
                        sudo apt install -y "$pkg"
                    fi
                done
                ;;
            0)
                break
                ;;
            *)
                print_error "Invalid option."
                ;;
        esac
    done
    log_action "Software management completed"
    print_status "Software management completed"
}

# Function for security hardening
security_hardening() {
    print_header "Security Hardening"
    echo
    print_status "Configuring Fail2Ban..."
    sudo cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local
    ssh_port=$(sudo grep "^Port" /etc/ssh/sshd_config | awk '{print $2}' || echo "22")
    sudo tee /etc/fail2ban/jail.d/sshd.conf > /dev/null <<EOF
[sshd]
enabled = true
port = $ssh_port
filter = sshd
logpath = /var/log/auth.log
maxretry = 3
bantime = 3600
findtime = 600
EOF
    sudo systemctl enable fail2ban
    sudo systemctl restart fail2ban
    print_status "Disabling unused services..."
    for service in avahi-daemon cups; do
        if systemctl is-active --quiet $service; then
            sudo systemctl disable "$service"
            sudo systemctl stop "$service"
            print_status "Disabled $service"
        fi
    done
    print_status "Configuring automatic security updates..."
    sudo tee /etc/apt/apt.conf.d/50unattended-upgrades > /dev/null <<EOF
Unattended-Upgrade::Automatic-Reboot "false";
Unattended-Upgrade::Automatic-Reboot-Time "02:00";
Unattended-Upgrade::Remove-Unused-Dependencies "true";
Unattended-Upgrade::Remove-New-Unused-Dependencies "true";
EOF
    log_action "Security hardening completed"
    print_status "Security hardening completed"
}

# Function to configure passwordless sudo
configure_passwordless_sudo() {
    print_header "Configure Passwordless Sudo"
    echo
    print_warning "This will allow btaylor-admin to run sudo commands without a password"
    read -p "Are you sure you want to continue? (y/n): " confirm
    if [[ $confirm =~ ^[Yy]$ ]]; then
        echo "btaylor-admin ALL=(ALL) NOPASSWD:ALL" | sudo tee /etc/sudoers.d/btaylor-admin-nopasswd > /dev/null
        sudo chmod 440 /etc/sudoers.d/btaylor-admin-nopasswd
        print_status "Passwordless sudo configured for btaylor-admin."
        log_action "Passwordless sudo enabled for btaylor-admin"
    else
        print_status "Passwordless sudo configuration skipped."
    fi
}

# Function to setup monitoring and logging
setup_monitoring() {
    print_header "Setup Monitoring & Logging"
    echo
    print_status "Installing sysstat and logrotate..."
    sudo apt install -y sysstat logrotate
    sudo systemctl enable sysstat
    sudo systemctl start sysstat
    print_status "Sysstat enabled."
    read -p "Configure logwatch for email reports? (y/n): " logwatch_choice
    if [[ $logwatch_choice =~ ^[Yy]$ ]]; then
        sudo apt install -y logwatch
        read -p "Enter email address for log reports: " log_email
        if [[ -n "$log_email" ]]; then
            sudo sed -i "s/^MailTo =.*/MailTo = $log_email/" /usr/share/logwatch/default.conf/logwatch.conf
            print_status "Logwatch configured to email: $log_email"
        fi
    fi
    log_action "Monitoring and logging configured"
    print_status "Monitoring and logging setup complete."
}

# Function for performance optimization
performance_optimization() {
    print_header "Performance Optimization"
    echo
    echo "Select optimization profile:"
    echo "  1) XenServer Base Optimizations"
    echo "  2) General Purpose"
    echo "  3) PostgreSQL Database Server"
    echo "  4) React Frontend/Web Server"
    echo "  0) Return to Main Menu"
    read -p "Choice: " perf_choice
    case $perf_choice in
        1)
            print_status "Applying XenServer base optimizations..."
            sudo tee /etc/sysctl.d/99-xenserver.conf > /dev/null <<EOF
# XenServer 8.3/Ubuntu 24.04 base optimizations
vm.swappiness=1
vm.vfs_cache_pressure=50
fs.file-max=2097152
net.core.somaxconn=1024
net.ipv4.tcp_fin_timeout=15
net.ipv4.tcp_keepalive_time=300
EOF
            sudo sysctl --system
            ;;
        2)
            print_status "Applying general purpose optimizations..."
            sudo tee /etc/sysctl.d/99-general-purpose.conf > /dev/null <<EOF
# General purpose optimizations
vm.swappiness=10
vm.dirty_ratio=15
vm.dirty_background_ratio=5
fs.file-max=1048576
EOF
            sudo sysctl --system
            ;;
        3)
            print_status "Applying PostgreSQL optimizations..."
            sudo tee /etc/sysctl.d/99-postgresql.conf > /dev/null <<EOF
# PostgreSQL DB server optimizations
kernel.shmmax=8589934592
kernel.shmall=2097152
vm.swappiness=1
EOF
            sudo sysctl --system
            ;;
        4)
            print_status "Applying React frontend/web server optimizations..."
            sudo tee /etc/sysctl.d/99-react.conf > /dev/null <<EOF
# React frontend/web server optimizations
fs.inotify.max_user_watches=524288
fs.inotify.max_user_instances=1024
EOF
            sudo sysctl --system
            ;;
        0)
            return
            ;;
        *)
            print_error "Invalid option."
            ;;
    esac
    log_action "Performance optimization applied (profile $perf_choice)"
    print_status "Performance optimization complete."
}

# Function for backup configuration (kept from original option 13)
setup_backup() {
    print_header "Backup Configuration"
    echo
    read -p "Enter backup destination directory (e.g. /mnt/backup): " backup_dest
    if [[ -z "$backup_dest" ]]; then
        print_warning "No backup destination entered. Skipping."
        return
    fi
    backup_script="$HOME/backup_vm.sh"
    cat > "$backup_script" <<EOF
#!/bin/bash
BACKUP_DIR="$backup_dest"
DATE=[31m$(date +%F-%H%M%S)[0m
HOSTNAME=[31m$(hostname)[0m
tar -czf "\${BACKUP_DIR}/\${HOSTNAME}-backup-\${DATE}.tar.gz" \
    /etc /home /var/log /opt
EOF
    chmod +x "$backup_script"
    print_status "Backup script created at $backup_script"
    read -p "Schedule daily backup with cron? (y/n): " cron_choice
    if [[ $cron_choice =~ ^[Yy]$ ]]; then
        (crontab -l 2>/dev/null; echo "0 2 * * * $backup_script") | crontab -
        print_status "Daily backup scheduled at 2:00 AM."
    fi
    log_action "Backup configured to $backup_dest"
    print_status "Backup configuration complete."
}

# XenServer Storage Management (from vm-setup-36.sh option 13)
xenserver_storage_management() {
    print_header "XenServer VM Storage Management"
    print_status "Storage management for Ubuntu VMs on XenServer"
    while true; do
        echo "XenServer Storage Management Options:"
        echo "  1) Scan for New/Expanded Disks"
        echo "  2) Expand Root Filesystem"
        echo "  3) Setup New Data Volume"
        echo "  4) View Storage Information"
        echo "  0) Return to Main Menu"
        echo
        read -p "Select storage option [0-4]: " storage_choice
        case $storage_choice in
            1)
                scan_for_new_disks
                ;;
            2)
                expand_root_filesystem
                ;;
            3)
                setup_new_data_volume
                ;;
            4)
                view_storage_information
                ;;
            0)
                return 0
                ;;
            *)
                print_error "Invalid option. Please select 0-4."
                ;;
        esac
        pause
    done
}

scan_for_new_disks() {
    print_header "Scan for New/Expanded Disks"
    print_status "Current disk information:"
    lsblk -f
    print_status "Rescanning SCSI bus for new devices..."
    for host in /sys/class/scsi_host/host*; do
        if [[ -f "$host/scan" ]]; then
            echo "- - -" | sudo tee "$host/scan" > /dev/null
        fi
    done
    print_status "Rereading partition tables..."
    for disk in /dev/sd* /dev/xvd*; do
        if [[ -b "$disk" ]] && [[ ! "$disk" =~ [0-9]$ ]]; then
            sudo partprobe "$disk" 2>/dev/null || true
        fi
    done
    sleep 2
    print_status "Updated disk information:"
    lsblk -f
    log_action "Disk scan completed"
    print_status "Disk scan completed"
}

expand_root_filesystem() {
    print_header "Expand Root Filesystem"
    root_device=$(df / | tail -1 | awk '{print $1}')
    root_disk=$(echo "$root_device" | sed 's/[0-9]*$//')
    print_status "Root filesystem: $root_device"
    print_status "Root disk: $root_disk"
    print_status "Current root filesystem usage:"
    df -h /
    print_warning "This will attempt to expand the root partition"
    read -p "Continue with root filesystem expansion? [y/n]: " confirm_expand
    if [[ ! $confirm_expand =~ ^[Yy]$ ]]; then
        print_status "Root filesystem expansion cancelled"
        return 0
    fi
    partition_num=$(echo "$root_device" | grep -o '[0-9]*$')
    print_status "Expanding partition $partition_num on $root_disk..."
    if sudo growpart "$root_disk" "$partition_num"; then
        print_status "Partition expanded successfully"
        print_status "Resizing filesystem..."
        if [[ $(df -T / | tail -1 | awk '{print $2}') == "ext4" ]]; then
            sudo resize2fs "$root_device"
        elif [[ $(df -T / | tail -1 | awk '{print $2}') == "xfs" ]]; then
            sudo xfs_growfs /
        fi
        print_status "Root filesystem expansion completed!"
        df -h /
        log_action "Root filesystem expanded"
    else
        print_error "Failed to expand partition"
    fi
}

setup_new_data_volume() {
    print_header "Setup New Data Volume"
    print_status "Available unpartitioned disks:"
    unpartitioned_disks=()
    for disk in /dev/sd* /dev/xvd*; do
        if [[ -b "$disk" ]] && [[ ! "$disk" =~ [0-9]$ ]]; then
            if ! sudo fdisk -l "$disk" 2>/dev/null | grep -q "^$disk"; then
                disk_size=$(sudo fdisk -l "$disk" 2>/dev/null | grep "Disk $disk" | awk '{print $3}')
                echo "  $disk ($disk_size GB)"
                unpartitioned_disks+=("$disk")
            fi
        fi
    done
    if [[ ${#unpartitioned_disks[@]} -eq 0 ]]; then
        print_warning "No unpartitioned disks found"
        return 0
    fi
    read -p "Enter the disk to setup [example: /dev/sdb]: " selected_disk
    read -p "Enter mount point [example: /data]: " mount_point
    read -p "Enter volume label [example: data]: " volume_label
    print_warning "This will DESTROY all data on $selected_disk"
    read -p "Type YES to confirm: " confirm_setup
    if [[ "$confirm_setup" != "YES" ]]; then
        print_status "Data volume setup cancelled"
        return 0
    fi
    print_status "Setting up data volume on $selected_disk..."
    sudo parted "$selected_disk" --script mklabel gpt
    sudo parted "$selected_disk" --script mkpart primary ext4 0% 100%
    partition="${selected_disk}1"
    if [[ "$selected_disk" =~ nvme ]]; then
        partition="${selected_disk}p1"
    fi
    sleep 2
    print_status "Creating ext4 filesystem..."
    sudo mkfs.ext4 -L "$volume_label" "$partition"
    print_status "Creating mount point: $mount_point"
    sudo mkdir -p "$mount_point"
    print_status "Mounting volume..."
    sudo mount "$partition" "$mount_point"
    print_status "Adding to /etc/fstab..."
    uuid=$(sudo blkid -s UUID -o value "$partition")
    echo "UUID=$uuid $mount_point ext4 defaults 0 2" | sudo tee -a /etc/fstab
    sudo chown btaylor-admin:btaylor-admin "$mount_point"
    sudo chmod 755 "$mount_point"
    print_status "Data volume setup completed!"
    echo "  Device: $partition"
    echo "  Mount Point: $mount_point"
    echo "  Label: $volume_label"
    echo "  UUID: $uuid"
    df -h "$mount_point"
    log_action "New data volume created: $partition at $mount_point"
}

view_storage_information() {
    print_header "Storage Information"
    echo "Block Devices:"
    lsblk -f
    echo
    echo "Disk Usage:"
    df -h
    echo
    echo "Mount Points:"
    mount | grep -E "^/dev" | sort
    echo
    echo "Fstab Entries:"
    grep -v "^#" /etc/fstab | grep -v "^$"
}

# Function to show system information
show_system_info() {
    print_header "System Information"
    echo
    echo -e "${WHITE}System Details:${NC}"
    hostnamectl
    echo
    echo -e "${WHITE}Network Information:${NC}"
    ip addr show | grep -E "(inet|ether)" | grep -v "127.0.0.1"
    echo
    echo -e "${WHITE}Storage Information:${NC}"
    df -h | grep -E "(Filesystem|/dev)"
    echo
    echo -e "${WHITE}Memory Information:${NC}"
    free -h
    echo
    echo -e "${WHITE}SSH Configuration:${NC}"
    sudo grep -E "(Port|PasswordAuthentication|PermitRootLogin)" /etc/ssh/sshd_config
    echo
    echo -e "${WHITE}Firewall Status:${NC}"
    sudo ufw status
    echo
    echo -e "${WHITE}Services Status:${NC}"
    systemctl is-active ssh fail2ban ufw
}

# Function to view setup log
view_log() {
    print_header "Setup Log"
    echo
    if [[ -f "$LOG_FILE" ]]; then
        tail -n 50 "$LOG_FILE"
    else
        print_warning "Log file not found"
    fi
}

# Function to save current progress
save_progress() {
    cat > "$CONFIG_FILE" <<EOF
# VM Setup Progress
LAST_RUN=$(date)
SCRIPT_VERSION=1.2
EOF
}

# Function to remove this script from login
remove_from_login() {
    print_header "Setup Complete"
    echo
    print_status "Removing auto-run on login..."

    # Create a backup of the original .bashrc
    cp ~/.bashrc ~/.bashrc.bak-$(date +%Y%m%d-%H%M%S) 2>/dev/null || true

    # Remove the complete auto-run block (multi-line)
    if grep -q "Auto-run VM setup script" ~/.bashrc; then
        # Remove the entire auto-run block including the if statement
        sed -i '/# Auto-run VM setup script on first login/,/^fi$/d' ~/.bashrc
        print_status "Removed auto-run block from .bashrc"
    fi

    # Remove any orphaned echo statements
    sed -i '/echo.*Starting Ubuntu.*VM Setup/d' ~/.bashrc

    # Clean up .profile as well
    if [ -f ~/.profile ] && grep -q "vm-setup.sh" ~/.profile; then
        sed -i '/# Auto-run VM setup script/,/^fi$/d' ~/.profile
        sed -i '/echo.*Starting Ubuntu.*VM Setup/d' ~/.profile
        print_status "Removed from .profile"
    fi

    # Stop and disable systemd user service
    if systemctl --user is-active vm-setup.service >/dev/null 2>&1; then
        systemctl --user stop vm-setup.service 2>/dev/null || true
        systemctl --user disable vm-setup.service 2>/dev/null || true
        print_status "Stopped and disabled systemd service"
    fi

    # Remove systemd service file
    if [ -f ~/.config/systemd/user/vm-setup.service ]; then
        rm -f ~/.config/systemd/user/vm-setup.service
        systemctl --user daemon-reload 2>/dev/null || true
        print_status "Removed systemd service file"
    fi

    # Remove desktop autostart entry
    if [ -f ~/.config/autostart/vm-setup.desktop ]; then
        rm -f ~/.config/autostart/vm-setup.desktop
        print_status "Removed desktop autostart entry"
    fi

    # Create completion marker
    touch ~/.vm-setup-completed
    save_progress
    log_action "Setup script completely removed from auto-run"

    # Validate the .bashrc syntax
    if ! bash -n ~/.bashrc 2>/dev/null; then
        print_warning "Warning: .bashrc has syntax errors, restoring from backup"
        cp ~/.bashrc ~/.bashrc.broken-$(date +%Y%m%d-%H%M%S) 2>/dev/null || true
        # Find the most recent backup
        latest_backup=$(ls -t ~/.bashrc.bak-* 2>/dev/null | head -1)
        if [ -n "$latest_backup" ]; then
            cp "$latest_backup" ~/.bashrc
            print_status "Restored .bashrc from backup: $latest_backup"
        fi
    fi

    print_status "Setup script will no longer run automatically on login"
    print_status "You can still run it manually: ./$SCRIPT_NAME or use the 'vm-setup' alias"
}

# Cleanup function to be called on exit
cleanup() {
    local exit_code=$?
    log_action "Script exiting with code $exit_code"
    # Remove lock file if it exists and we're the owner
    if [[ -f "/tmp/vm-setup.lock" ]]; then
        local lock_pid
        lock_pid=$(cat "/tmp/vm-setup.lock" 2>/dev/null)
        if [[ "$lock_pid" == "$$ " ]]; then
            rm -f "/tmp/vm-setup.lock"
            log_action "Lock file removed"
        fi
    fi
    exit $exit_code
}

# Main function
main() {
    # Set up trap for cleanup on exit
    trap cleanup EXIT
    
    # Check for another instance
    if ! (set -o noclobber; echo "$$ " > "/tmp/vm-setup.lock") 2>/dev/null; then
        local other_pid
        other_pid=$(cat "/tmp/vm-setup.lock" 2>/dev/null | tr -d '\0')
        if kill -0 "$other_pid" 2>/dev/null; then
            echo -e "${RED}[ERROR]${NC} Another instance of the setup script is already running (PID: $other_pid)" >&2
            exit 1
        else
            # Stale lock file, remove it
            rm -f "/tmp/vm-setup.lock"
            echo "$$ " > "/tmp/vm-setup.lock"
        fi
    fi
    
    check_user
    setup_logging
    log_action "Script started in interactive mode"
    while true; do
        show_main_menu
        read -p "Select an option [0-17]: " choice
        case $choice in
            1) update_system ;;
            2) configure_hostname ;;
            3) configure_static_ip ;;
            4) configure_ssh_keys ;;
            5) harden_ssh ;;
            6) undo_ssh_hardening ;;
            7) time_date_menu ;;
            8) install_essential_software ;;
            9) security_hardening ;;
            10) configure_passwordless_sudo ;;
            11) setup_monitoring ;;
            12) performance_optimization ;;
            13) xenserver_storage_management ;;
            14) complete_setup ;;
            15) show_system_info ;;
            16) configure_firewall ;;
            17) view_log ;;
            0)
                print_status "Exiting setup script"
                read -p "Mark setup as complete and remove auto-run? (y/n): " remove_autorun
                if [[ $remove_autorun =~ ^[Yy]$ ]]; then
                    remove_from_login
                else
                    # Just exit cleanly without modifying .bashrc
                    log_action "Setup script exited (auto-run preserved)"
                fi
                exit 0
                ;;
            *)
                print_error "Invalid option. Please select 0-17."
                ;;
        esac
        pause
    done
}

undo_ssh_hardening() {
    print_header "Undo SSH Hardening"
    echo
    # Find the most recent backup created by harden_ssh
    backup_file=$(ls -t /etc/ssh/sshd_config.bak-* 2>/dev/null | head -n 1)
    if [[ -z "$backup_file" ]]; then
        print_error "No SSH config backup found. Cannot undo."
        return 1
    fi
    print_status "Restoring SSH config from backup: $backup_file"
    sudo cp "$backup_file" /etc/ssh/sshd_config
    sudo systemctl reload sshd
    log_action "SSH hardening undone (restored $backup_file)"
    print_status "SSH hardening changes have been undone."
}

# Run main function
main "$@"
