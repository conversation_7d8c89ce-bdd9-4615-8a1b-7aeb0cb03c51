[{"D:\\Augment\\project-tracker\\auth-service-demo\\src\\index.tsx": "1", "D:\\Augment\\project-tracker\\auth-service-demo\\src\\reportWebVitals.ts": "2", "D:\\Augment\\project-tracker\\auth-service-demo\\src\\App.tsx": "3", "D:\\Augment\\project-tracker\\auth-service-demo\\src\\services\\mockAuthService.ts": "4", "D:\\Augment\\project-tracker\\auth-service-demo\\src\\components\\Dashboard.tsx": "5", "D:\\Augment\\project-tracker\\auth-service-demo\\src\\components\\LoginForm.tsx": "6"}, {"size": 554, "mtime": 1752520284136, "results": "7", "hashOfConfig": "8"}, {"size": 425, "mtime": 1752520283812, "results": "9", "hashOfConfig": "8"}, {"size": 3499, "mtime": 1752672936452, "results": "10", "hashOfConfig": "8"}, {"size": 5900, "mtime": 1752520532948, "results": "11", "hashOfConfig": "8"}, {"size": 11597, "mtime": 1752526670207, "results": "12", "hashOfConfig": "8"}, {"size": 6465, "mtime": 1752526596984, "results": "13", "hashOfConfig": "8"}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "pmjepw", {"filePath": "17", "messages": "18", "suppressedMessages": "19", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Augment\\project-tracker\\auth-service-demo\\src\\index.tsx", [], [], "D:\\Augment\\project-tracker\\auth-service-demo\\src\\reportWebVitals.ts", [], [], "D:\\Augment\\project-tracker\\auth-service-demo\\src\\App.tsx", [], [], "D:\\Augment\\project-tracker\\auth-service-demo\\src\\services\\mockAuthService.ts", ["32"], [], "D:\\Augment\\project-tracker\\auth-service-demo\\src\\components\\Dashboard.tsx", ["33", "34"], [], "D:\\Augment\\project-tracker\\auth-service-demo\\src\\components\\LoginForm.tsx", [], [], {"ruleId": "35", "severity": 1, "message": "36", "line": 9, "column": 3, "nodeType": "37", "messageId": "38", "endLine": 9, "endColumn": 16}, {"ruleId": "35", "severity": 1, "message": "39", "line": 8, "column": 3, "nodeType": "37", "messageId": "38", "endLine": 8, "endColumn": 7}, {"ruleId": "40", "severity": 1, "message": "41", "line": 51, "column": 6, "nodeType": "42", "endLine": 51, "endColumn": 8, "suggestions": "43"}, "@typescript-eslint/no-unused-vars", "'ErrorResponse' is defined but never used.", "Identifier", "unusedVar", "'Grid' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'validateCurrentToken'. Either include it or remove the dependency array.", "ArrayExpression", ["44"], {"desc": "45", "fix": "46"}, "Update the dependencies array to be: [validateCurrentToken]", {"range": "47", "text": "48"}, [1315, 1317], "[validateCurrentToken]"]