{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment\\\\project-tracker\\\\auth-service-demo\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect, createContext, useContext } from 'react';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport { CssBaseline, Box, CircularProgress } from '@mui/material';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { LoginForm } from './components/LoginForm';\nimport { Dashboard } from './components/Dashboard';\nimport { MockAuthService } from './services/mockAuthService';\n\n// Theme context\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ThemeContext = /*#__PURE__*/createContext({\n  darkMode: false,\n  toggleTheme: () => {}\n});\nexport const useTheme = () => {\n  _s();\n  return useContext(ThemeContext);\n};\n\n// Create light theme\n_s(useTheme, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nconst lightTheme = createTheme({\n  palette: {\n    mode: 'light',\n    primary: {\n      main: '#1976d2'\n    },\n    secondary: {\n      main: '#dc004e'\n    },\n    background: {\n      default: '#f5f5f5',\n      paper: '#ffffff'\n    }\n  },\n  typography: {\n    fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif'\n  },\n  components: {\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          borderRadius: 16\n        }\n      }\n    },\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          borderRadius: 12,\n          textTransform: 'none'\n        }\n      }\n    },\n    MuiPaper: {\n      styleOverrides: {\n        root: {\n          borderRadius: 16\n        }\n      }\n    },\n    MuiTextField: {\n      styleOverrides: {\n        root: {\n          '& .MuiOutlinedInput-root': {\n            borderRadius: 12\n          }\n        }\n      }\n    }\n  }\n});\n\n// Create dark theme with auth-service color scheme\nconst darkTheme = createTheme({\n  palette: {\n    mode: 'dark',\n    primary: {\n      main: '#3182ce',\n      dark: '#2c5282',\n      light: '#63b3ed'\n    },\n    secondary: {\n      main: '#bee3f8'\n    },\n    background: {\n      default: '#0f1419',\n      paper: 'rgba(26, 54, 93, 0.4)'\n    },\n    text: {\n      primary: '#e2e8f0',\n      secondary: '#bee3f8'\n    },\n    divider: 'rgba(255, 255, 255, 0.1)'\n  },\n  typography: {\n    fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif'\n  },\n  components: {\n    MuiCssBaseline: {\n      styleOverrides: {\n        body: {\n          background: 'linear-gradient(135deg, #0f1419 0%, #1a202c 50%, #2d3748 100%)',\n          minHeight: '100vh'\n        }\n      }\n    },\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          borderRadius: 16,\n          background: 'rgba(26, 54, 93, 0.4)',\n          backdropFilter: 'blur(20px)',\n          border: '1px solid rgba(255, 255, 255, 0.1)',\n          boxShadow: '0 25px 50px rgba(0, 0, 0, 0.4)',\n          position: 'relative',\n          overflow: 'hidden',\n          '&::before': {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            height: '4px',\n            background: 'linear-gradient(90deg, #3182ce, #2c5282, #2a4365)'\n          }\n        }\n      }\n    },\n    MuiPaper: {\n      styleOverrides: {\n        root: {\n          borderRadius: 16,\n          background: 'rgba(26, 54, 93, 0.4)',\n          backdropFilter: 'blur(20px)',\n          border: '1px solid rgba(255, 255, 255, 0.1)',\n          boxShadow: '0 25px 50px rgba(0, 0, 0, 0.4)'\n        }\n      }\n    },\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          borderRadius: 12,\n          textTransform: 'none'\n        },\n        contained: {\n          background: 'linear-gradient(135deg, #3182ce 0%, #2c5282 100%)',\n          boxShadow: '0 6px 20px rgba(49, 130, 206, 0.4)',\n          '&:hover': {\n            background: 'linear-gradient(135deg, #2c5282 0%, #2a4365 100%)',\n            transform: 'translateY(-2px)',\n            boxShadow: '0 10px 30px rgba(49, 130, 206, 0.6)'\n          }\n        }\n      }\n    },\n    MuiTextField: {\n      styleOverrides: {\n        root: {\n          '& .MuiOutlinedInput-root': {\n            borderRadius: 12,\n            background: 'rgba(255, 255, 255, 0.1)',\n            backdropFilter: 'blur(10px)',\n            '& fieldset': {\n              borderColor: 'rgba(255, 255, 255, 0.2)'\n            },\n            '&:hover fieldset': {\n              borderColor: 'rgba(255, 255, 255, 0.3)'\n            },\n            '&.Mui-focused fieldset': {\n              borderColor: '#63b3ed',\n              boxShadow: '0 0 0 3px rgba(99, 179, 237, 0.2)'\n            }\n          },\n          '& .MuiInputBase-input': {\n            color: '#e2e8f0',\n            '&::placeholder': {\n              color: '#a0aec0',\n              opacity: 1\n            }\n          },\n          '& .MuiInputLabel-root': {\n            color: '#a0aec0',\n            '&.Mui-focused': {\n              color: '#63b3ed'\n            }\n          }\n        }\n      }\n    }\n  }\n});\nfunction App() {\n  _s2();\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [darkMode, setDarkMode] = useState(() => {\n    const saved = localStorage.getItem('darkMode');\n    return saved ? JSON.parse(saved) : false;\n  });\n  useEffect(() => {\n    checkAuthStatus();\n  }, []);\n  const checkAuthStatus = async () => {\n    setLoading(true);\n    try {\n      const token = MockAuthService.getAccessToken();\n      if (token) {\n        // Validate the stored token\n        const validation = await MockAuthService.validateToken({\n          token\n        });\n        setIsAuthenticated(validation.valid);\n      } else {\n        setIsAuthenticated(false);\n      }\n    } catch (error) {\n      console.warn('Auth check failed:', error);\n      setIsAuthenticated(false);\n      // Clear invalid tokens\n      await MockAuthService.logout();\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleLoginSuccess = () => {\n    setIsAuthenticated(true);\n  };\n  const handleLogout = () => {\n    setIsAuthenticated(false);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n      theme: theme,\n      children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          minHeight: '100vh'\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 60\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: theme,\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: isAuthenticated ? /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/dashboard\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(LoginForm, {\n            onLoginSuccess: handleLoginSuccess\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/dashboard\",\n          element: isAuthenticated ? /*#__PURE__*/_jsxDEV(Dashboard, {\n            onLogout: handleLogout\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/login\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: isAuthenticated ? \"/dashboard\" : \"/login\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 253,\n    columnNumber: 5\n  }, this);\n}\n_s2(App, \"Iwlu+7aLpUfxsH47DXHARVyxNiI=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "createContext", "useContext", "ThemeProvider", "createTheme", "CssBaseline", "Box", "CircularProgress", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "LoginForm", "Dashboard", "MockAuthService", "jsxDEV", "_jsxDEV", "ThemeContext", "darkMode", "toggleTheme", "useTheme", "_s", "lightTheme", "palette", "mode", "primary", "main", "secondary", "background", "default", "paper", "typography", "fontFamily", "components", "MuiCard", "styleOverrides", "root", "borderRadius", "MuiB<PERSON>on", "textTransform", "MuiPaper", "MuiTextField", "darkTheme", "dark", "light", "text", "divider", "MuiCssBaseline", "body", "minHeight", "<PERSON><PERSON>ilter", "border", "boxShadow", "position", "overflow", "content", "top", "left", "right", "height", "contained", "transform", "borderColor", "color", "opacity", "App", "_s2", "isAuthenticated", "setIsAuthenticated", "loading", "setLoading", "setDarkMode", "saved", "localStorage", "getItem", "JSON", "parse", "checkAuthStatus", "token", "getAccessToken", "validation", "validateToken", "valid", "error", "console", "warn", "logout", "handleLoginSuccess", "handleLogout", "theme", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "display", "justifyContent", "alignItems", "size", "path", "element", "to", "replace", "onLoginSuccess", "onLogout", "_c", "$RefreshReg$"], "sources": ["D:/Augment/project-tracker/auth-service-demo/src/App.tsx"], "sourcesContent": ["import React, { useState, useEffect, createContext, useContext } from 'react';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport { CssBaseline, Box, CircularProgress, IconButton, Fab } from '@mui/material';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { Brightness4, Brightness7 } from '@mui/icons-material';\nimport { LoginForm } from './components/LoginForm';\nimport { Dashboard } from './components/Dashboard';\nimport { MockAuthService } from './services/mockAuthService';\n\n// Theme context\nconst ThemeContext = createContext({\n  darkMode: false,\n  toggleTheme: () => {},\n});\n\nexport const useTheme = () => useContext(ThemeContext);\n\n// Create light theme\nconst lightTheme = createTheme({\n  palette: {\n    mode: 'light',\n    primary: {\n      main: '#1976d2',\n    },\n    secondary: {\n      main: '#dc004e',\n    },\n    background: {\n      default: '#f5f5f5',\n      paper: '#ffffff',\n    },\n  },\n  typography: {\n    fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n  },\n  components: {\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          borderRadius: 16,\n        },\n      },\n    },\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          borderRadius: 12,\n          textTransform: 'none',\n        },\n      },\n    },\n    MuiPaper: {\n      styleOverrides: {\n        root: {\n          borderRadius: 16,\n        },\n      },\n    },\n    MuiTextField: {\n      styleOverrides: {\n        root: {\n          '& .MuiOutlinedInput-root': {\n            borderRadius: 12,\n          },\n        },\n      },\n    },\n  },\n});\n\n// Create dark theme with auth-service color scheme\nconst darkTheme = createTheme({\n  palette: {\n    mode: 'dark',\n    primary: {\n      main: '#3182ce',\n      dark: '#2c5282',\n      light: '#63b3ed',\n    },\n    secondary: {\n      main: '#bee3f8',\n    },\n    background: {\n      default: '#0f1419',\n      paper: 'rgba(26, 54, 93, 0.4)',\n    },\n    text: {\n      primary: '#e2e8f0',\n      secondary: '#bee3f8',\n    },\n    divider: 'rgba(255, 255, 255, 0.1)',\n  },\n  typography: {\n    fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n  },\n  components: {\n    MuiCssBaseline: {\n      styleOverrides: {\n        body: {\n          background: 'linear-gradient(135deg, #0f1419 0%, #1a202c 50%, #2d3748 100%)',\n          minHeight: '100vh',\n        },\n      },\n    },\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          borderRadius: 16,\n          background: 'rgba(26, 54, 93, 0.4)',\n          backdropFilter: 'blur(20px)',\n          border: '1px solid rgba(255, 255, 255, 0.1)',\n          boxShadow: '0 25px 50px rgba(0, 0, 0, 0.4)',\n          position: 'relative',\n          overflow: 'hidden',\n          '&::before': {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            height: '4px',\n            background: 'linear-gradient(90deg, #3182ce, #2c5282, #2a4365)',\n          },\n        },\n      },\n    },\n    MuiPaper: {\n      styleOverrides: {\n        root: {\n          borderRadius: 16,\n          background: 'rgba(26, 54, 93, 0.4)',\n          backdropFilter: 'blur(20px)',\n          border: '1px solid rgba(255, 255, 255, 0.1)',\n          boxShadow: '0 25px 50px rgba(0, 0, 0, 0.4)',\n        },\n      },\n    },\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          borderRadius: 12,\n          textTransform: 'none',\n        },\n        contained: {\n          background: 'linear-gradient(135deg, #3182ce 0%, #2c5282 100%)',\n          boxShadow: '0 6px 20px rgba(49, 130, 206, 0.4)',\n          '&:hover': {\n            background: 'linear-gradient(135deg, #2c5282 0%, #2a4365 100%)',\n            transform: 'translateY(-2px)',\n            boxShadow: '0 10px 30px rgba(49, 130, 206, 0.6)',\n          },\n        },\n      },\n    },\n    MuiTextField: {\n      styleOverrides: {\n        root: {\n          '& .MuiOutlinedInput-root': {\n            borderRadius: 12,\n            background: 'rgba(255, 255, 255, 0.1)',\n            backdropFilter: 'blur(10px)',\n            '& fieldset': {\n              borderColor: 'rgba(255, 255, 255, 0.2)',\n            },\n            '&:hover fieldset': {\n              borderColor: 'rgba(255, 255, 255, 0.3)',\n            },\n            '&.Mui-focused fieldset': {\n              borderColor: '#63b3ed',\n              boxShadow: '0 0 0 3px rgba(99, 179, 237, 0.2)',\n            },\n          },\n          '& .MuiInputBase-input': {\n            color: '#e2e8f0',\n            '&::placeholder': {\n              color: '#a0aec0',\n              opacity: 1,\n            },\n          },\n          '& .MuiInputLabel-root': {\n            color: '#a0aec0',\n            '&.Mui-focused': {\n              color: '#63b3ed',\n            },\n          },\n        },\n      },\n    },\n  },\n});\n\nfunction App() {\n  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [darkMode, setDarkMode] = useState<boolean>(() => {\n    const saved = localStorage.getItem('darkMode');\n    return saved ? JSON.parse(saved) : false;\n  });\n\n  useEffect(() => {\n    checkAuthStatus();\n  }, []);\n\n  const checkAuthStatus = async () => {\n    setLoading(true);\n\n    try {\n      const token = MockAuthService.getAccessToken();\n      if (token) {\n        // Validate the stored token\n        const validation = await MockAuthService.validateToken({ token });\n        setIsAuthenticated(validation.valid);\n      } else {\n        setIsAuthenticated(false);\n      }\n    } catch (error) {\n      console.warn('Auth check failed:', error);\n      setIsAuthenticated(false);\n      // Clear invalid tokens\n      await MockAuthService.logout();\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleLoginSuccess = () => {\n    setIsAuthenticated(true);\n  };\n\n  const handleLogout = () => {\n    setIsAuthenticated(false);\n  };\n\n  if (loading) {\n    return (\n      <ThemeProvider theme={theme}>\n        <CssBaseline />\n        <Box\n          sx={{\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            minHeight: '100vh',\n          }}\n        >\n          <CircularProgress size={60} />\n        </Box>\n      </ThemeProvider>\n    );\n  }\n\n  return (\n    <ThemeProvider theme={theme}>\n      <CssBaseline />\n      <Router>\n        <Routes>\n          <Route\n            path=\"/login\"\n            element={\n              isAuthenticated ? (\n                <Navigate to=\"/dashboard\" replace />\n              ) : (\n                <LoginForm onLoginSuccess={handleLoginSuccess} />\n              )\n            }\n          />\n          <Route\n            path=\"/dashboard\"\n            element={\n              isAuthenticated ? (\n                <Dashboard onLogout={handleLogout} />\n              ) : (\n                <Navigate to=\"/login\" replace />\n              )\n            }\n          />\n          <Route\n            path=\"/\"\n            element={\n              <Navigate to={isAuthenticated ? \"/dashboard\" : \"/login\"} replace />\n            }\n          />\n        </Routes>\n      </Router>\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,aAAa,EAAEC,UAAU,QAAQ,OAAO;AAC7E,SAASC,aAAa,EAAEC,WAAW,QAAQ,sBAAsB;AACjE,SAASC,WAAW,EAAEC,GAAG,EAAEC,gBAAgB,QAAyB,eAAe;AACnF,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AAEnF,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,eAAe,QAAQ,4BAA4B;;AAE5D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,gBAAGjB,aAAa,CAAC;EACjCkB,QAAQ,EAAE,KAAK;EACfC,WAAW,EAAEA,CAAA,KAAM,CAAC;AACtB,CAAC,CAAC;AAEF,OAAO,MAAMC,QAAQ,GAAGA,CAAA;EAAAC,EAAA;EAAA,OAAMpB,UAAU,CAACgB,YAAY,CAAC;AAAA;;AAEtD;AAAAI,EAAA,CAFaD,QAAQ;AAGrB,MAAME,UAAU,GAAGnB,WAAW,CAAC;EAC7BoB,OAAO,EAAE;IACPC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE;MACPC,IAAI,EAAE;IACR,CAAC;IACDC,SAAS,EAAE;MACTD,IAAI,EAAE;IACR,CAAC;IACDE,UAAU,EAAE;MACVC,OAAO,EAAE,SAAS;MAClBC,KAAK,EAAE;IACT;EACF,CAAC;EACDC,UAAU,EAAE;IACVC,UAAU,EAAE;EACd,CAAC;EACDC,UAAU,EAAE;IACVC,OAAO,EAAE;MACPC,cAAc,EAAE;QACdC,IAAI,EAAE;UACJC,YAAY,EAAE;QAChB;MACF;IACF,CAAC;IACDC,SAAS,EAAE;MACTH,cAAc,EAAE;QACdC,IAAI,EAAE;UACJC,YAAY,EAAE,EAAE;UAChBE,aAAa,EAAE;QACjB;MACF;IACF,CAAC;IACDC,QAAQ,EAAE;MACRL,cAAc,EAAE;QACdC,IAAI,EAAE;UACJC,YAAY,EAAE;QAChB;MACF;IACF,CAAC;IACDI,YAAY,EAAE;MACZN,cAAc,EAAE;QACdC,IAAI,EAAE;UACJ,0BAA0B,EAAE;YAC1BC,YAAY,EAAE;UAChB;QACF;MACF;IACF;EACF;AACF,CAAC,CAAC;;AAEF;AACA,MAAMK,SAAS,GAAGvC,WAAW,CAAC;EAC5BoB,OAAO,EAAE;IACPC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE;MACPC,IAAI,EAAE,SAAS;MACfiB,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE;IACT,CAAC;IACDjB,SAAS,EAAE;MACTD,IAAI,EAAE;IACR,CAAC;IACDE,UAAU,EAAE;MACVC,OAAO,EAAE,SAAS;MAClBC,KAAK,EAAE;IACT,CAAC;IACDe,IAAI,EAAE;MACJpB,OAAO,EAAE,SAAS;MAClBE,SAAS,EAAE;IACb,CAAC;IACDmB,OAAO,EAAE;EACX,CAAC;EACDf,UAAU,EAAE;IACVC,UAAU,EAAE;EACd,CAAC;EACDC,UAAU,EAAE;IACVc,cAAc,EAAE;MACdZ,cAAc,EAAE;QACda,IAAI,EAAE;UACJpB,UAAU,EAAE,gEAAgE;UAC5EqB,SAAS,EAAE;QACb;MACF;IACF,CAAC;IACDf,OAAO,EAAE;MACPC,cAAc,EAAE;QACdC,IAAI,EAAE;UACJC,YAAY,EAAE,EAAE;UAChBT,UAAU,EAAE,uBAAuB;UACnCsB,cAAc,EAAE,YAAY;UAC5BC,MAAM,EAAE,oCAAoC;UAC5CC,SAAS,EAAE,gCAAgC;UAC3CC,QAAQ,EAAE,UAAU;UACpBC,QAAQ,EAAE,QAAQ;UAClB,WAAW,EAAE;YACXC,OAAO,EAAE,IAAI;YACbF,QAAQ,EAAE,UAAU;YACpBG,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,KAAK;YACb/B,UAAU,EAAE;UACd;QACF;MACF;IACF,CAAC;IACDY,QAAQ,EAAE;MACRL,cAAc,EAAE;QACdC,IAAI,EAAE;UACJC,YAAY,EAAE,EAAE;UAChBT,UAAU,EAAE,uBAAuB;UACnCsB,cAAc,EAAE,YAAY;UAC5BC,MAAM,EAAE,oCAAoC;UAC5CC,SAAS,EAAE;QACb;MACF;IACF,CAAC;IACDd,SAAS,EAAE;MACTH,cAAc,EAAE;QACdC,IAAI,EAAE;UACJC,YAAY,EAAE,EAAE;UAChBE,aAAa,EAAE;QACjB,CAAC;QACDqB,SAAS,EAAE;UACThC,UAAU,EAAE,mDAAmD;UAC/DwB,SAAS,EAAE,oCAAoC;UAC/C,SAAS,EAAE;YACTxB,UAAU,EAAE,mDAAmD;YAC/DiC,SAAS,EAAE,kBAAkB;YAC7BT,SAAS,EAAE;UACb;QACF;MACF;IACF,CAAC;IACDX,YAAY,EAAE;MACZN,cAAc,EAAE;QACdC,IAAI,EAAE;UACJ,0BAA0B,EAAE;YAC1BC,YAAY,EAAE,EAAE;YAChBT,UAAU,EAAE,0BAA0B;YACtCsB,cAAc,EAAE,YAAY;YAC5B,YAAY,EAAE;cACZY,WAAW,EAAE;YACf,CAAC;YACD,kBAAkB,EAAE;cAClBA,WAAW,EAAE;YACf,CAAC;YACD,wBAAwB,EAAE;cACxBA,WAAW,EAAE,SAAS;cACtBV,SAAS,EAAE;YACb;UACF,CAAC;UACD,uBAAuB,EAAE;YACvBW,KAAK,EAAE,SAAS;YAChB,gBAAgB,EAAE;cAChBA,KAAK,EAAE,SAAS;cAChBC,OAAO,EAAE;YACX;UACF,CAAC;UACD,uBAAuB,EAAE;YACvBD,KAAK,EAAE,SAAS;YAChB,eAAe,EAAE;cACfA,KAAK,EAAE;YACT;UACF;QACF;MACF;IACF;EACF;AACF,CAAC,CAAC;AAEF,SAASE,GAAGA,CAAA,EAAG;EAAAC,GAAA;EACb,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtE,QAAQ,CAAU,KAAK,CAAC;EACtE,MAAM,CAACuE,OAAO,EAAEC,UAAU,CAAC,GAAGxE,QAAQ,CAAU,IAAI,CAAC;EACrD,MAAM,CAACoB,QAAQ,EAAEqD,WAAW,CAAC,GAAGzE,QAAQ,CAAU,MAAM;IACtD,MAAM0E,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IAC9C,OAAOF,KAAK,GAAGG,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAC,GAAG,KAAK;EAC1C,CAAC,CAAC;EAEFzE,SAAS,CAAC,MAAM;IACd8E,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClCP,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMQ,KAAK,GAAGhE,eAAe,CAACiE,cAAc,CAAC,CAAC;MAC9C,IAAID,KAAK,EAAE;QACT;QACA,MAAME,UAAU,GAAG,MAAMlE,eAAe,CAACmE,aAAa,CAAC;UAAEH;QAAM,CAAC,CAAC;QACjEV,kBAAkB,CAACY,UAAU,CAACE,KAAK,CAAC;MACtC,CAAC,MAAM;QACLd,kBAAkB,CAAC,KAAK,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,oBAAoB,EAAEF,KAAK,CAAC;MACzCf,kBAAkB,CAAC,KAAK,CAAC;MACzB;MACA,MAAMtD,eAAe,CAACwE,MAAM,CAAC,CAAC;IAChC,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiB,kBAAkB,GAAGA,CAAA,KAAM;IAC/BnB,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMoB,YAAY,GAAGA,CAAA,KAAM;IACzBpB,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;EAED,IAAIC,OAAO,EAAE;IACX,oBACErD,OAAA,CAACd,aAAa;MAACuF,KAAK,EAAEA,KAAM;MAAAC,QAAA,gBAC1B1E,OAAA,CAACZ,WAAW;QAAAuF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACf9E,OAAA,CAACX,GAAG;QACF0F,EAAE,EAAE;UACFC,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,QAAQ;UACxBC,UAAU,EAAE,QAAQ;UACpBjD,SAAS,EAAE;QACb,CAAE;QAAAyC,QAAA,eAEF1E,OAAA,CAACV,gBAAgB;UAAC6F,IAAI,EAAE;QAAG;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAEpB;EAEA,oBACE9E,OAAA,CAACd,aAAa;IAACuF,KAAK,EAAEA,KAAM;IAAAC,QAAA,gBAC1B1E,OAAA,CAACZ,WAAW;MAAAuF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACf9E,OAAA,CAACR,MAAM;MAAAkF,QAAA,eACL1E,OAAA,CAACP,MAAM;QAAAiF,QAAA,gBACL1E,OAAA,CAACN,KAAK;UACJ0F,IAAI,EAAC,QAAQ;UACbC,OAAO,EACLlC,eAAe,gBACbnD,OAAA,CAACL,QAAQ;YAAC2F,EAAE,EAAC,YAAY;YAACC,OAAO;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEpC9E,OAAA,CAACJ,SAAS;YAAC4F,cAAc,EAAEjB;UAAmB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAEnD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACF9E,OAAA,CAACN,KAAK;UACJ0F,IAAI,EAAC,YAAY;UACjBC,OAAO,EACLlC,eAAe,gBACbnD,OAAA,CAACH,SAAS;YAAC4F,QAAQ,EAAEjB;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAErC9E,OAAA,CAACL,QAAQ;YAAC2F,EAAE,EAAC,QAAQ;YAACC,OAAO;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAElC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACF9E,OAAA,CAACN,KAAK;UACJ0F,IAAI,EAAC,GAAG;UACRC,OAAO,eACLrF,OAAA,CAACL,QAAQ;YAAC2F,EAAE,EAAEnC,eAAe,GAAG,YAAY,GAAG,QAAS;YAACoC,OAAO;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACnE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEpB;AAAC5B,GAAA,CA/FQD,GAAG;AAAAyC,EAAA,GAAHzC,GAAG;AAiGZ,eAAeA,GAAG;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}