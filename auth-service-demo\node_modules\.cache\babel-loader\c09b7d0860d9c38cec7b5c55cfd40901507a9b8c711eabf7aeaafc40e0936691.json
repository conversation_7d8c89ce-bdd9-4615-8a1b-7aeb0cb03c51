{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment\\\\project-tracker\\\\auth-service-demo\\\\src\\\\components\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Card, CardContent, Typography, Button, Alert, CircularProgress, Chip, Divider, List, ListItem, ListItemText, AppBar, Toolbar, IconButton, Menu, MenuItem } from '@mui/material';\nimport { Security as SecurityIcon, Token as TokenIcon, Refresh as RefreshIcon, Logout as LogoutIcon, HealthAndSafety as HealthIcon, AccountCircle } from '@mui/icons-material';\nimport { MockAuthService } from '../services/mockAuthService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const Dashboard = ({\n  onLogout\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  const [healthData, setHealthData] = useState(null);\n  const [tokenData, setTokenData] = useState(null);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const accessToken = MockAuthService.getAccessToken();\n  const refreshToken = MockAuthService.getRefreshToken();\n  useEffect(() => {\n    checkHealth();\n    validateCurrentToken();\n  }, []);\n  const checkHealth = async () => {\n    try {\n      const health = await MockAuthService.healthCheck();\n      setHealthData(health);\n    } catch (err) {\n      console.warn('Health check failed:', err.message);\n    }\n  };\n  const validateCurrentToken = async () => {\n    if (!accessToken) return;\n    try {\n      const validation = await MockAuthService.validateToken({\n        token: accessToken\n      });\n      setTokenData(validation);\n    } catch (err) {\n      console.warn('Token validation failed:', err.message);\n    }\n  };\n  const handleRefreshToken = async () => {\n    if (!refreshToken) {\n      setError('No refresh token available');\n      return;\n    }\n    setLoading(true);\n    setError(null);\n    setSuccess(null);\n    try {\n      await MockAuthService.refreshToken({\n        refresh_token: refreshToken,\n        grant_type: 'refresh_token'\n      });\n      setSuccess('Token refreshed successfully');\n      await validateCurrentToken();\n    } catch (err) {\n      setError(err.message || 'Token refresh failed');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleLogout = async () => {\n    setLoading(true);\n    try {\n      await MockAuthService.logout();\n      onLogout();\n    } catch (err) {\n      console.warn('Logout error:', err.message);\n      onLogout(); // Logout anyway\n    }\n  };\n  const handleMenuOpen = event => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n  };\n  const formatTokenPreview = token => {\n    if (!token) return 'Not available';\n    return `${token.substring(0, 20)}...${token.substring(token.length - 10)}`;\n  };\n  const formatTimestamp = timestamp => {\n    return new Date(timestamp * 1000).toLocaleString();\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      flexGrow: 1\n    },\n    children: [/*#__PURE__*/_jsxDEV(AppBar, {\n      position: \"static\",\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        children: [/*#__PURE__*/_jsxDEV(SecurityIcon, {\n          sx: {\n            mr: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          component: \"div\",\n          sx: {\n            flexGrow: 1\n          },\n          children: \"Auth Service Demo Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n          label: \"DEMO MODE\",\n          color: \"secondary\",\n          size: \"small\",\n          sx: {\n            mr: 2,\n            fontWeight: 'bold'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"large\",\n          \"aria-label\": \"account menu\",\n          \"aria-controls\": \"menu-appbar\",\n          \"aria-haspopup\": \"true\",\n          onClick: handleMenuOpen,\n          color: \"inherit\",\n          children: /*#__PURE__*/_jsxDEV(AccountCircle, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Menu, {\n          id: \"menu-appbar\",\n          anchorEl: anchorEl,\n          anchorOrigin: {\n            vertical: 'top',\n            horizontal: 'right'\n          },\n          keepMounted: true,\n          transformOrigin: {\n            vertical: 'top',\n            horizontal: 'right'\n          },\n          open: Boolean(anchorEl),\n          onClose: handleMenuClose,\n          children: /*#__PURE__*/_jsxDEV(MenuItem, {\n            onClick: handleLogout,\n            children: [/*#__PURE__*/_jsxDEV(LogoutIcon, {\n              sx: {\n                mr: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), \"Logout\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Demo Mode:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this), \" This is a demonstration of the React/TypeScript UI with mock data. In production, this would connect to the real C++23 auth-service backend.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 3\n        },\n        onClose: () => setError(null),\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 11\n      }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"success\",\n        sx: {\n          mb: 3\n        },\n        onClose: () => setSuccess(null),\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'grid',\n          gridTemplateColumns: {\n            xs: '1fr',\n            md: '1fr 1fr'\n          },\n          gap: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              borderRadius: 3\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(HealthIcon, {\n                  sx: {\n                    mr: 1,\n                    color: 'success.main'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"Service Health\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this), healthData ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Chip, {\n                  label: healthData.status,\n                  color: \"success\",\n                  sx: {\n                    mb: 2\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Service: \", healthData.service]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Version: \", healthData.version]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Last Check: \", formatTimestamp(healthData.timestamp)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Divider, {\n                  sx: {\n                    my: 2\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  gutterBottom: true,\n                  children: \"Available Endpoints:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(List, {\n                  dense: true,\n                  children: healthData.oauth2_endpoints.map((endpoint, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n                    sx: {\n                      py: 0\n                    },\n                    children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                      primary: endpoint,\n                      primaryTypographyProps: {\n                        variant: 'body2'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 222,\n                      columnNumber: 27\n                    }, this)\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                children: \"Loading health data...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(TokenIcon, {\n                  sx: {\n                    mr: 1,\n                    color: 'primary.main'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"Token Information\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this), tokenData ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Chip, {\n                  label: tokenData.valid ? 'Valid' : 'Invalid',\n                  color: tokenData.valid ? 'success' : 'error',\n                  sx: {\n                    mb: 2\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 21\n                }, this), tokenData.valid && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: [\"User: \", tokenData.username]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 255,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: [\"User ID: \", tokenData.user_id]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 25\n                  }, this), tokenData.expires_at && /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: [\"Expires: \", formatTimestamp(tokenData.expires_at)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 262,\n                    columnNumber: 27\n                  }, this), tokenData.scopes && /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: [\"Scopes: \", tokenData.scopes.join(', ')]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                children: \"Loading token data...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {\n                sx: {\n                  my: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                children: \"Access Token:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                sx: {\n                  wordBreak: 'break-all',\n                  mb: 1\n                },\n                children: formatTokenPreview(accessToken)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                children: \"Refresh Token:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                sx: {\n                  wordBreak: 'break-all'\n                },\n                children: formatTokenPreview(refreshToken)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Token Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 2,\n                flexWrap: 'wrap'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 42\n                }, this) : /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 75\n                }, this),\n                onClick: handleRefreshToken,\n                disabled: loading || !refreshToken,\n                children: \"Refresh Token\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                startIcon: /*#__PURE__*/_jsxDEV(HealthIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 32\n                }, this),\n                onClick: checkHealth,\n                disabled: loading,\n                children: \"Check Health\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                startIcon: /*#__PURE__*/_jsxDEV(TokenIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 32\n                }, this),\n                onClick: validateCurrentToken,\n                disabled: loading || !accessToken,\n                children: \"Validate Token\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                color: \"error\",\n                startIcon: /*#__PURE__*/_jsxDEV(LogoutIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 32\n                }, this),\n                onClick: handleLogout,\n                disabled: loading,\n                children: \"Logout\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 123,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"hZ+tK1ctdCEyYGTHmAhe3p+CnZY=\");\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "<PERSON><PERSON>", "CircularProgress", "Chip", "Divider", "List", "ListItem", "ListItemText", "AppBar", "<PERSON><PERSON><PERSON>", "IconButton", "<PERSON><PERSON>", "MenuItem", "Security", "SecurityIcon", "Token", "TokenIcon", "Refresh", "RefreshIcon", "Logout", "LogoutIcon", "HealthAndSafety", "HealthIcon", "AccountCircle", "MockAuthService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Dashboard", "onLogout", "_s", "loading", "setLoading", "error", "setError", "success", "setSuccess", "healthData", "setHealthData", "tokenData", "setTokenData", "anchorEl", "setAnchorEl", "accessToken", "getAccessToken", "refreshToken", "getRefreshToken", "checkHealth", "validateCurrentToken", "health", "healthCheck", "err", "console", "warn", "message", "validation", "validateToken", "token", "handleRefreshToken", "refresh_token", "grant_type", "handleLogout", "logout", "handleMenuOpen", "event", "currentTarget", "handleMenuClose", "formatTokenPreview", "substring", "length", "formatTimestamp", "timestamp", "Date", "toLocaleString", "sx", "flexGrow", "children", "position", "mr", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "component", "label", "color", "size", "fontWeight", "onClick", "id", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "keepMounted", "transform<PERSON><PERSON>in", "open", "Boolean", "onClose", "p", "severity", "mb", "display", "gridTemplateColumns", "xs", "md", "gap", "borderRadius", "alignItems", "status", "service", "version", "my", "gutterBottom", "dense", "oauth2_endpoints", "map", "endpoint", "index", "py", "primary", "primaryTypographyProps", "valid", "username", "user_id", "expires_at", "scopes", "join", "wordBreak", "mt", "flexWrap", "startIcon", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/Augment/project-tracker/auth-service-demo/src/components/Dashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Ty<PERSON>graphy,\n  Button,\n  Grid,\n  Alert,\n  CircularProgress,\n  Chip,\n  Divider,\n  List,\n  ListItem,\n  ListItemText,\n  AppBar,\n  Toolbar,\n  IconButton,\n  Menu,\n  MenuItem\n} from '@mui/material';\nimport {\n  Security as SecurityIcon,\n  Token as TokenIcon,\n  Refresh as RefreshIcon,\n  Logout as LogoutIcon,\n  HealthAndSafety as HealthIcon,\n  AccountCircle\n} from '@mui/icons-material';\nimport { MockAuthService } from '../services/mockAuthService';\nimport { HealthResponse, ValidationResponse } from '../types/auth';\n\ninterface DashboardProps {\n  onLogout: () => void;\n}\n\nexport const Dashboard: React.FC<DashboardProps> = ({ onLogout }) => {\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const [healthData, setHealthData] = useState<HealthResponse | null>(null);\n  const [tokenData, setTokenData] = useState<ValidationResponse | null>(null);\n  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);\n\n  const accessToken = MockAuthService.getAccessToken();\n  const refreshToken = MockAuthService.getRefreshToken();\n\n  useEffect(() => {\n    checkHealth();\n    validateCurrentToken();\n  }, []);\n\n  const checkHealth = async () => {\n    try {\n      const health = await MockAuthService.healthCheck();\n      setHealthData(health);\n    } catch (err: any) {\n      console.warn('Health check failed:', err.message);\n    }\n  };\n\n  const validateCurrentToken = async () => {\n    if (!accessToken) return;\n\n    try {\n      const validation = await MockAuthService.validateToken({ token: accessToken });\n      setTokenData(validation);\n    } catch (err: any) {\n      console.warn('Token validation failed:', err.message);\n    }\n  };\n\n  const handleRefreshToken = async () => {\n    if (!refreshToken) {\n      setError('No refresh token available');\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n    setSuccess(null);\n\n    try {\n      await MockAuthService.refreshToken({ refresh_token: refreshToken, grant_type: 'refresh_token' });\n      setSuccess('Token refreshed successfully');\n      await validateCurrentToken();\n    } catch (err: any) {\n      setError(err.message || 'Token refresh failed');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleLogout = async () => {\n    setLoading(true);\n    try {\n      await MockAuthService.logout();\n      onLogout();\n    } catch (err: any) {\n      console.warn('Logout error:', err.message);\n      onLogout(); // Logout anyway\n    }\n  };\n\n  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n  };\n\n  const formatTokenPreview = (token: string | null): string => {\n    if (!token) return 'Not available';\n    return `${token.substring(0, 20)}...${token.substring(token.length - 10)}`;\n  };\n\n  const formatTimestamp = (timestamp: number): string => {\n    return new Date(timestamp * 1000).toLocaleString();\n  };\n\n  return (\n    <Box sx={{ flexGrow: 1 }}>\n      {/* App Bar */}\n      <AppBar position=\"static\">\n        <Toolbar>\n          <SecurityIcon sx={{ mr: 2 }} />\n          <Typography variant=\"h6\" component=\"div\" sx={{ flexGrow: 1 }}>\n            Auth Service Demo Dashboard\n          </Typography>\n          <Chip \n            label=\"DEMO MODE\" \n            color=\"secondary\" \n            size=\"small\" \n            sx={{ mr: 2, fontWeight: 'bold' }}\n          />\n          <IconButton\n            size=\"large\"\n            aria-label=\"account menu\"\n            aria-controls=\"menu-appbar\"\n            aria-haspopup=\"true\"\n            onClick={handleMenuOpen}\n            color=\"inherit\"\n          >\n            <AccountCircle />\n          </IconButton>\n          <Menu\n            id=\"menu-appbar\"\n            anchorEl={anchorEl}\n            anchorOrigin={{\n              vertical: 'top',\n              horizontal: 'right',\n            }}\n            keepMounted\n            transformOrigin={{\n              vertical: 'top',\n              horizontal: 'right',\n            }}\n            open={Boolean(anchorEl)}\n            onClose={handleMenuClose}\n          >\n            <MenuItem onClick={handleLogout}>\n              <LogoutIcon sx={{ mr: 1 }} />\n              Logout\n            </MenuItem>\n          </Menu>\n        </Toolbar>\n      </AppBar>\n\n      {/* Main Content */}\n      <Box sx={{ p: 3 }}>\n        {/* Demo Notice */}\n        <Alert severity=\"info\" sx={{ mb: 3 }}>\n          <strong>Demo Mode:</strong> This is a demonstration of the React/TypeScript UI with mock data. \n          In production, this would connect to the real C++23 auth-service backend.\n        </Alert>\n\n        {/* Alerts */}\n        {error && (\n          <Alert severity=\"error\" sx={{ mb: 3 }} onClose={() => setError(null)}>\n            {error}\n          </Alert>\n        )}\n        {success && (\n          <Alert severity=\"success\" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>\n            {success}\n          </Alert>\n        )}\n\n        <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 3 }}>\n          {/* Service Health */}\n          <Box>\n            <Card sx={{ borderRadius: 3 }}>\n              <CardContent>\n                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                  <HealthIcon sx={{ mr: 1, color: 'success.main' }} />\n                  <Typography variant=\"h6\">Service Health</Typography>\n                </Box>\n                {healthData ? (\n                  <>\n                    <Chip \n                      label={healthData.status} \n                      color=\"success\" \n                      sx={{ mb: 2 }} \n                    />\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Service: {healthData.service}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Version: {healthData.version}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Last Check: {formatTimestamp(healthData.timestamp)}\n                    </Typography>\n                    <Divider sx={{ my: 2 }} />\n                    <Typography variant=\"subtitle2\" gutterBottom>\n                      Available Endpoints:\n                    </Typography>\n                    <List dense>\n                      {healthData.oauth2_endpoints.map((endpoint, index) => (\n                        <ListItem key={index} sx={{ py: 0 }}>\n                          <ListItemText \n                            primary={endpoint} \n                            primaryTypographyProps={{ variant: 'body2' }}\n                          />\n                        </ListItem>\n                      ))}\n                    </List>\n                  </>\n                ) : (\n                  <Typography color=\"text.secondary\">Loading health data...</Typography>\n                )}\n              </CardContent>\n            </Card>\n          </Box>\n\n          {/* Token Information */}\n          <Box>\n            <Card>\n              <CardContent>\n                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                  <TokenIcon sx={{ mr: 1, color: 'primary.main' }} />\n                  <Typography variant=\"h6\">Token Information</Typography>\n                </Box>\n                \n                {tokenData ? (\n                  <>\n                    <Chip \n                      label={tokenData.valid ? 'Valid' : 'Invalid'} \n                      color={tokenData.valid ? 'success' : 'error'} \n                      sx={{ mb: 2 }} \n                    />\n                    {tokenData.valid && (\n                      <>\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          User: {tokenData.username}\n                        </Typography>\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          User ID: {tokenData.user_id}\n                        </Typography>\n                        {tokenData.expires_at && (\n                          <Typography variant=\"body2\" color=\"text.secondary\">\n                            Expires: {formatTimestamp(tokenData.expires_at)}\n                          </Typography>\n                        )}\n                        {tokenData.scopes && (\n                          <Typography variant=\"body2\" color=\"text.secondary\">\n                            Scopes: {tokenData.scopes.join(', ')}\n                          </Typography>\n                        )}\n                      </>\n                    )}\n                  </>\n                ) : (\n                  <Typography color=\"text.secondary\">Loading token data...</Typography>\n                )}\n\n                <Divider sx={{ my: 2 }} />\n                \n                <Typography variant=\"subtitle2\" gutterBottom>\n                  Access Token:\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\" sx={{ wordBreak: 'break-all', mb: 1 }}>\n                  {formatTokenPreview(accessToken)}\n                </Typography>\n                \n                <Typography variant=\"subtitle2\" gutterBottom>\n                  Refresh Token:\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\" sx={{ wordBreak: 'break-all' }}>\n                  {formatTokenPreview(refreshToken)}\n                </Typography>\n              </CardContent>\n            </Card>\n          </Box>\n        </Box>\n\n        {/* Actions */}\n        <Box sx={{ mt: 3 }}>\n            <Card>\n              <CardContent>\n                <Typography variant=\"h6\" gutterBottom>\n                  Token Actions\n                </Typography>\n                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>\n                  <Button\n                    variant=\"contained\"\n                    startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}\n                    onClick={handleRefreshToken}\n                    disabled={loading || !refreshToken}\n                  >\n                    Refresh Token\n                  </Button>\n                  <Button\n                    variant=\"outlined\"\n                    startIcon={<HealthIcon />}\n                    onClick={checkHealth}\n                    disabled={loading}\n                  >\n                    Check Health\n                  </Button>\n                  <Button\n                    variant=\"outlined\"\n                    startIcon={<TokenIcon />}\n                    onClick={validateCurrentToken}\n                    disabled={loading || !accessToken}\n                  >\n                    Validate Token\n                  </Button>\n                  <Button\n                    variant=\"outlined\"\n                    color=\"error\"\n                    startIcon={<LogoutIcon />}\n                    onClick={handleLogout}\n                    disabled={loading}\n                  >\n                    Logout\n                  </Button>\n                </Box>\n              </CardContent>\n            </Card>\n          </Box>\n      </Box>\n    </Box>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EAENC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,IAAI,EACJC,QAAQ,QACH,eAAe;AACtB,SACEC,QAAQ,IAAIC,YAAY,EACxBC,KAAK,IAAIC,SAAS,EAClBC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,EACpBC,eAAe,IAAIC,UAAU,EAC7BC,aAAa,QACR,qBAAqB;AAC5B,SAASC,eAAe,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAO9D,OAAO,MAAMC,SAAmC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACnE,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwC,KAAK,EAAEC,QAAQ,CAAC,GAAGzC,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC0C,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAwB,IAAI,CAAC;EACzE,MAAM,CAAC8C,SAAS,EAAEC,YAAY,CAAC,GAAG/C,QAAQ,CAA4B,IAAI,CAAC;EAC3E,MAAM,CAACgD,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAqB,IAAI,CAAC;EAElE,MAAMkD,WAAW,GAAGpB,eAAe,CAACqB,cAAc,CAAC,CAAC;EACpD,MAAMC,YAAY,GAAGtB,eAAe,CAACuB,eAAe,CAAC,CAAC;EAEtDpD,SAAS,CAAC,MAAM;IACdqD,WAAW,CAAC,CAAC;IACbC,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAME,MAAM,GAAG,MAAM1B,eAAe,CAAC2B,WAAW,CAAC,CAAC;MAClDZ,aAAa,CAACW,MAAM,CAAC;IACvB,CAAC,CAAC,OAAOE,GAAQ,EAAE;MACjBC,OAAO,CAACC,IAAI,CAAC,sBAAsB,EAAEF,GAAG,CAACG,OAAO,CAAC;IACnD;EACF,CAAC;EAED,MAAMN,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACL,WAAW,EAAE;IAElB,IAAI;MACF,MAAMY,UAAU,GAAG,MAAMhC,eAAe,CAACiC,aAAa,CAAC;QAAEC,KAAK,EAAEd;MAAY,CAAC,CAAC;MAC9EH,YAAY,CAACe,UAAU,CAAC;IAC1B,CAAC,CAAC,OAAOJ,GAAQ,EAAE;MACjBC,OAAO,CAACC,IAAI,CAAC,0BAA0B,EAAEF,GAAG,CAACG,OAAO,CAAC;IACvD;EACF,CAAC;EAED,MAAMI,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAACb,YAAY,EAAE;MACjBX,QAAQ,CAAC,4BAA4B,CAAC;MACtC;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMb,eAAe,CAACsB,YAAY,CAAC;QAAEc,aAAa,EAAEd,YAAY;QAAEe,UAAU,EAAE;MAAgB,CAAC,CAAC;MAChGxB,UAAU,CAAC,8BAA8B,CAAC;MAC1C,MAAMY,oBAAoB,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAOG,GAAQ,EAAE;MACjBjB,QAAQ,CAACiB,GAAG,CAACG,OAAO,IAAI,sBAAsB,CAAC;IACjD,CAAC,SAAS;MACRtB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6B,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B7B,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMT,eAAe,CAACuC,MAAM,CAAC,CAAC;MAC9BjC,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOsB,GAAQ,EAAE;MACjBC,OAAO,CAACC,IAAI,CAAC,eAAe,EAAEF,GAAG,CAACG,OAAO,CAAC;MAC1CzB,QAAQ,CAAC,CAAC,CAAC,CAAC;IACd;EACF,CAAC;EAED,MAAMkC,cAAc,GAAIC,KAAoC,IAAK;IAC/DtB,WAAW,CAACsB,KAAK,CAACC,aAAa,CAAC;EAClC,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5BxB,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAMyB,kBAAkB,GAAIV,KAAoB,IAAa;IAC3D,IAAI,CAACA,KAAK,EAAE,OAAO,eAAe;IAClC,OAAO,GAAGA,KAAK,CAACW,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,MAAMX,KAAK,CAACW,SAAS,CAACX,KAAK,CAACY,MAAM,GAAG,EAAE,CAAC,EAAE;EAC5E,CAAC;EAED,MAAMC,eAAe,GAAIC,SAAiB,IAAa;IACrD,OAAO,IAAIC,IAAI,CAACD,SAAS,GAAG,IAAI,CAAC,CAACE,cAAc,CAAC,CAAC;EACpD,CAAC;EAED,oBACEhD,OAAA,CAAC9B,GAAG;IAAC+E,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAEvBnD,OAAA,CAAClB,MAAM;MAACsE,QAAQ,EAAC,QAAQ;MAAAD,QAAA,eACvBnD,OAAA,CAACjB,OAAO;QAAAoE,QAAA,gBACNnD,OAAA,CAACZ,YAAY;UAAC6D,EAAE,EAAE;YAAEI,EAAE,EAAE;UAAE;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/BzD,OAAA,CAAC3B,UAAU;UAACqF,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,KAAK;UAACV,EAAE,EAAE;YAAEC,QAAQ,EAAE;UAAE,CAAE;UAAAC,QAAA,EAAC;QAE9D;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbzD,OAAA,CAACvB,IAAI;UACHmF,KAAK,EAAC,WAAW;UACjBC,KAAK,EAAC,WAAW;UACjBC,IAAI,EAAC,OAAO;UACZb,EAAE,EAAE;YAAEI,EAAE,EAAE,CAAC;YAAEU,UAAU,EAAE;UAAO;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACFzD,OAAA,CAAChB,UAAU;UACT8E,IAAI,EAAC,OAAO;UACZ,cAAW,cAAc;UACzB,iBAAc,aAAa;UAC3B,iBAAc,MAAM;UACpBE,OAAO,EAAE1B,cAAe;UACxBuB,KAAK,EAAC,SAAS;UAAAV,QAAA,eAEfnD,OAAA,CAACH,aAAa;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACbzD,OAAA,CAACf,IAAI;UACHgF,EAAE,EAAC,aAAa;UAChBjD,QAAQ,EAAEA,QAAS;UACnBkD,YAAY,EAAE;YACZC,QAAQ,EAAE,KAAK;YACfC,UAAU,EAAE;UACd,CAAE;UACFC,WAAW;UACXC,eAAe,EAAE;YACfH,QAAQ,EAAE,KAAK;YACfC,UAAU,EAAE;UACd,CAAE;UACFG,IAAI,EAAEC,OAAO,CAACxD,QAAQ,CAAE;UACxByD,OAAO,EAAEhC,eAAgB;UAAAU,QAAA,eAEzBnD,OAAA,CAACd,QAAQ;YAAC8E,OAAO,EAAE5B,YAAa;YAAAe,QAAA,gBAC9BnD,OAAA,CAACN,UAAU;cAACuD,EAAE,EAAE;gBAAEI,EAAE,EAAE;cAAE;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,UAE/B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGTzD,OAAA,CAAC9B,GAAG;MAAC+E,EAAE,EAAE;QAAEyB,CAAC,EAAE;MAAE,CAAE;MAAAvB,QAAA,gBAEhBnD,OAAA,CAACzB,KAAK;QAACoG,QAAQ,EAAC,MAAM;QAAC1B,EAAE,EAAE;UAAE2B,EAAE,EAAE;QAAE,CAAE;QAAAzB,QAAA,gBACnCnD,OAAA;UAAAmD,QAAA,EAAQ;QAAU;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,iJAE7B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAGPjD,KAAK,iBACJR,OAAA,CAACzB,KAAK;QAACoG,QAAQ,EAAC,OAAO;QAAC1B,EAAE,EAAE;UAAE2B,EAAE,EAAE;QAAE,CAAE;QAACH,OAAO,EAAEA,CAAA,KAAMhE,QAAQ,CAAC,IAAI,CAAE;QAAA0C,QAAA,EAClE3C;MAAK;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,EACA/C,OAAO,iBACNV,OAAA,CAACzB,KAAK;QAACoG,QAAQ,EAAC,SAAS;QAAC1B,EAAE,EAAE;UAAE2B,EAAE,EAAE;QAAE,CAAE;QAACH,OAAO,EAAEA,CAAA,KAAM9D,UAAU,CAAC,IAAI,CAAE;QAAAwC,QAAA,EACtEzC;MAAO;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACR,eAEDzD,OAAA,CAAC9B,GAAG;QAAC+E,EAAE,EAAE;UAAE4B,OAAO,EAAE,MAAM;UAAEC,mBAAmB,EAAE;YAAEC,EAAE,EAAE,KAAK;YAAEC,EAAE,EAAE;UAAU,CAAC;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAA9B,QAAA,gBAEtFnD,OAAA,CAAC9B,GAAG;UAAAiF,QAAA,eACFnD,OAAA,CAAC7B,IAAI;YAAC8E,EAAE,EAAE;cAAEiC,YAAY,EAAE;YAAE,CAAE;YAAA/B,QAAA,eAC5BnD,OAAA,CAAC5B,WAAW;cAAA+E,QAAA,gBACVnD,OAAA,CAAC9B,GAAG;gBAAC+E,EAAE,EAAE;kBAAE4B,OAAO,EAAE,MAAM;kBAAEM,UAAU,EAAE,QAAQ;kBAAEP,EAAE,EAAE;gBAAE,CAAE;gBAAAzB,QAAA,gBACxDnD,OAAA,CAACJ,UAAU;kBAACqD,EAAE,EAAE;oBAAEI,EAAE,EAAE,CAAC;oBAAEQ,KAAK,EAAE;kBAAe;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpDzD,OAAA,CAAC3B,UAAU;kBAACqF,OAAO,EAAC,IAAI;kBAAAP,QAAA,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,EACL7C,UAAU,gBACTZ,OAAA,CAAAE,SAAA;gBAAAiD,QAAA,gBACEnD,OAAA,CAACvB,IAAI;kBACHmF,KAAK,EAAEhD,UAAU,CAACwE,MAAO;kBACzBvB,KAAK,EAAC,SAAS;kBACfZ,EAAE,EAAE;oBAAE2B,EAAE,EAAE;kBAAE;gBAAE;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACFzD,OAAA,CAAC3B,UAAU;kBAACqF,OAAO,EAAC,OAAO;kBAACG,KAAK,EAAC,gBAAgB;kBAAAV,QAAA,GAAC,WACxC,EAACvC,UAAU,CAACyE,OAAO;gBAAA;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACbzD,OAAA,CAAC3B,UAAU;kBAACqF,OAAO,EAAC,OAAO;kBAACG,KAAK,EAAC,gBAAgB;kBAAAV,QAAA,GAAC,WACxC,EAACvC,UAAU,CAAC0E,OAAO;gBAAA;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACbzD,OAAA,CAAC3B,UAAU;kBAACqF,OAAO,EAAC,OAAO;kBAACG,KAAK,EAAC,gBAAgB;kBAAAV,QAAA,GAAC,cACrC,EAACN,eAAe,CAACjC,UAAU,CAACkC,SAAS,CAAC;gBAAA;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACbzD,OAAA,CAACtB,OAAO;kBAACuE,EAAE,EAAE;oBAAEsC,EAAE,EAAE;kBAAE;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC1BzD,OAAA,CAAC3B,UAAU;kBAACqF,OAAO,EAAC,WAAW;kBAAC8B,YAAY;kBAAArC,QAAA,EAAC;gBAE7C;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbzD,OAAA,CAACrB,IAAI;kBAAC8G,KAAK;kBAAAtC,QAAA,EACRvC,UAAU,CAAC8E,gBAAgB,CAACC,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,kBAC/C7F,OAAA,CAACpB,QAAQ;oBAAaqE,EAAE,EAAE;sBAAE6C,EAAE,EAAE;oBAAE,CAAE;oBAAA3C,QAAA,eAClCnD,OAAA,CAACnB,YAAY;sBACXkH,OAAO,EAAEH,QAAS;sBAClBI,sBAAsB,EAAE;wBAAEtC,OAAO,EAAE;sBAAQ;oBAAE;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C;kBAAC,GAJWoC,KAAK;oBAAAvC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAKV,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA,eACP,CAAC,gBAEHzD,OAAA,CAAC3B,UAAU;gBAACwF,KAAK,EAAC,gBAAgB;gBAAAV,QAAA,EAAC;cAAsB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACtE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNzD,OAAA,CAAC9B,GAAG;UAAAiF,QAAA,eACFnD,OAAA,CAAC7B,IAAI;YAAAgF,QAAA,eACHnD,OAAA,CAAC5B,WAAW;cAAA+E,QAAA,gBACVnD,OAAA,CAAC9B,GAAG;gBAAC+E,EAAE,EAAE;kBAAE4B,OAAO,EAAE,MAAM;kBAAEM,UAAU,EAAE,QAAQ;kBAAEP,EAAE,EAAE;gBAAE,CAAE;gBAAAzB,QAAA,gBACxDnD,OAAA,CAACV,SAAS;kBAAC2D,EAAE,EAAE;oBAAEI,EAAE,EAAE,CAAC;oBAAEQ,KAAK,EAAE;kBAAe;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnDzD,OAAA,CAAC3B,UAAU;kBAACqF,OAAO,EAAC,IAAI;kBAAAP,QAAA,EAAC;gBAAiB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,EAEL3C,SAAS,gBACRd,OAAA,CAAAE,SAAA;gBAAAiD,QAAA,gBACEnD,OAAA,CAACvB,IAAI;kBACHmF,KAAK,EAAE9C,SAAS,CAACmF,KAAK,GAAG,OAAO,GAAG,SAAU;kBAC7CpC,KAAK,EAAE/C,SAAS,CAACmF,KAAK,GAAG,SAAS,GAAG,OAAQ;kBAC7ChD,EAAE,EAAE;oBAAE2B,EAAE,EAAE;kBAAE;gBAAE;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,EACD3C,SAAS,CAACmF,KAAK,iBACdjG,OAAA,CAAAE,SAAA;kBAAAiD,QAAA,gBACEnD,OAAA,CAAC3B,UAAU;oBAACqF,OAAO,EAAC,OAAO;oBAACG,KAAK,EAAC,gBAAgB;oBAAAV,QAAA,GAAC,QAC3C,EAACrC,SAAS,CAACoF,QAAQ;kBAAA;oBAAA5C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,eACbzD,OAAA,CAAC3B,UAAU;oBAACqF,OAAO,EAAC,OAAO;oBAACG,KAAK,EAAC,gBAAgB;oBAAAV,QAAA,GAAC,WACxC,EAACrC,SAAS,CAACqF,OAAO;kBAAA;oBAAA7C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,EACZ3C,SAAS,CAACsF,UAAU,iBACnBpG,OAAA,CAAC3B,UAAU;oBAACqF,OAAO,EAAC,OAAO;oBAACG,KAAK,EAAC,gBAAgB;oBAAAV,QAAA,GAAC,WACxC,EAACN,eAAe,CAAC/B,SAAS,CAACsF,UAAU,CAAC;kBAAA;oBAAA9C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CACb,EACA3C,SAAS,CAACuF,MAAM,iBACfrG,OAAA,CAAC3B,UAAU;oBAACqF,OAAO,EAAC,OAAO;oBAACG,KAAK,EAAC,gBAAgB;oBAAAV,QAAA,GAAC,UACzC,EAACrC,SAAS,CAACuF,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC;kBAAA;oBAAAhD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CACb;gBAAA,eACD,CACH;cAAA,eACD,CAAC,gBAEHzD,OAAA,CAAC3B,UAAU;gBAACwF,KAAK,EAAC,gBAAgB;gBAAAV,QAAA,EAAC;cAAqB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACrE,eAEDzD,OAAA,CAACtB,OAAO;gBAACuE,EAAE,EAAE;kBAAEsC,EAAE,EAAE;gBAAE;cAAE;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAE1BzD,OAAA,CAAC3B,UAAU;gBAACqF,OAAO,EAAC,WAAW;gBAAC8B,YAAY;gBAAArC,QAAA,EAAC;cAE7C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzD,OAAA,CAAC3B,UAAU;gBAACqF,OAAO,EAAC,OAAO;gBAACG,KAAK,EAAC,gBAAgB;gBAACZ,EAAE,EAAE;kBAAEsD,SAAS,EAAE,WAAW;kBAAE3B,EAAE,EAAE;gBAAE,CAAE;gBAAAzB,QAAA,EACtFT,kBAAkB,CAACxB,WAAW;cAAC;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eAEbzD,OAAA,CAAC3B,UAAU;gBAACqF,OAAO,EAAC,WAAW;gBAAC8B,YAAY;gBAAArC,QAAA,EAAC;cAE7C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzD,OAAA,CAAC3B,UAAU;gBAACqF,OAAO,EAAC,OAAO;gBAACG,KAAK,EAAC,gBAAgB;gBAACZ,EAAE,EAAE;kBAAEsD,SAAS,EAAE;gBAAY,CAAE;gBAAApD,QAAA,EAC/ET,kBAAkB,CAACtB,YAAY;cAAC;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzD,OAAA,CAAC9B,GAAG;QAAC+E,EAAE,EAAE;UAAEuD,EAAE,EAAE;QAAE,CAAE;QAAArD,QAAA,eACfnD,OAAA,CAAC7B,IAAI;UAAAgF,QAAA,eACHnD,OAAA,CAAC5B,WAAW;YAAA+E,QAAA,gBACVnD,OAAA,CAAC3B,UAAU;cAACqF,OAAO,EAAC,IAAI;cAAC8B,YAAY;cAAArC,QAAA,EAAC;YAEtC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbzD,OAAA,CAAC9B,GAAG;cAAC+E,EAAE,EAAE;gBAAE4B,OAAO,EAAE,MAAM;gBAAEI,GAAG,EAAE,CAAC;gBAAEwB,QAAQ,EAAE;cAAO,CAAE;cAAAtD,QAAA,gBACrDnD,OAAA,CAAC1B,MAAM;gBACLoF,OAAO,EAAC,WAAW;gBACnBgD,SAAS,EAAEpG,OAAO,gBAAGN,OAAA,CAACxB,gBAAgB;kBAACsF,IAAI,EAAE;gBAAG;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGzD,OAAA,CAACR,WAAW;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACtEO,OAAO,EAAE/B,kBAAmB;gBAC5B0E,QAAQ,EAAErG,OAAO,IAAI,CAACc,YAAa;gBAAA+B,QAAA,EACpC;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTzD,OAAA,CAAC1B,MAAM;gBACLoF,OAAO,EAAC,UAAU;gBAClBgD,SAAS,eAAE1G,OAAA,CAACJ,UAAU;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC1BO,OAAO,EAAE1C,WAAY;gBACrBqF,QAAQ,EAAErG,OAAQ;gBAAA6C,QAAA,EACnB;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTzD,OAAA,CAAC1B,MAAM;gBACLoF,OAAO,EAAC,UAAU;gBAClBgD,SAAS,eAAE1G,OAAA,CAACV,SAAS;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBO,OAAO,EAAEzC,oBAAqB;gBAC9BoF,QAAQ,EAAErG,OAAO,IAAI,CAACY,WAAY;gBAAAiC,QAAA,EACnC;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTzD,OAAA,CAAC1B,MAAM;gBACLoF,OAAO,EAAC,UAAU;gBAClBG,KAAK,EAAC,OAAO;gBACb6C,SAAS,eAAE1G,OAAA,CAACN,UAAU;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC1BO,OAAO,EAAE5B,YAAa;gBACtBuE,QAAQ,EAAErG,OAAQ;gBAAA6C,QAAA,EACnB;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpD,EAAA,CArTWF,SAAmC;AAAAyG,EAAA,GAAnCzG,SAAmC;AAAA,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}