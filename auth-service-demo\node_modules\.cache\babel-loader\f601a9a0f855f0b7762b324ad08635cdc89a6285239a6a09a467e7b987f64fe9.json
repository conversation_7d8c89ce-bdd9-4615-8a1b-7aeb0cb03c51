{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment\\\\project-tracker\\\\auth-service-demo\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect, createContext, useContext } from 'react';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport { CssBaseline, Box, CircularProgress } from '@mui/material';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { LoginForm } from './components/LoginForm';\nimport { Dashboard } from './components/Dashboard';\nimport { MockAuthService } from './services/mockAuthService';\n\n// Theme context\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ThemeContext = /*#__PURE__*/createContext({\n  darkMode: false,\n  toggleTheme: () => {}\n});\nexport const useTheme = () => {\n  _s();\n  return useContext(ThemeContext);\n};\n\n// Create Material-UI theme\n_s(useTheme, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#1976d2'\n    },\n    secondary: {\n      main: '#dc004e'\n    },\n    background: {\n      default: '#f5f5f5'\n    }\n  },\n  typography: {\n    fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif'\n  },\n  components: {\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          borderRadius: 16\n        }\n      }\n    },\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          borderRadius: 12,\n          textTransform: 'none'\n        }\n      }\n    },\n    MuiPaper: {\n      styleOverrides: {\n        root: {\n          borderRadius: 16\n        }\n      }\n    },\n    MuiTextField: {\n      styleOverrides: {\n        root: {\n          '& .MuiOutlinedInput-root': {\n            borderRadius: 12\n          }\n        }\n      }\n    }\n  }\n});\nfunction App() {\n  _s2();\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    checkAuthStatus();\n  }, []);\n  const checkAuthStatus = async () => {\n    setLoading(true);\n    try {\n      const token = MockAuthService.getAccessToken();\n      if (token) {\n        // Validate the stored token\n        const validation = await MockAuthService.validateToken({\n          token\n        });\n        setIsAuthenticated(validation.valid);\n      } else {\n        setIsAuthenticated(false);\n      }\n    } catch (error) {\n      console.warn('Auth check failed:', error);\n      setIsAuthenticated(false);\n      // Clear invalid tokens\n      await MockAuthService.logout();\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleLoginSuccess = () => {\n    setIsAuthenticated(true);\n  };\n  const handleLogout = () => {\n    setIsAuthenticated(false);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n      theme: theme,\n      children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          minHeight: '100vh'\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 60\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: theme,\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: isAuthenticated ? /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/dashboard\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(LoginForm, {\n            onLoginSuccess: handleLoginSuccess\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/dashboard\",\n          element: isAuthenticated ? /*#__PURE__*/_jsxDEV(Dashboard, {\n            onLogout: handleLogout\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/login\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: isAuthenticated ? \"/dashboard\" : \"/login\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n}\n_s2(App, \"Ax+B/BOELR06clzL32vENhaokmk=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "createContext", "useContext", "ThemeProvider", "createTheme", "CssBaseline", "Box", "CircularProgress", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "LoginForm", "Dashboard", "MockAuthService", "jsxDEV", "_jsxDEV", "ThemeContext", "darkMode", "toggleTheme", "useTheme", "_s", "theme", "palette", "primary", "main", "secondary", "background", "default", "typography", "fontFamily", "components", "MuiCard", "styleOverrides", "root", "borderRadius", "MuiB<PERSON>on", "textTransform", "MuiPaper", "MuiTextField", "App", "_s2", "isAuthenticated", "setIsAuthenticated", "loading", "setLoading", "checkAuthStatus", "token", "getAccessToken", "validation", "validateToken", "valid", "error", "console", "warn", "logout", "handleLoginSuccess", "handleLogout", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "display", "justifyContent", "alignItems", "minHeight", "size", "path", "element", "to", "replace", "onLoginSuccess", "onLogout", "_c", "$RefreshReg$"], "sources": ["D:/Augment/project-tracker/auth-service-demo/src/App.tsx"], "sourcesContent": ["import React, { useState, useEffect, createContext, useContext } from 'react';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport { CssBaseline, Box, CircularProgress, IconButton, Fab } from '@mui/material';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { Brightness4, Brightness7 } from '@mui/icons-material';\nimport { LoginForm } from './components/LoginForm';\nimport { Dashboard } from './components/Dashboard';\nimport { MockAuthService } from './services/mockAuthService';\n\n// Theme context\nconst ThemeContext = createContext({\n  darkMode: false,\n  toggleTheme: () => {},\n});\n\nexport const useTheme = () => useContext(ThemeContext);\n\n// Create Material-UI theme\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#1976d2',\n    },\n    secondary: {\n      main: '#dc004e',\n    },\n    background: {\n      default: '#f5f5f5',\n    },\n  },\n  typography: {\n    fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n  },\n  components: {\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          borderRadius: 16,\n        },\n      },\n    },\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          borderRadius: 12,\n          textTransform: 'none',\n        },\n      },\n    },\n    MuiPaper: {\n      styleOverrides: {\n        root: {\n          borderRadius: 16,\n        },\n      },\n    },\n    MuiTextField: {\n      styleOverrides: {\n        root: {\n          '& .MuiOutlinedInput-root': {\n            borderRadius: 12,\n          },\n        },\n      },\n    },\n  },\n});\n\nfunction App() {\n  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);\n  const [loading, setLoading] = useState<boolean>(true);\n\n  useEffect(() => {\n    checkAuthStatus();\n  }, []);\n\n  const checkAuthStatus = async () => {\n    setLoading(true);\n\n    try {\n      const token = MockAuthService.getAccessToken();\n      if (token) {\n        // Validate the stored token\n        const validation = await MockAuthService.validateToken({ token });\n        setIsAuthenticated(validation.valid);\n      } else {\n        setIsAuthenticated(false);\n      }\n    } catch (error) {\n      console.warn('Auth check failed:', error);\n      setIsAuthenticated(false);\n      // Clear invalid tokens\n      await MockAuthService.logout();\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleLoginSuccess = () => {\n    setIsAuthenticated(true);\n  };\n\n  const handleLogout = () => {\n    setIsAuthenticated(false);\n  };\n\n  if (loading) {\n    return (\n      <ThemeProvider theme={theme}>\n        <CssBaseline />\n        <Box\n          sx={{\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            minHeight: '100vh',\n          }}\n        >\n          <CircularProgress size={60} />\n        </Box>\n      </ThemeProvider>\n    );\n  }\n\n  return (\n    <ThemeProvider theme={theme}>\n      <CssBaseline />\n      <Router>\n        <Routes>\n          <Route\n            path=\"/login\"\n            element={\n              isAuthenticated ? (\n                <Navigate to=\"/dashboard\" replace />\n              ) : (\n                <LoginForm onLoginSuccess={handleLoginSuccess} />\n              )\n            }\n          />\n          <Route\n            path=\"/dashboard\"\n            element={\n              isAuthenticated ? (\n                <Dashboard onLogout={handleLogout} />\n              ) : (\n                <Navigate to=\"/login\" replace />\n              )\n            }\n          />\n          <Route\n            path=\"/\"\n            element={\n              <Navigate to={isAuthenticated ? \"/dashboard\" : \"/login\"} replace />\n            }\n          />\n        </Routes>\n      </Router>\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,aAAa,EAAEC,UAAU,QAAQ,OAAO;AAC7E,SAASC,aAAa,EAAEC,WAAW,QAAQ,sBAAsB;AACjE,SAASC,WAAW,EAAEC,GAAG,EAAEC,gBAAgB,QAAyB,eAAe;AACnF,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AAEnF,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,eAAe,QAAQ,4BAA4B;;AAE5D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,gBAAGjB,aAAa,CAAC;EACjCkB,QAAQ,EAAE,KAAK;EACfC,WAAW,EAAEA,CAAA,KAAM,CAAC;AACtB,CAAC,CAAC;AAEF,OAAO,MAAMC,QAAQ,GAAGA,CAAA;EAAAC,EAAA;EAAA,OAAMpB,UAAU,CAACgB,YAAY,CAAC;AAAA;;AAEtD;AAAAI,EAAA,CAFaD,QAAQ;AAGrB,MAAME,KAAK,GAAGnB,WAAW,CAAC;EACxBoB,OAAO,EAAE;IACPC,OAAO,EAAE;MACPC,IAAI,EAAE;IACR,CAAC;IACDC,SAAS,EAAE;MACTD,IAAI,EAAE;IACR,CAAC;IACDE,UAAU,EAAE;MACVC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,UAAU,EAAE;IACVC,UAAU,EAAE;EACd,CAAC;EACDC,UAAU,EAAE;IACVC,OAAO,EAAE;MACPC,cAAc,EAAE;QACdC,IAAI,EAAE;UACJC,YAAY,EAAE;QAChB;MACF;IACF,CAAC;IACDC,SAAS,EAAE;MACTH,cAAc,EAAE;QACdC,IAAI,EAAE;UACJC,YAAY,EAAE,EAAE;UAChBE,aAAa,EAAE;QACjB;MACF;IACF,CAAC;IACDC,QAAQ,EAAE;MACRL,cAAc,EAAE;QACdC,IAAI,EAAE;UACJC,YAAY,EAAE;QAChB;MACF;IACF,CAAC;IACDI,YAAY,EAAE;MACZN,cAAc,EAAE;QACdC,IAAI,EAAE;UACJ,0BAA0B,EAAE;YAC1BC,YAAY,EAAE;UAChB;QACF;MACF;IACF;EACF;AACF,CAAC,CAAC;AAEF,SAASK,GAAGA,CAAA,EAAG;EAAAC,GAAA;EACb,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG7C,QAAQ,CAAU,KAAK,CAAC;EACtE,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAU,IAAI,CAAC;EAErDC,SAAS,CAAC,MAAM;IACd+C,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClCD,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAME,KAAK,GAAGjC,eAAe,CAACkC,cAAc,CAAC,CAAC;MAC9C,IAAID,KAAK,EAAE;QACT;QACA,MAAME,UAAU,GAAG,MAAMnC,eAAe,CAACoC,aAAa,CAAC;UAAEH;QAAM,CAAC,CAAC;QACjEJ,kBAAkB,CAACM,UAAU,CAACE,KAAK,CAAC;MACtC,CAAC,MAAM;QACLR,kBAAkB,CAAC,KAAK,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,oBAAoB,EAAEF,KAAK,CAAC;MACzCT,kBAAkB,CAAC,KAAK,CAAC;MACzB;MACA,MAAM7B,eAAe,CAACyC,MAAM,CAAC,CAAC;IAChC,CAAC,SAAS;MACRV,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMW,kBAAkB,GAAGA,CAAA,KAAM;IAC/Bb,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMc,YAAY,GAAGA,CAAA,KAAM;IACzBd,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;EAED,IAAIC,OAAO,EAAE;IACX,oBACE5B,OAAA,CAACd,aAAa;MAACoB,KAAK,EAAEA,KAAM;MAAAoC,QAAA,gBAC1B1C,OAAA,CAACZ,WAAW;QAAAuD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACf9C,OAAA,CAACX,GAAG;QACF0D,EAAE,EAAE;UACFC,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,QAAQ;UACxBC,UAAU,EAAE,QAAQ;UACpBC,SAAS,EAAE;QACb,CAAE;QAAAT,QAAA,eAEF1C,OAAA,CAACV,gBAAgB;UAAC8D,IAAI,EAAE;QAAG;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAEpB;EAEA,oBACE9C,OAAA,CAACd,aAAa;IAACoB,KAAK,EAAEA,KAAM;IAAAoC,QAAA,gBAC1B1C,OAAA,CAACZ,WAAW;MAAAuD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACf9C,OAAA,CAACR,MAAM;MAAAkD,QAAA,eACL1C,OAAA,CAACP,MAAM;QAAAiD,QAAA,gBACL1C,OAAA,CAACN,KAAK;UACJ2D,IAAI,EAAC,QAAQ;UACbC,OAAO,EACL5B,eAAe,gBACb1B,OAAA,CAACL,QAAQ;YAAC4D,EAAE,EAAC,YAAY;YAACC,OAAO;UAAA;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEpC9C,OAAA,CAACJ,SAAS;YAAC6D,cAAc,EAAEjB;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAEnD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACF9C,OAAA,CAACN,KAAK;UACJ2D,IAAI,EAAC,YAAY;UACjBC,OAAO,EACL5B,eAAe,gBACb1B,OAAA,CAACH,SAAS;YAAC6D,QAAQ,EAAEjB;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAErC9C,OAAA,CAACL,QAAQ;YAAC4D,EAAE,EAAC,QAAQ;YAACC,OAAO;UAAA;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAElC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACF9C,OAAA,CAACN,KAAK;UACJ2D,IAAI,EAAC,GAAG;UACRC,OAAO,eACLtD,OAAA,CAACL,QAAQ;YAAC4D,EAAE,EAAE7B,eAAe,GAAG,YAAY,GAAG,QAAS;YAAC8B,OAAO;UAAA;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACnE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEpB;AAACrB,GAAA,CA3FQD,GAAG;AAAAmC,EAAA,GAAHnC,GAAG;AA6FZ,eAAeA,GAAG;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}