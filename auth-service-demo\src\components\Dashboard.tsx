import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  Grid,
  Alert,
  CircularProgress,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemText,
  AppBar,
  Toolbar,
  IconButton,
  Menu,
  MenuItem
} from '@mui/material';
import {
  Security as SecurityIcon,
  Token as TokenIcon,
  Refresh as RefreshIcon,
  Logout as LogoutIcon,
  HealthAndSafety as HealthIcon,
  AccountCircle
} from '@mui/icons-material';
import { MockAuthService } from '../services/mockAuthService';
import { HealthResponse, ValidationResponse } from '../types/auth';

interface DashboardProps {
  onLogout: () => void;
}

export const Dashboard: React.FC<DashboardProps> = ({ onLogout }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [healthData, setHealthData] = useState<HealthResponse | null>(null);
  const [tokenData, setTokenData] = useState<ValidationResponse | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const accessToken = MockAuthService.getAccessToken();
  const refreshToken = MockAuthService.getRefreshToken();

  useEffect(() => {
    checkHealth();
    validateCurrentToken();
  }, []);

  const checkHealth = async () => {
    try {
      const health = await MockAuthService.healthCheck();
      setHealthData(health);
    } catch (err: any) {
      console.warn('Health check failed:', err.message);
    }
  };

  const validateCurrentToken = async () => {
    if (!accessToken) return;

    try {
      const validation = await MockAuthService.validateToken({ token: accessToken });
      setTokenData(validation);
    } catch (err: any) {
      console.warn('Token validation failed:', err.message);
    }
  };

  const handleRefreshToken = async () => {
    if (!refreshToken) {
      setError('No refresh token available');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      await MockAuthService.refreshToken({ refresh_token: refreshToken, grant_type: 'refresh_token' });
      setSuccess('Token refreshed successfully');
      await validateCurrentToken();
    } catch (err: any) {
      setError(err.message || 'Token refresh failed');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    setLoading(true);
    try {
      await MockAuthService.logout();
      onLogout();
    } catch (err: any) {
      console.warn('Logout error:', err.message);
      onLogout(); // Logout anyway
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const formatTokenPreview = (token: string | null): string => {
    if (!token) return 'Not available';
    return `${token.substring(0, 20)}...${token.substring(token.length - 10)}`;
  };

  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* App Bar */}
      <AppBar position="static">
        <Toolbar>
          <SecurityIcon sx={{ mr: 2 }} />
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            Auth Service Demo Dashboard
          </Typography>
          <Chip 
            label="DEMO MODE" 
            color="secondary" 
            size="small" 
            sx={{ mr: 2, fontWeight: 'bold' }}
          />
          <IconButton
            size="large"
            aria-label="account menu"
            aria-controls="menu-appbar"
            aria-haspopup="true"
            onClick={handleMenuOpen}
            color="inherit"
          >
            <AccountCircle />
          </IconButton>
          <Menu
            id="menu-appbar"
            anchorEl={anchorEl}
            anchorOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            keepMounted
            transformOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
          >
            <MenuItem onClick={handleLogout}>
              <LogoutIcon sx={{ mr: 1 }} />
              Logout
            </MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>

      {/* Main Content */}
      <Box sx={{ p: 3 }}>
        {/* Demo Notice */}
        <Alert severity="info" sx={{ mb: 3 }}>
          <strong>Demo Mode:</strong> This is a demonstration of the React/TypeScript UI with mock data. 
          In production, this would connect to the real C++23 auth-service backend.
        </Alert>

        {/* Alerts */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}
        {success && (
          <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
            {success}
          </Alert>
        )}

        <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 3 }}>
          {/* Service Health */}
          <Box>
            <Card sx={{ borderRadius: 3 }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <HealthIcon sx={{ mr: 1, color: 'success.main' }} />
                  <Typography variant="h6">Service Health</Typography>
                </Box>
                {healthData ? (
                  <>
                    <Chip 
                      label={healthData.status} 
                      color="success" 
                      sx={{ mb: 2 }} 
                    />
                    <Typography variant="body2" color="text.secondary">
                      Service: {healthData.service}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Version: {healthData.version}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Last Check: {formatTimestamp(healthData.timestamp)}
                    </Typography>
                    <Divider sx={{ my: 2 }} />
                    <Typography variant="subtitle2" gutterBottom>
                      Available Endpoints:
                    </Typography>
                    <List dense>
                      {healthData.oauth2_endpoints.map((endpoint, index) => (
                        <ListItem key={index} sx={{ py: 0 }}>
                          <ListItemText 
                            primary={endpoint} 
                            primaryTypographyProps={{ variant: 'body2' }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </>
                ) : (
                  <Typography color="text.secondary">Loading health data...</Typography>
                )}
              </CardContent>
            </Card>
          </Box>

          {/* Token Information */}
          <Box>
            <Card sx={{ borderRadius: 3 }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <TokenIcon sx={{ mr: 1, color: 'primary.main' }} />
                  <Typography variant="h6">Token Information</Typography>
                </Box>
                
                {tokenData ? (
                  <>
                    <Chip 
                      label={tokenData.valid ? 'Valid' : 'Invalid'} 
                      color={tokenData.valid ? 'success' : 'error'} 
                      sx={{ mb: 2 }} 
                    />
                    {tokenData.valid && (
                      <>
                        <Typography variant="body2" color="text.secondary">
                          User: {tokenData.username}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          User ID: {tokenData.user_id}
                        </Typography>
                        {tokenData.expires_at && (
                          <Typography variant="body2" color="text.secondary">
                            Expires: {formatTimestamp(tokenData.expires_at)}
                          </Typography>
                        )}
                        {tokenData.scopes && (
                          <Typography variant="body2" color="text.secondary">
                            Scopes: {tokenData.scopes.join(', ')}
                          </Typography>
                        )}
                      </>
                    )}
                  </>
                ) : (
                  <Typography color="text.secondary">Loading token data...</Typography>
                )}

                <Divider sx={{ my: 2 }} />
                
                <Typography variant="subtitle2" gutterBottom>
                  Access Token:
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ wordBreak: 'break-all', mb: 1 }}>
                  {formatTokenPreview(accessToken)}
                </Typography>
                
                <Typography variant="subtitle2" gutterBottom>
                  Refresh Token:
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ wordBreak: 'break-all' }}>
                  {formatTokenPreview(refreshToken)}
                </Typography>
              </CardContent>
            </Card>
          </Box>
        </Box>

        {/* Actions */}
        <Box sx={{ mt: 3 }}>
            <Card sx={{ borderRadius: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Token Actions
                </Typography>
                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                  <Button
                    variant="contained"
                    startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}
                    onClick={handleRefreshToken}
                    disabled={loading || !refreshToken}
                    sx={{ borderRadius: 2 }}
                  >
                    Refresh Token
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<HealthIcon />}
                    onClick={checkHealth}
                    disabled={loading}
                    sx={{ borderRadius: 2 }}
                  >
                    Check Health
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<TokenIcon />}
                    onClick={validateCurrentToken}
                    disabled={loading || !accessToken}
                    sx={{ borderRadius: 2 }}
                  >
                    Validate Token
                  </Button>
                  <Button
                    variant="outlined"
                    color="error"
                    startIcon={<LogoutIcon />}
                    onClick={handleLogout}
                    disabled={loading}
                    sx={{ borderRadius: 2 }}
                  >
                    Logout
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Box>
      </Box>
    </Box>
  );
};
