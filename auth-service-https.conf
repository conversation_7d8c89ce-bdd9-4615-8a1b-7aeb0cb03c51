server {
    listen 80;
    server_name auth-dev.chcit.org;
    return 301 https://auth-dev.chcit.org$request_uri;
}

server {
    listen 443 ssl http2;
    server_name auth-dev.chcit.org;
    
    ssl_certificate /etc/letsencrypt/live/chcit.org/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/chcit.org/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    location / {
        root /opt/auth-service-ui/html;
        index index.html;
        try_files $uri $uri/ =404;
    }
    
    location /oauth/ {
        proxy_pass http://127.0.0.1:8083/oauth/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /health {
        proxy_pass http://127.0.0.1:8083/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
