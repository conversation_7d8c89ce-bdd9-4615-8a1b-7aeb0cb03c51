# Auth Service UI Deployment Requirements

## 📁 **New Directory Structure**

The Auth Service UI has been moved from `/var/www/` to the proper `/opt/` location:

```
/opt/auth-service-ui/
├── html/                    # Web UI files
│   └── index.html          # Main dashboard interface
├── config/                 # Configuration files
│   └── .htpasswd          # Admin access credentials (future)
└── logs/                  # UI-specific logs
```

## 🔧 **Updated Nginx Configuration**

**File:** `/etc/nginx/sites-available/auth-service`

**Key Changes:**
- **Root directory changed** from `/var/www/auth-ui` to `/opt/auth-service-ui/html`
- **Proper SSL configuration** maintained
- **OAuth proxy endpoints** preserved
- **Health check endpoints** maintained

**Configuration snippet:**
```nginx
server {
    listen 443 ssl;
    server_name auth-dev.chcit.org;
    
    ssl_certificate /etc/letsencrypt/live/chcit.org/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/chcit.org/privkey.pem;
    
    root /opt/auth-service-ui/html;  # NEW LOCATION
    index index.html;
    
    location / {
        try_files $uri $uri/ =404;
    }
    
    location /oauth/ {
        proxy_pass http://localhost:8083/oauth/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /health {
        proxy_pass http://localhost:8083/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🛠️ **Deployment Script Updates Required**

### **1. Update Directory Creation**
```bash
# Replace old /var/www/auth-ui-react creation with:
sudo mkdir -p /opt/auth-service-ui/{html,config,logs}
sudo chown -R www-data:www-data /opt/auth-service-ui
sudo chmod -R 755 /opt/auth-service-ui
```

### **2. Update File Deployment**
```bash
# Replace old deployment path:
# OLD: sudo cp auth-ui.html /var/www/auth-ui-react/index.html
# NEW:
sudo cp auth-ui.html /opt/auth-service-ui/html/index.html
```

### **3. Update Nginx Configuration**
```bash
# Ensure nginx config points to new location
sudo sed -i 's|root /var/www/auth-ui|root /opt/auth-service-ui/html|g' /etc/nginx/sites-available/auth-service
sudo nginx -t && sudo systemctl reload nginx
```

## 📋 **Dependencies Configuration**

### **Auth-Dev Server Requirements**
```json
{
  "server": "auth-dev.chcit.org",
  "role": "development",
  "ui_dependencies": [
    "nginx",
    "ssl-certificates",
    "auth-service-backend"
  ],
  "ui_location": "/opt/auth-service-ui",
  "features": [
    "complete-dashboard",
    "admin-panel",
    "token-management",
    "system-health",
    "user-profile"
  ]
}
```

### **AuthBE Server Requirements**
```json
{
  "server": "authbe.chcit.org",
  "role": "backend-production",
  "ui_dependencies": [
    "nginx",
    "ssl-certificates",
    "auth-service-backend",
    "postgresql",
    "valkey"
  ],
  "ui_location": "/opt/auth-service-ui",
  "features": [
    "admin-panel",
    "system-monitoring",
    "production-controls"
  ]
}
```

### **AuthFE Server Requirements**
```json
{
  "server": "authfe.chcit.org", 
  "role": "frontend-production",
  "ui_dependencies": [
    "nginx",
    "ssl-certificates"
  ],
  "ui_location": "/opt/auth-service-ui",
  "features": [
    "user-authentication",
    "basic-dashboard",
    "profile-management"
  ]
}
```

## 🎯 **Complete UI Features Deployed**

### **✅ Admin Panel**
- **User Management**: List, Create, Delete users
- **System Control**: View logs, Clear sessions, Restart service
- **Configuration**: View config, Reload config, Backup system

### **✅ Token Management**
- **Token Operations**: Refresh, Validate, Revoke tokens
- **Token Display**: Current access token with details
- **Security Features**: Token validation and expiration tracking

### **✅ User Profile**
- **Profile Editing**: Username, email, password changes
- **Account Information**: User ID, status, last login
- **Security Settings**: Password management

### **✅ System Health**
- **Health Monitoring**: System status, uptime, resource usage
- **Database Health**: Connection status, performance metrics
- **Service Status**: All system services monitoring

### **✅ Authentication**
- **OAuth 2.0 Integration**: Full token-based authentication
- **Login/Logout**: Secure session management
- **Demo Mode**: Skip login for testing/development

## 🚀 **Access Instructions**

1. **Visit**: `https://auth-dev.chcit.org`
2. **Login Options**:
   - **Real Login**: Use `testuser` / `testpass123`
   - **Demo Mode**: Click "🚀 Go to Dashboard" button
3. **Admin Panel**: Click "🛠️ Admin Panel" tab after login
4. **All Features**: Navigate between Overview, Token Management, Profile, Admin Panel, System Health

## 📝 **Verification Commands**

```bash
# Check UI deployment
curl -s https://auth-dev.chcit.org | grep "Auth Service Dashboard"

# Verify directory structure
ls -la /opt/auth-service-ui/

# Check nginx configuration
sudo grep "root.*opt.*auth-service-ui" /etc/nginx/sites-available/auth-service

# Test nginx config
sudo nginx -t

# Reload nginx
sudo systemctl reload nginx
```

## 🔄 **Migration Notes**

- **Old Location**: `/var/www/auth-ui` (deprecated)
- **New Location**: `/opt/auth-service-ui/html` (active)
- **Nginx Updated**: Configuration points to new location
- **Permissions**: `www-data:www-data` ownership maintained
- **SSL**: Existing certificates preserved
- **Functionality**: All features working in new location

## ⚠️ **Important**

- **Update all deployment scripts** to use `/opt/auth-service-ui/html/`
- **Update requirements.json files** for all environments
- **Test deployment** on each server after script updates
- **Remove old `/var/www/auth-ui*` directories** after verification
