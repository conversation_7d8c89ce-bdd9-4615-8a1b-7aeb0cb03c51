# Auth Service - OAuth 2.0 Authentication System

A comprehensive OAuth 2.0 authentication service built with C++23 and featuring a modern web interface with role-based access control.

## Project Structure

```
auth-service/
├── auth-service-app/           # C++23 Backend Application
│   ├── src/                    # C++ source files
│   ├── include/                # C++ header files
│   ├── config/                 # Configuration files
│   │   ├── auth-service.conf   # Current configuration
│   │   └── auth-service-enhanced.conf  # Enhanced RBAC configuration
│   ├── database/               # Database schemas and scripts
│   │   ├── auth_schema.sql     # Original database schema
│   │   ├── enhanced_auth_schema.sql    # Enhanced RBAC schema
│   │   ├── update_database.sh  # Database update script
│   │   └── update_password.sql # Password update utilities
│   ├── tools/                  # Development utilities
│   │   ├── hash-generator.cpp  # Password hash generator
│   │   └── build-hash-generator.sh  # Build script for hash tool
│   ├── tests/                  # Testing scripts
│   │   └── test_validation.sh  # OAuth validation tests
│   └── CMakeLists.txt          # Build configuration
│
├── auth-service-ui/            # Frontend User Interface
│   ├── html/                   # Current HTML-based UI (deployed)
│   │   ├── index.html          # Main login page
│   │   ├── admin.html          # Admin dashboard
│   │   └── dashboard.html      # User dashboard
│   ├── deployment/             # Deployment configurations
│   │   ├── nginx-ssl-config.conf      # HTTPS nginx configuration
│   │   ├── nginx-rate-limits.conf     # Rate limiting configuration
│   │   ├── build-react-simple.sh      # React build script
│   │   └── deploy-react-ui.sh         # React deployment script
│   ├── src/                    # React/TypeScript UI (future)
│   │   ├── components/         # React components
│   │   ├── services/           # API services
│   │   └── types/              # TypeScript types
│   ├── public/                 # React public assets
│   ├── package.json            # Node.js dependencies
│   └── tsconfig.json           # TypeScript configuration
│
├── docs/                       # Documentation
│   └── certificate-management.md  # SSL certificate management
│
├── cert_sync_helper/           # Certificate synchronization (legacy)
├── react-ui/                   # Alternative React implementation (to be consolidated)
├── auth-service-template-ui/   # Template UI (to be consolidated)
│
└── README.md                   # This file
```

## Features

### Current Implementation
- ✅ **OAuth 2.0 Core**: Token generation, validation, refresh, revocation
- ✅ **User Authentication**: Secure login with Argon2ID password hashing
- ✅ **Role-Based Access Control**: Admin and user roles with permissions
- ✅ **Multi-tenant Architecture**: Organizations and projects support
- ✅ **Modern UI**: Dark blue theme with glass morphism effects
- ✅ **HTTPS Support**: SSL/TLS encryption with wildcard certificates

### Database Schema
- **Enhanced RBAC**: Comprehensive role and permission system
- **Multi-tenant**: Organizations and projects structure
- **User Management**: Enhanced user table with admin flags
- **Token Tracking**: Project-specific and enhanced token management

### User Accounts
- **testuser** / `testpass123` - Standard user with viewer role
- **btaylor-admin** / `AdminPass123!` - System administrator with full access

## Development

### Backend (C++23)
```bash
cd auth-service-app
mkdir build && cd build
cmake ..
make
```

### Frontend (React/TypeScript - Future)
```bash
cd auth-service-ui
npm install
npm start
```

### Database Setup
```bash
cd auth-service-app/database
chmod +x update_database.sh
sudo ./update_database.sh
```

## Deployment

The HTML-based UI is currently deployed to `/opt/auth-service-ui/html/` on the server.

### Access URLs
- **Main Login**: https://auth-dev.chcit.org/
- **Admin Dashboard**: https://auth-dev.chcit.org/admin.html (admin users only)
- **User Dashboard**: https://auth-dev.chcit.org/dashboard.html

## Configuration

### Database Configuration
- **Database**: `auth_service_db`
- **User**: `auth_service_user`
- **Host**: `127.0.0.1:5432`

### OAuth 2.0 Settings
- **Access Token Expiry**: 3600 seconds (1 hour)
- **Refresh Token Expiry**: 6048000 seconds (70 days)
- **JWT Algorithm**: HS256

## Security Features

- **Argon2ID Password Hashing**: Industry-standard password security
- **JWT Tokens**: Secure token-based authentication
- **Rate Limiting**: Protection against brute force attacks
- **HTTPS Only**: All communications encrypted
- **Role-Based Access**: Granular permission system
- **Session Management**: Secure session handling

## API Endpoints

### OAuth 2.0
- `POST /oauth/token` - Generate access token
- `POST /oauth/refresh` - Refresh access token
- `POST /oauth/validate` - Validate token
- `POST /oauth/revoke` - Revoke token

### System
- `GET /health` - Service health check

## Future Enhancements

- **React/TypeScript Migration**: Modern frontend framework
- **User Registration**: Self-service user registration
- **Admin Interface**: Comprehensive admin management
- **API Rate Limiting**: Enhanced security controls
- **Audit Logging**: Comprehensive activity tracking
- **WebSocket Support**: Real-time communication

## License

Private project for CHC IT infrastructure.
