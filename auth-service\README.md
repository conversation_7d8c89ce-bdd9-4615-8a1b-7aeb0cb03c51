# Auth Service - OAuth 2.0 Authentication System

A comprehensive OAuth 2.0 authentication service built with C++23 and featuring a modern web interface with role-based access control.

## Project Structure

```
auth-service/
├── auth-service-app/           # C++23 Backend Application
│   ├── src/                    # C++ source files
│   ├── include/                # C++ header files
│   ├── config/                 # Configuration files
│   │   ├── auth-service.conf   # Current configuration
│   │   └── auth-service-enhanced.conf  # Enhanced RBAC configuration
│   ├── database/               # Database schemas and scripts
│   │   ├── auth_schema.sql     # Original database schema
│   │   ├── enhanced_auth_schema.sql    # Enhanced RBAC schema
│   │   ├── update_database.sh  # Database update script
│   │   └── update_password.sql # Password update utilities
│   ├── tools/                  # Development utilities
│   │   ├── hash-generator.cpp  # Password hash generator
│   │   └── build-hash-generator.sh  # Build script for hash tool
│   ├── tests/                  # Testing scripts
│   │   └── test_validation.sh  # OAuth validation tests
│   └── CMakeLists.txt          # Build configuration
│
├── auth-service-ui/            # Frontend User Interface
│   ├── html/                   # Current HTML-based UI (deployed)
│   │   ├── index.html          # Main login page
│   │   ├── admin.html          # Admin dashboard
│   │   └── dashboard.html      # User dashboard
│   ├── deployment/             # Deployment configurations
│   │   ├── nginx-ssl-config.conf      # HTTPS nginx configuration
│   │   ├── nginx-rate-limits.conf     # Rate limiting configuration
│   │   ├── build-react-simple.sh      # React build script
│   │   └── deploy-react-ui.sh         # React deployment script
│   ├── src/                    # React/TypeScript UI (future)
│   │   ├── components/         # React components
│   │   ├── services/           # API services
│   │   └── types/              # TypeScript types
│   ├── public/                 # React public assets
│   ├── package.json            # Node.js dependencies
│   └── tsconfig.json           # TypeScript configuration
│
├── docs/                       # Documentation
│   ├── certificate-management.md  # SSL certificate management
│   └── file-organization.md       # Project file organization
│
├── auth-service-deployment/    # Deployment automation (existing)
│   ├── deployment_scripts/     # Deployment scripts
│   └── deployment_files/       # Deployment configurations
│
└── README.md                   # This file
```

## Current Status (July 2025)

### ✅ **Production Ready Components**

#### **C++23 Backend Application**
- ✅ **OAuth 2.0 Core**: Complete token lifecycle (generate, validate, refresh, revoke)
- ✅ **Enhanced Database Schema**: Multi-tenant RBAC with organizations, projects, roles, permissions
- ✅ **Security**: Argon2ID password hashing, JWT tokens, rate limiting
- ✅ **Configuration**: Enhanced RBAC configuration with multi-tenant support
- ✅ **Development Tools**: Password hash generator, comprehensive testing scripts
- ✅ **Status**: Ready for enhanced RBAC implementation

#### **HTML-Based User Interface** (Currently Deployed)
- ✅ **Production Deployment**: Live at https://auth-dev.chcit.org/
- ✅ **Modern Design**: Dark blue Vision UI theme with glass morphism effects
- ✅ **Role-Based Access**: Automatic redirect based on user permissions
- ✅ **Admin Dashboard**: Comprehensive admin interface with token management
- ✅ **User Dashboard**: Sample application demonstrating OAuth flow
- ✅ **Security**: HTTPS-only, rate limiting, security headers
- ✅ **Status**: Fully functional and actively used

#### **Infrastructure & Deployment**
- ✅ **HTTPS Configuration**: Wildcard SSL certificates (*.chcit.org)
- ✅ **Nginx Configuration**: SSL termination, rate limiting, security headers
- ✅ **Certificate Management**: Automated sync from project-tracker.chcit.org
- ✅ **Deployment Automation**: Scripts for UI deployment and configuration
- ✅ **Status**: Production-ready infrastructure

### 🔄 **Recent Major Updates (July 2025)**

#### **File Organization Overhaul**
- ✅ **Complete Reorganization**: All files moved to proper directories
- ✅ **Cleanup**: Removed 3 unused React implementations and legacy directories
- ✅ **Tool Organization**: Development utilities moved to `auth-service-app/tools/`
- ✅ **Test Organization**: Testing scripts moved to `auth-service-app/tests/`
- ✅ **Deployment Organization**: All deployment configs moved to `auth-service-ui/deployment/`

#### **Enhanced Database Schema** (Ready for Implementation)
- ✅ **Multi-tenant RBAC**: Organizations, projects, roles, permissions tables
- ✅ **Enhanced User Management**: Admin flags, organization membership
- ✅ **Project-specific Tokens**: Token tracking per project
- ✅ **Migration Scripts**: Automated database update procedures

#### **UI Enhancements** (Deployed)
- ✅ **Admin Dashboard**: Complete admin interface with token management
- ✅ **Role-based Routing**: Automatic redirect based on user permissions
- ✅ **Modern Design**: Dark blue theme with professional styling
- ✅ **Security Features**: Rate limiting, HTTPS enforcement, security headers

### 👥 **User Accounts**
- **testuser** / `testpass123` - Standard user with viewer role
- **btaylor-admin** / `AdminPass123!` - System administrator with full access

## Development

### Backend (C++23)
```bash
cd auth-service-app
mkdir build && cd build
cmake ..
make
```

### Frontend (React/TypeScript - Future)
```bash
cd auth-service-ui
npm install
npm start
```

### Database Setup
```bash
cd auth-service-app/database
chmod +x update_database.sh
sudo ./update_database.sh
```

## Deployment

The HTML-based UI is currently deployed to `/opt/auth-service-ui/html/` on the server.

### Access URLs
- **Main Login**: https://auth-dev.chcit.org/
- **Admin Dashboard**: https://auth-dev.chcit.org/admin.html (admin users only)
- **User Dashboard**: https://auth-dev.chcit.org/dashboard.html

## Configuration

### Database Configuration
- **Database**: `auth_service_db`
- **User**: `auth_service_user`
- **Host**: `127.0.0.1:5432`

### OAuth 2.0 Settings
- **Access Token Expiry**: 3600 seconds (1 hour)
- **Refresh Token Expiry**: 6048000 seconds (70 days)
- **JWT Algorithm**: HS256

## Security Features

- **Argon2ID Password Hashing**: Industry-standard password security
- **JWT Tokens**: Secure token-based authentication
- **Rate Limiting**: Protection against brute force attacks
- **HTTPS Only**: All communications encrypted
- **Role-Based Access**: Granular permission system
- **Session Management**: Secure session handling

## API Endpoints

### OAuth 2.0
- `POST /oauth/token` - Generate access token
- `POST /oauth/refresh` - Refresh access token
- `POST /oauth/validate` - Validate token
- `POST /oauth/revoke` - Revoke token

### System
- `GET /health` - Service health check

## Future Enhancements

- **React/TypeScript Migration**: Modern frontend framework
- **User Registration**: Self-service user registration
- **Admin Interface**: Comprehensive admin management
- **API Rate Limiting**: Enhanced security controls
- **Audit Logging**: Comprehensive activity tracking
- **WebSocket Support**: Real-time communication

## 📚 **Documentation**

### **Project Documentation**
- **[Current Status Report](docs/current-status.md)** - Comprehensive status of all components
- **[Deployment Status](docs/deployment-status.md)** - Live deployment status and verification
- **[Development Roadmap](docs/ROADMAP.md)** - Complete development roadmap and phases
- **[Task Tracking](docs/TASK-TRACKING.md)** - Active task tracking and sprint management
- **[Change Log](docs/CHANGELOG.md)** - Complete history of changes and updates
- **[File Organization](docs/file-organization.md)** - Project structure and organization
- **[Certificate Management](docs/certificate-management.md)** - SSL certificate procedures

### **Development Documentation**
- **[Enhanced Database Schema](auth-service-app/database/enhanced_auth_schema.sql)** - Multi-tenant RBAC schema
- **[Configuration Guide](auth-service-app/config/auth-service-enhanced.conf)** - Enhanced configuration
- **[Testing Guide](auth-service-app/tests/test_validation.sh)** - OAuth endpoint testing
- **[Deployment Guide](auth-service-ui/deployment/)** - UI deployment procedures

## 🎯 **Quick Start**

### **Access the Application**
- **Main Login**: https://auth-dev.chcit.org/
- **Admin Dashboard**: https://auth-dev.chcit.org/admin.html (admin users only)
- **User Dashboard**: https://auth-dev.chcit.org/dashboard.html

### **Test Accounts**
- **Standard User**: `testuser` / `testpass123`
- **Administrator**: `btaylor-admin` / `AdminPass123!`

### **Development**
```bash
# Build password hash generator
cd auth-service-app/tools
./build-hash-generator.sh

# Run OAuth tests
cd auth-service-app/tests
./test_validation.sh

# Deploy UI changes
cd auth-service-ui/deployment
./deploy-react-ui.sh
```

## License

Private project for CHC IT infrastructure.
