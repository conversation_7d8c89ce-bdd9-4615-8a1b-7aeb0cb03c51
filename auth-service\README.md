# Auth Service - OAuth 2.0 Authentication System

A comprehensive OAuth 2.0 authentication service built with C++23 featuring secure Argon2id password hashing, multi-tenant RBAC, and a modern web interface. **Production ready and actively deployed.**

## 🔐 **Security Highlight: Argon2id Implementation**

**Latest Achievement**: Successfully migrated from SHA256 to **Argon2id password hashing** - the gold standard for password security.

### **Why Argon2id?**
- **🛡️ Memory-Hard**: Resistant to GPU and ASIC attacks (64MB memory cost)
- **⏱️ Time-Hard**: Configurable computational cost (time cost: 3)
- **🔀 Parallel-Resistant**: Multi-threaded protection (parallelism: 4)
- **🎯 Side-Channel Safe**: Protection against timing attacks
- **🏆 Industry Standard**: Recommended by OWASP and security experts

### **Implementation Status**
- ✅ **Database Schema**: Updated to support 64-character hex salts
- ✅ **Hash Generator**: Built and deployed Argon2id tool
- ✅ **User Migration**: Both production users migrated to Argon2id
- ✅ **Authentication**: Live system using Argon2id verification
- ✅ **Documentation**: Comprehensive implementation guide
- ✅ **Migration Tools**: Automated scripts for existing databases

## Project Structure

```
auth-service/
├── auth-service-app/           # 🚀 C++23 Backend Application (PRODUCTION)
│   ├── src/                    # C++ source files
│   ├── include/                # C++ header files
│   ├── config/                 # Configuration files
│   │   ├── auth-service.conf   # Current configuration
│   │   └── auth-service-enhanced.conf  # Enhanced RBAC configuration
│   ├── database/               # Database schemas and scripts
│   │   ├── auth_schema.sql     # Argon2id-enabled database schema
│   │   ├── update_database.sh  # Database setup with Argon2id
│   │   ├── update_password.sql # Password update utilities
│   │   ├── migrate_to_argon2id.sh  # Migration script for existing DBs
│   │   └── README_ARGON2ID.md  # Argon2id documentation
│   ├── tools/                  # Development utilities
│   │   ├── hash-generator.cpp  # Argon2id password hash generator
│   │   └── build-hash-generator.sh  # Build script for hash tool
│   ├── tests/                  # Testing scripts
│   │   └── test_validation.sh  # OAuth validation tests
│   └── CMakeLists.txt          # Build configuration
│
├── auth-service-app-test/      # 🧪 Windows Testing Environment
│
├── auth-service-ui/            # Frontend User Interface
│   ├── html/                   # Current HTML-based UI (deployed)
│   │   ├── index.html          # Main login page
│   │   ├── admin.html          # Admin dashboard
│   │   └── dashboard.html      # User dashboard
│   ├── deployment/             # Deployment configurations
│   │   ├── nginx-ssl-config.conf      # HTTPS nginx configuration
│   │   ├── nginx-rate-limits.conf     # Rate limiting configuration
│   │   ├── build-react-simple.sh      # React build script
│   │   └── deploy-react-ui.sh         # React deployment script
│   ├── src/                    # React/TypeScript UI (future)
│   │   ├── components/         # React components
│   │   ├── services/           # API services
│   │   └── types/              # TypeScript types
│   ├── public/                 # React public assets
│   ├── package.json            # Node.js dependencies
│   └── tsconfig.json           # TypeScript configuration
│
├── docs/                       # Documentation
│   ├── certificate-management.md  # SSL certificate management
│   └── file-organization.md       # Project file organization
│
├── auth-service-deployment/    # Deployment automation (existing)
│   ├── deployment_scripts/     # Deployment scripts
│   └── deployment_files/       # Deployment configurations
│
└── README.md                   # This file
```

## Current Status (July 2025)

### ✅ **Production Ready Components**

#### **C++23 Backend Application**
- ✅ **OAuth 2.0 Core**: Complete token lifecycle (generate, validate, refresh, revoke)
- ✅ **Enhanced Database Schema**: Multi-tenant RBAC with organizations, projects, roles, permissions
- ✅ **Security**: **Argon2id password hashing** (memory-hard, GPU-resistant), JWT tokens, rate limiting
- ✅ **Password Security**: 64MB memory cost, 32-byte salts, cryptographically secure
- ✅ **Configuration**: Enhanced RBAC configuration with multi-tenant support
- ✅ **Development Tools**: Argon2id hash generator, comprehensive testing scripts
- ✅ **Database Migration**: Automated migration from SHA256 to Argon2id
- ✅ **Status**: **Fully deployed with Argon2id security**

#### **HTML-Based User Interface** (Currently Deployed)
- ✅ **Production Deployment**: Live at https://auth-dev.chcit.org/
- ✅ **Modern Design**: Dark blue Vision UI theme with glass morphism effects
- ✅ **Role-Based Access**: Automatic redirect based on user permissions
- ✅ **Admin Dashboard**: Comprehensive admin interface with token management
- ✅ **User Dashboard**: Sample application demonstrating OAuth flow
- ✅ **Security**: HTTPS-only, rate limiting, security headers
- ✅ **Status**: Fully functional and actively used

#### **Infrastructure & Deployment**
- ✅ **HTTPS Configuration**: Wildcard SSL certificates (*.chcit.org)
- ✅ **Nginx Configuration**: SSL termination, rate limiting, security headers
- ✅ **Certificate Management**: Automated sync from project-tracker.chcit.org
- ✅ **Deployment Automation**: Scripts for UI deployment and configuration
- ✅ **Status**: Production-ready infrastructure

### 🔄 **Recent Major Updates (July 2025)**

#### **🔐 Argon2id Security Implementation** (COMPLETED)
- ✅ **Password Hashing Upgrade**: Migrated from SHA256 to Argon2id
- ✅ **Database Schema Update**: Salt column expanded to support 64-character hex salts
- ✅ **Hash Generator Tool**: Built and deployed Argon2id hash generator
- ✅ **Migration Scripts**: Automated migration for existing databases
- ✅ **Security Parameters**: 64MB memory cost, time cost 3, parallelism 4
- ✅ **Production Deployment**: Both users now using Argon2id hashes
- ✅ **Documentation**: Comprehensive Argon2id implementation guide

#### **File Organization Overhaul**
- ✅ **Complete Reorganization**: All files moved to proper directories
- ✅ **Cleanup**: Removed 3 unused React implementations and legacy directories
- ✅ **Tool Organization**: Development utilities moved to `auth-service-app/tools/`
- ✅ **Test Organization**: Testing scripts moved to `auth-service-app/tests/`
- ✅ **Deployment Organization**: All deployment configs moved to `auth-service-ui/deployment/`

#### **Enhanced Database Schema** (Ready for Implementation)
- ✅ **Multi-tenant RBAC**: Organizations, projects, roles, permissions tables
- ✅ **Enhanced User Management**: Admin flags, organization membership
- ✅ **Project-specific Tokens**: Token tracking per project
- ✅ **Migration Scripts**: Automated database update procedures
- ✅ **Argon2id Support**: Full schema compatibility with secure password hashing

#### **UI Enhancements** (Deployed)
- ✅ **Admin Dashboard**: Complete admin interface with token management
- ✅ **Role-based Routing**: Automatic redirect based on user permissions
- ✅ **Modern Design**: Dark blue theme with professional styling
- ✅ **Security Features**: Rate limiting, HTTPS enforcement, security headers

### 👥 **User Accounts** (Argon2id Secured)
- **testuser** / `testpass123` - Standard user with viewer role ✅ **Argon2id**
- **btaylor-admin** / `AdminPass123!` - System administrator with full access ✅ **Argon2id**

## Development

### Backend (C++23)
```bash
cd auth-service-app
mkdir build && cd build
cmake ..
make
```

### Frontend (React/TypeScript - Future)
```bash
cd auth-service-ui
npm install
npm start
```

### Database Setup (Argon2id)
```bash
# For new installations
cd auth-service-app/database
chmod +x update_database.sh
sudo ./update_database.sh

# For migrating existing databases to Argon2id
chmod +x migrate_to_argon2id.sh
sudo ./migrate_to_argon2id.sh

# Generate Argon2id password hashes
cd auth-service-app/tools
./build-hash-generator.sh
./hash-generator "your_password"
```

## Deployment

The HTML-based UI is currently deployed to `/opt/auth-service-ui/html/` on the server.

### Access URLs
- **Main Login**: https://auth-dev.chcit.org/
- **Admin Dashboard**: https://auth-dev.chcit.org/admin.html (admin users only)
- **User Dashboard**: https://auth-dev.chcit.org/dashboard.html

## Configuration

### Database Configuration
- **Database**: `auth_service_db`
- **User**: `auth_service_user`
- **Host**: `127.0.0.1:5432`

### OAuth 2.0 Settings
- **Access Token Expiry**: 3600 seconds (1 hour)
- **Refresh Token Expiry**: 6048000 seconds (70 days)
- **JWT Algorithm**: HS256

## Security Features

### 🔐 **Password Security (Argon2id)**
- **Memory-Hard Hashing**: 64MB memory cost resistant to GPU/ASIC attacks
- **Time-Hard Parameters**: Configurable time cost for additional security
- **Cryptographic Salts**: 32-byte random salts for each password
- **Side-Channel Resistant**: Protection against timing attacks
- **Future-Proof**: Recommended by security experts worldwide

### 🛡️ **Authentication & Authorization**
- **JWT Tokens**: Secure token-based authentication with expiration
- **Role-Based Access Control**: Multi-tenant RBAC with organizations and projects
- **Session Management**: Secure session handling and token lifecycle
- **Permission System**: Granular permissions with inheritance

### 🚨 **Infrastructure Security**
- **Rate Limiting**: Protection against brute force attacks
- **HTTPS Only**: All communications encrypted with TLS
- **Security Headers**: HSTS, CSP, and other security headers
- **Certificate Management**: Automated wildcard SSL certificate sync

## API Endpoints

### OAuth 2.0
- `POST /oauth/token` - Generate access token
- `POST /oauth/refresh` - Refresh access token
- `POST /oauth/validate` - Validate token
- `POST /oauth/revoke` - Revoke token

### System
- `GET /health` - Service health check

## Future Enhancements

### 🎯 **Immediate Next Steps**
- **Enhanced RBAC Implementation**: Deploy multi-tenant role system
- **Admin Interface Expansion**: User management and role assignment
- **Audit Logging**: Comprehensive activity tracking and monitoring

### 🚀 **Medium-Term Goals**
- **React/TypeScript Migration**: Modern frontend framework
- **User Registration**: Self-service user registration with email verification
- **API Rate Limiting**: Enhanced security controls and throttling
- **WebSocket Support**: Real-time communication and notifications

### 🔮 **Long-Term Vision**
- **Multi-Factor Authentication**: TOTP and hardware key support
- **OAuth 2.1 Compliance**: Latest OAuth specification support
- **Advanced Analytics**: User behavior and security analytics
- **Mobile SDK**: Native mobile application support

## 📚 **Documentation**

### **Project Documentation**
- **[Current Status Report](docs/current-status.md)** - Comprehensive status of all components
- **[Deployment Status](docs/deployment-status.md)** - Live deployment status and verification
- **[Development Roadmap](docs/ROADMAP.md)** - Complete development roadmap and phases
- **[Task Tracking](docs/TASK-TRACKING.md)** - Active task tracking and sprint management
- **[Change Log](docs/CHANGELOG.md)** - Complete history of changes and updates
- **[File Organization](docs/file-organization.md)** - Project structure and organization
- **[Certificate Management](docs/certificate-management.md)** - SSL certificate procedures

### **Development Documentation**
- **[Implementation Plan](docs/IMPLEMENTATION-PLAN.md)** - Detailed C++23 RBAC implementation strategy
- **[Detailed Task List](docs/DETAILED-TASK-LIST.md)** - 47 tasks with acceptance criteria
- **[Compilation Test Report](docs/COMPILATION-TEST-REPORT.md)** - ✅ **Complete test results and validation**
- **[Testing Environment Guide](auth-service-app-test/README-TEST.md)** - 🧪 **Windows testing setup**
- **[Enhanced Database Schema](auth-service-app/database/enhanced_auth_schema.sql)** - Multi-tenant RBAC schema
- **[Configuration Guide](auth-service-app/config/auth-service-enhanced.conf)** - Enhanced configuration
- **[Testing Guide](auth-service-app/tests/test_validation.sh)** - OAuth endpoint testing
- **[Deployment Guide](auth-service-ui/deployment/)** - UI deployment procedures

### **Security Documentation**
- **[Argon2id Implementation Guide](auth-service-app/database/README_ARGON2ID.md)** - 🔐 **Complete Argon2id documentation**
- **[Password Migration Guide](auth-service-app/database/migrate_to_argon2id.sh)** - Migration from SHA256 to Argon2id
- **[Hash Generator Tool](auth-service-app/tools/hash-generator.cpp)** - Secure password hash generation
- **[Certificate Management](docs/certificate-management.md)** - SSL certificate procedures

## 🎯 **Quick Start**

### **Access the Application**
- **Main Login**: https://auth-dev.chcit.org/
- **Admin Dashboard**: https://auth-dev.chcit.org/admin.html (admin users only)
- **User Dashboard**: https://auth-dev.chcit.org/dashboard.html

### **Test Accounts** (Argon2id Secured)
- **Standard User**: `testuser` / `testpass123` ✅ **Argon2id**
- **Administrator**: `btaylor-admin` / `AdminPass123!` ✅ **Argon2id**

### **Development**
```bash
# Build Argon2id password hash generator
cd auth-service-app/tools
./build-hash-generator.sh

# Generate secure password hash
./hash-generator "your_password"

# Run OAuth tests
cd auth-service-app/tests
./test_validation.sh

# Deploy UI changes
cd auth-service-ui/deployment
./deploy-react-ui.sh

# Migrate existing database to Argon2id
cd auth-service-app/database
./migrate_to_argon2id.sh
```

## License

Private project for CHC IT infrastructure.
