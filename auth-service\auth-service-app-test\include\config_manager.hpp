﻿#pragma once
#include <string>
#ifdef _WIN32
// <PERSON><PERSON> n<PERSON>hmann/json for Windows testing
namespace nlohmann {
    class json {
    public:
        json() = default;
        json(const std::string&) {}
        std::string value(const std::string& key, const std::string& default_val) const { return default_val; }
        int value(const std::string& key, int default_val) const { return default_val; }
        bool value(const std::string& key, bool default_val) const { return default_val; }
    };
}
#else
#include <nlohmann/json.hpp>
#endif

class ConfigManager {
public:
    explicit ConfigManager(const std::string& config_file);

    // Database configuration
    std::string get_database_host() const;
    int get_database_port() const;
    std::string get_database_name() const;
    std::string get_database_user() const;
    std::string get_database_password() const;

    // Server configuration
    int get_server_port() const;
    std::string get_log_level() const;

    // OAuth 2.0 JWT configuration
    std::string get_jwt_secret() const;
    int get_jwt_access_token_expiry() const;  // seconds
    int get_jwt_refresh_token_expiry() const; // seconds
    std::string get_jwt_algorithm() const;

    // OAuth 2.0 Argon2 configuration
    int get_argon2_memory_cost() const;       // KB
    int get_argon2_time_cost() const;         // iterations
    int get_argon2_parallelism() const;       // threads
    int get_argon2_salt_length() const;       // bytes

    // OAuth 2.0 Session configuration
    int get_session_timeout() const;          // seconds
    int get_session_cleanup_interval() const; // seconds
    int get_max_sessions_per_user() const;
private:
    nlohmann::json config_;
    void load_config(const std::string& config_file);
};
