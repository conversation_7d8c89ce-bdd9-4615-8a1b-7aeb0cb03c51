#pragma once
#include <string>
#include <vector>
#include <memory>
#include <optional>
#include <chrono>
#include <unordered_map>

class DatabaseManager;
class RBACManager;
class JWTManager;

/**
 * @brief Enhanced Token Manager with Project Scoping
 *
 * Provides enhanced token management with:
 * - Project-specific token scoping
 * - Role-based token permissions
 * - Token analytics and reporting
 * - Enhanced token lifecycle management
 * - Integration with RBAC system
 *
 * Enhanced OAuth 2.0 Implementation with Multi-tenant RBAC
 */
class EnhancedTokenManager {
public:
    /**
     * @brief Enhanced token data structure
     */
    struct EnhancedToken {
        std::string token_id;        // UUID
        std::string user_id;         // User UUID
        std::string project_id;      // Project UUID (scope)
        std::string org_id;          // Organization UUID
        std::string token_hash;      // Token hash for storage
        std::string token_type;      // "access" or "refresh"
        std::string jti;             // JWT ID
        std::vector<std::string> scopes;     // Token scopes
        std::vector<std::string> permissions; // Effective permissions
        std::chrono::system_clock::time_point created_at;
        std::chrono::system_clock::time_point expires_at;
        std::chrono::system_clock::time_point last_used_at;
        bool is_active;
        std::string client_info;     // Client information (IP, User-Agent, etc.)
    };

    /**
     * @brief Token generation request
     */
    struct TokenRequest {
        std::string user_id;         // User UUID
        std::string project_id;      // Project UUID (required for scoping)
        std::vector<std::string> requested_scopes; // Requested scopes
        std::string client_ip;       // Client IP address
        std::string user_agent;      // Client User-Agent
        std::chrono::seconds access_token_lifetime;  // Custom lifetime (optional)
        std::chrono::seconds refresh_token_lifetime; // Custom lifetime (optional)
    };

    /**
     * @brief Token validation result
     */
    struct TokenValidationResult {
        bool is_valid;
        std::string user_id;
        std::string project_id;
        std::string org_id;
        std::vector<std::string> scopes;
        std::vector<std::string> permissions;
        std::chrono::system_clock::time_point expires_at;
        std::string error_message;
    };

    /**
     * @brief Token analytics data
     */
    struct TokenAnalytics {
        std::string project_id;
        std::string org_id;
        int total_tokens;
        int active_tokens;
        int expired_tokens;
        int revoked_tokens;
        std::chrono::system_clock::time_point last_token_created;
        std::chrono::system_clock::time_point last_token_used;
        std::unordered_map<std::string, int> tokens_by_user;
        std::unordered_map<std::string, int> tokens_by_scope;
    };

public:
    /**
     * @brief Constructor
     * @param db_manager Database manager instance
     * @param rbac_manager RBAC manager instance
     * @param jwt_manager JWT manager instance
     */
    EnhancedTokenManager(DatabaseManager* db_manager, 
                        RBACManager* rbac_manager,
                        JWTManager* jwt_manager);

    /**
     * @brief Destructor
     */
    ~EnhancedTokenManager();

    // ========================================================================
    // Enhanced Token Generation
    // ========================================================================

    /**
     * @brief Generate project-scoped access and refresh tokens
     * @param request Token generation request
     * @return Pair of (access_token, refresh_token) if successful
     */
    std::pair<std::string, std::string> generateProjectTokens(const TokenRequest& request);

    /**
     * @brief Generate access token only
     * @param request Token generation request
     * @return Access token if successful, empty string if failed
     */
    std::string generateAccessToken(const TokenRequest& request);

    /**
     * @brief Refresh access token using refresh token
     * @param refresh_token Refresh token
     * @param project_id Project UUID (must match original token)
     * @param client_ip Client IP address
     * @return New access token if successful, empty string if failed
     */
    std::string refreshAccessToken(const std::string& refresh_token,
                                 const std::string& project_id,
                                 const std::string& client_ip = "");

    // ========================================================================
    // Enhanced Token Validation
    // ========================================================================

    /**
     * @brief Validate token with project context
     * @param token Access token
     * @param project_id Expected project UUID
     * @param required_permission Required permission (optional)
     * @return Token validation result
     */
    TokenValidationResult validateProjectToken(const std::string& token,
                                              const std::string& project_id,
                                              const std::string& required_permission = "");

    /**
     * @brief Validate token and check specific permission
     * @param token Access token
     * @param permission_name Required permission name
     * @return true if token is valid and has permission
     */
    bool validateTokenPermission(const std::string& token,
                               const std::string& permission_name);

    /**
     * @brief Get token information without validation
     * @param token Access token
     * @return Token information if token exists
     */
    std::optional<EnhancedToken> getTokenInfo(const std::string& token);

    // ========================================================================
    // Token Lifecycle Management
    // ========================================================================

    /**
     * @brief Revoke specific token
     * @param token Token to revoke
     * @param reason Revocation reason
     * @return true if successful
     */
    bool revokeToken(const std::string& token, const std::string& reason = "");

    /**
     * @brief Revoke all tokens for a user in a project
     * @param user_id User UUID
     * @param project_id Project UUID
     * @param reason Revocation reason
     * @return Number of tokens revoked
     */
    int revokeUserProjectTokens(const std::string& user_id,
                               const std::string& project_id,
                               const std::string& reason = "");

    /**
     * @brief Revoke all tokens for a user across all projects
     * @param user_id User UUID
     * @param reason Revocation reason
     * @return Number of tokens revoked
     */
    int revokeAllUserTokens(const std::string& user_id,
                           const std::string& reason = "");

    /**
     * @brief Clean up expired tokens
     * @param older_than_days Remove tokens expired more than X days ago
     * @return Number of tokens cleaned up
     */
    int cleanupExpiredTokens(int older_than_days = 7);

    // ========================================================================
    // Token Analytics and Reporting
    // ========================================================================

    /**
     * @brief Get token analytics for a project
     * @param project_id Project UUID
     * @param days_back Number of days to analyze
     * @return Token analytics data
     */
    TokenAnalytics getProjectTokenAnalytics(const std::string& project_id,
                                           int days_back = 30);

    /**
     * @brief Get token analytics for an organization
     * @param org_id Organization UUID
     * @param days_back Number of days to analyze
     * @return Token analytics data
     */
    TokenAnalytics getOrganizationTokenAnalytics(const std::string& org_id,
                                                int days_back = 30);

    /**
     * @brief Get active tokens for a user
     * @param user_id User UUID
     * @param project_id Project UUID (optional, all projects if empty)
     * @return Vector of active tokens
     */
    std::vector<EnhancedToken> getUserActiveTokens(const std::string& user_id,
                                                  const std::string& project_id = "");

    /**
     * @brief Get token usage statistics
     * @param project_id Project UUID
     * @param start_date Start date for statistics
     * @param end_date End date for statistics
     * @return Usage statistics as key-value pairs
     */
    std::unordered_map<std::string, int> getTokenUsageStats(
        const std::string& project_id,
        const std::chrono::system_clock::time_point& start_date,
        const std::chrono::system_clock::time_point& end_date);

    // ========================================================================
    // Token Scoping and Permissions
    // ========================================================================

    /**
     * @brief Get available scopes for a user in a project
     * @param user_id User UUID
     * @param project_id Project UUID
     * @return Vector of available scope names
     */
    std::vector<std::string> getAvailableScopes(const std::string& user_id,
                                               const std::string& project_id);

    /**
     * @brief Get effective permissions for a token
     * @param token Access token
     * @return Vector of permission names
     */
    std::vector<std::string> getTokenPermissions(const std::string& token);

    /**
     * @brief Update token last used timestamp
     * @param token Access token
     * @param client_ip Client IP address
     * @return true if successful
     */
    bool updateTokenUsage(const std::string& token, const std::string& client_ip = "");

    // ========================================================================
    // Token Security Features
    // ========================================================================

    /**
     * @brief Check for suspicious token activity
     * @param user_id User UUID
     * @param project_id Project UUID
     * @param client_ip Client IP address
     * @return true if activity seems suspicious
     */
    bool detectSuspiciousActivity(const std::string& user_id,
                                const std::string& project_id,
                                const std::string& client_ip);

    /**
     * @brief Get token security events
     * @param project_id Project UUID
     * @param days_back Number of days to look back
     * @return Vector of security event descriptions
     */
    std::vector<std::string> getSecurityEvents(const std::string& project_id,
                                              int days_back = 7);

    /**
     * @brief Validate token rate limiting
     * @param user_id User UUID
     * @param project_id Project UUID
     * @param action Action being performed
     * @return true if within rate limits
     */
    bool validateRateLimit(const std::string& user_id,
                         const std::string& project_id,
                         const std::string& action);

private:
    DatabaseManager* db_manager_;
    RBACManager* rbac_manager_;
    JWTManager* jwt_manager_;

    // Helper methods
    std::string generateTokenHash(const std::string& token);
    std::vector<std::string> calculateEffectivePermissions(const std::string& user_id,
                                                          const std::string& project_id);
    bool isValidScope(const std::string& scope);
    void logTokenEvent(const std::string& event_type, const std::string& token_id,
                      const std::string& details = "");
    std::string createJWTPayload(const EnhancedToken& token_data);
};
