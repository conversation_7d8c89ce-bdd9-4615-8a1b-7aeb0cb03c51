#pragma once

#include <string>
#include <memory>
#include <chrono>
#include <unordered_map>
#include <vector>

// Forward declarations
class ConfigManager;
class DatabaseManager;

/**
 * @brief JWT Token Management for OAuth 2.0 Authentication
 * 
 * Implements JWT (JSON Web Token) functionality including:
 * - Token generation (access and refresh tokens)
 * - Token validation and verification
 * - Token refresh mechanisms
 * - Secure cryptographic operations using OpenSSL
 * 
 * Step 4 of OAuth 2.0 Implementation
 */
class JWTManager {
public:
    /**
     * @brief JWT Token Types
     */
    enum class TokenType {
        ACCESS,
        REFRESH
    };

    /**
     * @brief JWT Token Structure
     */
    struct Token {
        std::string token;
        std::string token_hash;
        TokenType type;
        std::chrono::system_clock::time_point expires_at;
        std::string user_id;  // UUID
        std::vector<std::string> scopes;
        
        bool is_expired() const {
            return std::chrono::system_clock::now() >= expires_at;
        }
    };

    /**
     * @brief JWT Token Pair (Access + Refresh)
     */
    struct TokenPair {
        Token access_token;
        Token refresh_token;
        bool success;
        std::string error_message;
    };

    /**
     * @brief JWT Validation Result
     */
    struct ValidationResult {
        bool valid;
        std::string user_id;  // UUID
        std::vector<std::string> scopes;
        std::string error_message;
        std::chrono::system_clock::time_point expires_at;
    };

public:
    /**
     * @brief Constructor
     * @param config Configuration manager for JWT settings
     * @param database Database manager for token storage
     */
    explicit JWTManager(std::shared_ptr<ConfigManager> config, 
                       std::shared_ptr<DatabaseManager> database);

    /**
     * @brief Destructor
     */
    ~JWTManager();

    /**
     * @brief Initialize JWT Manager
     * @return true if initialization successful
     */
    bool initialize();

    /**
     * @brief Generate JWT token pair (access + refresh)
     * @param user_id User UUID for token
     * @param scopes Token scopes/permissions
     * @return TokenPair with access and refresh tokens
     */
    TokenPair generateTokenPair(const std::string& user_id, const std::vector<std::string>& scopes = {"read"});

    /**
     * @brief Generate single JWT token
     * @param user_id User UUID for token
     * @param type Token type (ACCESS or REFRESH)
     * @param scopes Token scopes/permissions
     * @return Generated token
     */
    Token generateToken(const std::string& user_id, TokenType type, const std::vector<std::string>& scopes = {"read"});

    /**
     * @brief Validate JWT token
     * @param token_string JWT token string
     * @return Validation result with user info
     */
    ValidationResult validateToken(const std::string& token_string);

    /**
     * @brief Refresh access token using refresh token
     * @param refresh_token Refresh token string
     * @return New token pair if successful
     */
    TokenPair refreshToken(const std::string& refresh_token);

    /**
     * @brief Revoke JWT token
     * @param token_string Token to revoke
     * @return true if revocation successful
     */
    bool revokeToken(const std::string& token_string);

    /**
     * @brief Check if token is revoked
     * @param token_hash Token hash to check
     * @return true if token is revoked
     */
    bool isTokenRevoked(const std::string& token_hash);

    /**
     * @brief Clean up expired tokens from database
     * @return Number of tokens cleaned up
     */
    int cleanupExpiredTokens();

private:
    /**
     * @brief Create JWT header
     * @return Base64 encoded JWT header
     */
    std::string createHeader();

    /**
     * @brief Create JWT payload
     * @param user_id User UUID
     * @param type Token type
     * @param scopes Token scopes
     * @param expires_at Expiration time
     * @return Base64 encoded JWT payload
     */
    std::string createPayload(const std::string& user_id, TokenType type,
                             const std::vector<std::string>& scopes,
                             std::chrono::system_clock::time_point expires_at);

    /**
     * @brief Sign JWT token using HMAC-SHA256
     * @param header Base64 encoded header
     * @param payload Base64 encoded payload
     * @return Base64 encoded signature
     */
    std::string signToken(const std::string& header, const std::string& payload);

    /**
     * @brief Verify JWT token signature
     * @param header Base64 encoded header
     * @param payload Base64 encoded payload
     * @param signature Base64 encoded signature
     * @return true if signature is valid
     */
    bool verifySignature(const std::string& header, const std::string& payload, 
                        const std::string& signature);

    /**
     * @brief Parse JWT token into components
     * @param token_string JWT token string
     * @param header Output header
     * @param payload Output payload
     * @param signature Output signature
     * @return true if parsing successful
     */
    bool parseToken(const std::string& token_string, std::string& header, 
                   std::string& payload, std::string& signature);

    /**
     * @brief Decode JWT payload
     * @param payload Base64 encoded payload
     * @param user_id Output user UUID
     * @param type Output token type
     * @param scopes Output scopes
     * @param expires_at Output expiration time
     * @return true if decoding successful
     */
    bool decodePayload(const std::string& payload, std::string& user_id, TokenType& type,
                      std::vector<std::string>& scopes,
                      std::chrono::system_clock::time_point& expires_at);

    /**
     * @brief Calculate SHA256 hash of token
     * @param token Token string
     * @return SHA256 hash
     */
    std::string calculateTokenHash(const std::string& token);

    /**
     * @brief Store token in database
     * @param token Token to store
     * @return true if storage successful
     */
    bool storeToken(const Token& token);

    /**
     * @brief Base64 encode string
     * @param input Input string
     * @return Base64 encoded string
     */
    std::string base64Encode(const std::string& input);

    /**
     * @brief Base64 decode string
     * @param input Base64 encoded string
     * @return Decoded string
     */
    std::string base64Decode(const std::string& input);

    /**
     * @brief Get current timestamp as seconds since epoch
     * @return Current timestamp
     */
    int64_t getCurrentTimestamp();

    /**
     * @brief Convert timestamp to time_point
     * @param timestamp Seconds since epoch
     * @return time_point
     */
    std::chrono::system_clock::time_point timestampToTimePoint(int64_t timestamp);

private:
    std::shared_ptr<ConfigManager> config_manager_;
    std::shared_ptr<DatabaseManager> database_manager_;
    
    // JWT Configuration
    std::string secret_key_;
    std::string algorithm_;
    std::string issuer_;
    int access_token_expiry_;  // seconds
    int refresh_token_expiry_; // seconds
    
    // Initialization state
    bool initialized_;
};
