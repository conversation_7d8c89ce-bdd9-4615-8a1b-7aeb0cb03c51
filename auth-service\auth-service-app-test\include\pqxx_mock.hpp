#pragma once
// Mock PQXX for Windows compilation testing
#include <string>
#include <vector>

namespace pqxx {
    // Mock connection class
    class connection {
    public:
        connection(const std::string& conn_str) {}
        bool is_open() const { return true; }
        void close() {}
        std::string dbname() const { return "mock_db"; }
    };

    // Mock work class
    class work {
    public:
        work(connection& conn) {}
        void commit() {}
        std::string esc(const std::string& str) { return str; }
        
        // Mock result for simple queries
        struct mock_result {
            struct mock_row {
                struct mock_field {
                    template<typename T>
                    T as() const { 
                        if constexpr (std::is_same_v<T, std::string>) {
                            return "mock_value";
                        } else if constexpr (std::is_same_v<T, int>) {
                            return 1;
                        } else if constexpr (std::is_same_v<T, bool>) {
                            return true;
                        }
                        return T{};
                    }
                    
                    template<typename T>
                    T as(const T& default_val) const { return default_val; }

                    const char* c_str() const { return "mock_value"; }
                };

                mock_field operator[](const std::string& field) const { return mock_field{}; }
                mock_field operator[](int index) const { return mock_field{}; }
                bool empty() const { return false; }
            };
            
            mock_row operator[](int index) const { return mock_row{}; }
            bool empty() const { return false; }
            size_t size() const { return 1; }
            int affected_rows() const { return 1; }
            
            // Iterator support
            struct iterator {
                mock_row operator*() const { return mock_row{}; }
                iterator& operator++() { return *this; }
                bool operator!=(const iterator& other) const { return false; }
            };
            
            iterator begin() const { return iterator{}; }
            iterator end() const { return iterator{}; }
        };

        mock_result exec(const std::string& query) { return mock_result{}; }
        mock_result exec_params(const std::string& query, const std::string& param1) { return mock_result{}; }
        mock_result exec_params(const std::string& query, const std::string& param1, const std::string& param2) { return mock_result{}; }
        mock_result exec_params(const std::string& query, const std::string& param1, const std::string& param2, const std::string& param3) { return mock_result{}; }
        mock_result exec_params(const std::string& query, const std::string& param1, const std::string& param2, const std::string& param3, const std::string& param4) { return mock_result{}; }
        mock_result exec_params(const std::string& query, const std::string& param1, const std::string& param2, const std::string& param3, const std::string& param4, const std::string& param5) { return mock_result{}; }
    };

    using result = work::mock_result;
    using row = work::mock_result::mock_row;
}
