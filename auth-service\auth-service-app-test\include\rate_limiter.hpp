#pragma once
#include <string>
#include <unordered_map>
#include <chrono>
#include <mutex>
#include <deque>

/**
 * @brief Rate Limiter for OAuth 2.0 API endpoints
 * 
 * Implements sliding window rate limiting with different limits per endpoint
 */
class RateLimiter {
public:
    struct RateLimitConfig {
        int requests_per_minute = 60;
        int burst_limit = 10;
        std::chrono::seconds window_size = std::chrono::seconds(60);
    };

    struct RateLimitResult {
        bool allowed;
        int remaining_requests;
        std::chrono::system_clock::time_point reset_time;
        std::string message;
    };

    RateLimiter();
    ~RateLimiter() = default;

    /**
     * @brief Check if request is allowed for client and endpoint
     * @param client_ip Client IP address
     * @param endpoint API endpoint path
     * @return Rate limit result
     */
    RateLimitResult checkLimit(const std::string& client_ip, const std::string& endpoint);

    /**
     * @brief Configure rate limits for specific endpoint
     * @param endpoint API endpoint path
     * @param config Rate limit configuration
     */
    void configureEndpoint(const std::string& endpoint, const RateLimitConfig& config);

    /**
     * @brief Add client to blacklist
     * @param client_ip Client IP address
     * @param duration Blacklist duration (0 = permanent)
     */
    void blacklistClient(const std::string& client_ip, std::chrono::seconds duration = std::chrono::seconds(0));

    /**
     * @brief Remove client from blacklist
     * @param client_ip Client IP address
     */
    void removeFromBlacklist(const std::string& client_ip);

    /**
     * @brief Get rate limiting statistics
     */
    struct Stats {
        int total_requests = 0;
        int allowed_requests = 0;
        int blocked_requests = 0;
        int blacklisted_requests = 0;
    };
    Stats getStats() const;

private:
    struct ClientTracker {
        std::deque<std::chrono::system_clock::time_point> request_times;
        std::chrono::system_clock::time_point last_request;
        int burst_count = 0;
        std::chrono::system_clock::time_point burst_start;
    };

    struct BlacklistEntry {
        std::chrono::system_clock::time_point expires_at;
        std::string reason;
    };

    mutable std::mutex mutex_;
    std::unordered_map<std::string, RateLimitConfig> endpoint_configs_;
    std::unordered_map<std::string, std::unordered_map<std::string, ClientTracker>> client_trackers_; // endpoint -> client_ip -> tracker
    std::unordered_map<std::string, BlacklistEntry> blacklist_;
    mutable Stats stats_;

    /**
     * @brief Get rate limit configuration for endpoint
     */
    const RateLimitConfig& getEndpointConfig(const std::string& endpoint) const;

    /**
     * @brief Check if client is blacklisted
     */
    bool isBlacklisted(const std::string& client_ip) const;

    /**
     * @brief Clean up expired entries
     */
    void cleanup();

    /**
     * @brief Check sliding window rate limit
     */
    bool checkSlidingWindow(ClientTracker& tracker, const RateLimitConfig& config, 
                           const std::chrono::system_clock::time_point& now);

    /**
     * @brief Check burst limit
     */
    bool checkBurstLimit(ClientTracker& tracker, const RateLimitConfig& config,
                        const std::chrono::system_clock::time_point& now);
};
