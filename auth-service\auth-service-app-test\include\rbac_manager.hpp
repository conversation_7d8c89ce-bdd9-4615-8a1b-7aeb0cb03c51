#pragma once
#include <string>
#include <vector>
#include <memory>
#include <optional>
#include <chrono>
#include <unordered_map>

class DatabaseManager;

/**
 * @brief Role-Based Access Control Manager
 *
 * Provides comprehensive RBAC operations for:
 * - Organization management (multi-tenant support)
 * - Project management within organizations
 * - Role and permission management
 * - User-role assignments
 * - Permission validation
 *
 * Enhanced OAuth 2.0 Implementation with Multi-tenant RBAC
 */
class RBACManager {
public:
    /**
     * @brief Organization data structure
     */
    struct Organization {
        std::string org_id;          // UUID
        std::string org_name;        // Organization name
        std::string org_domain;      // Organization domain (optional)
        std::chrono::system_clock::time_point created_at;
        std::chrono::system_clock::time_point updated_at;
        bool is_active;
    };

    /**
     * @brief Project data structure
     */
    struct Project {
        std::string project_id;      // UUID
        std::string org_id;          // Organization UUID
        std::string project_name;    // Project name
        std::string project_description; // Project description
        std::chrono::system_clock::time_point created_at;
        std::chrono::system_clock::time_point updated_at;
        bool is_active;
    };

    /**
     * @brief Role data structure
     */
    struct Role {
        std::string role_id;         // UUID
        std::string role_name;       // Role name
        std::string role_description; // Role description
        bool is_system_role;         // System-wide role flag
        std::chrono::system_clock::time_point created_at;
    };

    /**
     * @brief Permission data structure
     */
    struct Permission {
        std::string permission_id;   // UUID
        std::string permission_name; // Permission name
        std::string permission_description; // Permission description
        std::chrono::system_clock::time_point created_at;
    };

    /**
     * @brief User-Organization relationship
     */
    struct UserOrganization {
        std::string user_id;         // User UUID
        std::string org_id;          // Organization UUID
        std::chrono::system_clock::time_point joined_at;
        bool is_active;
    };

    /**
     * @brief User-Project-Role assignment
     */
    struct UserProjectRole {
        std::string user_id;         // User UUID
        std::string project_id;      // Project UUID
        std::string role_id;         // Role UUID
        std::chrono::system_clock::time_point assigned_at;
        std::chrono::system_clock::time_point expires_at; // Optional expiration
        bool is_active;
    };

    /**
     * @brief Permission validation result
     */
    struct PermissionResult {
        bool has_permission;
        std::string user_id;
        std::string project_id;
        std::string permission_name;
        std::vector<std::string> roles; // Roles that granted the permission
    };

public:
    /**
     * @brief Constructor
     * @param db_manager Database manager instance
     */
    explicit RBACManager(DatabaseManager* db_manager);

    /**
     * @brief Destructor
     */
    ~RBACManager();

    // ========================================================================
    // Organization Management
    // ========================================================================

    /**
     * @brief Create a new organization
     * @param org_name Organization name
     * @param org_domain Organization domain (optional)
     * @return Organization UUID if successful, empty string if failed
     */
    std::string createOrganization(const std::string& org_name, 
                                 const std::string& org_domain = "");

    /**
     * @brief Get organization by ID
     * @param org_id Organization UUID
     * @return Organization data if found
     */
    std::optional<Organization> getOrganization(const std::string& org_id);

    /**
     * @brief Get organization by name
     * @param org_name Organization name
     * @return Organization data if found
     */
    std::optional<Organization> getOrganizationByName(const std::string& org_name);

    /**
     * @brief Update organization
     * @param org_id Organization UUID
     * @param org_name New organization name
     * @param org_domain New organization domain
     * @return true if successful
     */
    bool updateOrganization(const std::string& org_id, 
                          const std::string& org_name,
                          const std::string& org_domain = "");

    /**
     * @brief Delete organization (soft delete)
     * @param org_id Organization UUID
     * @return true if successful
     */
    bool deleteOrganization(const std::string& org_id);

    /**
     * @brief List all organizations
     * @param include_inactive Include inactive organizations
     * @return Vector of organizations
     */
    std::vector<Organization> listOrganizations(bool include_inactive = false);

    // ========================================================================
    // Project Management
    // ========================================================================

    /**
     * @brief Create a new project within an organization
     * @param org_id Organization UUID
     * @param project_name Project name
     * @param project_description Project description
     * @return Project UUID if successful, empty string if failed
     */
    std::string createProject(const std::string& org_id,
                            const std::string& project_name,
                            const std::string& project_description = "");

    /**
     * @brief Get project by ID
     * @param project_id Project UUID
     * @return Project data if found
     */
    std::optional<Project> getProject(const std::string& project_id);

    /**
     * @brief Update project
     * @param project_id Project UUID
     * @param project_name New project name
     * @param project_description New project description
     * @return true if successful
     */
    bool updateProject(const std::string& project_id,
                     const std::string& project_name,
                     const std::string& project_description = "");

    /**
     * @brief Delete project (soft delete)
     * @param project_id Project UUID
     * @return true if successful
     */
    bool deleteProject(const std::string& project_id);

    /**
     * @brief List projects in an organization
     * @param org_id Organization UUID
     * @param include_inactive Include inactive projects
     * @return Vector of projects
     */
    std::vector<Project> listProjectsByOrganization(const std::string& org_id,
                                                   bool include_inactive = false);

    // ========================================================================
    // Role and Permission Management
    // ========================================================================

    /**
     * @brief Create a new role
     * @param role_name Role name
     * @param role_description Role description
     * @param is_system_role Whether this is a system-wide role
     * @return Role UUID if successful, empty string if failed
     */
    std::string createRole(const std::string& role_name,
                         const std::string& role_description = "",
                         bool is_system_role = false);

    /**
     * @brief Get role by ID
     * @param role_id Role UUID
     * @return Role data if found
     */
    std::optional<Role> getRole(const std::string& role_id);

    /**
     * @brief Get role by name
     * @param role_name Role name
     * @return Role data if found
     */
    std::optional<Role> getRoleByName(const std::string& role_name);

    /**
     * @brief List all roles
     * @param system_roles_only Only return system roles
     * @return Vector of roles
     */
    std::vector<Role> listRoles(bool system_roles_only = false);

    /**
     * @brief Create a new permission
     * @param permission_name Permission name
     * @param permission_description Permission description
     * @return Permission UUID if successful, empty string if failed
     */
    std::string createPermission(const std::string& permission_name,
                               const std::string& permission_description = "");

    /**
     * @brief Get permission by ID
     * @param permission_id Permission UUID
     * @return Permission data if found
     */
    std::optional<Permission> getPermission(const std::string& permission_id);

    /**
     * @brief Get permission by name
     * @param permission_name Permission name
     * @return Permission data if found
     */
    std::optional<Permission> getPermissionByName(const std::string& permission_name);

    /**
     * @brief List all permissions
     * @return Vector of permissions
     */
    std::vector<Permission> listPermissions();

    /**
     * @brief Assign permission to role
     * @param role_id Role UUID
     * @param permission_id Permission UUID
     * @return true if successful
     */
    bool assignPermissionToRole(const std::string& role_id,
                              const std::string& permission_id);

    /**
     * @brief Remove permission from role
     * @param role_id Role UUID
     * @param permission_id Permission UUID
     * @return true if successful
     */
    bool removePermissionFromRole(const std::string& role_id,
                                const std::string& permission_id);

    /**
     * @brief Get permissions for a role
     * @param role_id Role UUID
     * @return Vector of permissions
     */
    std::vector<Permission> getRolePermissions(const std::string& role_id);

    // ========================================================================
    // User-Role Assignment
    // ========================================================================

    /**
     * @brief Add user to organization
     * @param user_id User UUID
     * @param org_id Organization UUID
     * @return true if successful
     */
    bool addUserToOrganization(const std::string& user_id,
                             const std::string& org_id);

    /**
     * @brief Remove user from organization
     * @param user_id User UUID
     * @param org_id Organization UUID
     * @return true if successful
     */
    bool removeUserFromOrganization(const std::string& user_id,
                                  const std::string& org_id);

    /**
     * @brief Assign role to user for a project
     * @param user_id User UUID
     * @param project_id Project UUID
     * @param role_id Role UUID
     * @param expires_at Optional expiration time
     * @return true if successful
     */
    bool assignUserProjectRole(const std::string& user_id,
                             const std::string& project_id,
                             const std::string& role_id,
                             const std::optional<std::chrono::system_clock::time_point>& expires_at = std::nullopt);

    /**
     * @brief Remove role from user for a project
     * @param user_id User UUID
     * @param project_id Project UUID
     * @param role_id Role UUID
     * @return true if successful
     */
    bool removeUserProjectRole(const std::string& user_id,
                             const std::string& project_id,
                             const std::string& role_id);

    /**
     * @brief Get user's roles for a project
     * @param user_id User UUID
     * @param project_id Project UUID
     * @return Vector of roles
     */
    std::vector<Role> getUserProjectRoles(const std::string& user_id,
                                        const std::string& project_id);

    /**
     * @brief Get user's organizations
     * @param user_id User UUID
     * @return Vector of organizations
     */
    std::vector<Organization> getUserOrganizations(const std::string& user_id);

    /**
     * @brief Get user's projects within an organization
     * @param user_id User UUID
     * @param org_id Organization UUID
     * @return Vector of projects
     */
    std::vector<Project> getUserProjects(const std::string& user_id,
                                       const std::string& org_id);

    // ========================================================================
    // Permission Validation
    // ========================================================================

    /**
     * @brief Check if user has permission for a project
     * @param user_id User UUID
     * @param project_id Project UUID
     * @param permission_name Permission name
     * @return Permission validation result
     */
    PermissionResult validatePermission(const std::string& user_id,
                                      const std::string& project_id,
                                      const std::string& permission_name);

    /**
     * @brief Check if user has system-level permission
     * @param user_id User UUID
     * @param permission_name Permission name
     * @return true if user has permission
     */
    bool validateSystemPermission(const std::string& user_id,
                                const std::string& permission_name);

    /**
     * @brief Get all permissions for user in a project
     * @param user_id User UUID
     * @param project_id Project UUID
     * @return Vector of permission names
     */
    std::vector<std::string> getUserProjectPermissions(const std::string& user_id,
                                                      const std::string& project_id);

private:
    DatabaseManager* db_manager_;
    
    // Helper methods
    bool isValidUUID(const std::string& uuid);
    std::string generateUUID();
    void logError(const std::string& operation, const std::string& error);
};
