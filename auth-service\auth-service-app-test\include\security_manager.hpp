﻿#pragma once
#include <string>
#include <memory>

// Forward declaration for ConfigManager
class ConfigManager;

class SecurityManager {
public:
    explicit SecurityManager(std::shared_ptr<ConfigManager> config);
    ~SecurityManager();

    // Argon2id password hashing
    std::string hash_password(const std::string& password) const;
    std::string hash_password_with_salt(const std::string& password, const std::string& salt) const;
    bool verify_password(const std::string& password, const std::string& hash) const;
    std::string generate_salt() const;

    // Token management (placeholder for Step 4)
    std::string generate_token() const;
    bool validate_token(const std::string& token) const;

private:
    std::shared_ptr<ConfigManager> config_;

    // Argon2 helper methods
    std::string encode_argon2_hash(const std::string& hash, const std::string& salt) const;
    bool decode_argon2_hash(const std::string& encoded_hash, std::string& hash, std::string& salt) const;
};
