﻿#pragma once
#include <string>
#include <optional>

class DatabaseManager;
class SecurityManager;

/**
 * @brief User Manager with Database Integration
 *
 * Provides user management operations with real database storage:
 * - User registration with email validation
 * - User authentication with password verification
 * - User profile management
 * - Integration with SecurityManager for password hashing
 *
 * Step 6 of OAuth 2.0 Implementation
 */
class UserManager {
public:
    /**
     * @brief User authentication result
     */
    struct AuthResult {
        bool success;
        std::string user_id;  // UUID
        std::string username;
        std::string email;
        std::string error_message;
    };

public:
    /**
     * @brief Constructor
     * @param db_manager Database manager
     * @param sec_manager Security manager
     */
    UserManager(DatabaseManager* db_manager, SecurityManager* sec_manager);

    /**
     * @brief Destructor
     */
    ~UserManager();

    /**
     * @brief Create a new user with email
     * @param username User's username
     * @param email User's email address
     * @param password User's password (will be hashed)
     * @return User UUID if successful, empty string if failed
     */
    std::string create_user(const std::string& username, const std::string& email, const std::string& password);

    /**
     * @brief Create a user (legacy method for compatibility)
     * @param username User's username
     * @param password User's password
     * @return true if successful
     */
    bool create_user(const std::string& username, const std::string& password);

    /**
     * @brief Authenticate user with database lookup
     * @param username Username or email
     * @param password Password to verify
     * @return Authentication result with user details
     */
    AuthResult authenticate_user(const std::string& username, const std::string& password);

    /**
     * @brief Authenticate user (legacy method for compatibility)
     * @param username Username
     * @param password Password
     * @return true if authenticated
     */
    bool authenticate_user_simple(const std::string& username, const std::string& password);

    /**
     * @brief Get user ID by username
     * @param username Username to lookup
     * @return User UUID if found, empty string if not found
     */
    std::string get_user_id(const std::string& username);

    /**
     * @brief Update user's last login time
     * @param user_id User UUID
     * @return true if successful
     */
    bool update_last_login(const std::string& user_id);

    /**
     * @brief Delete user by username
     * @param username Username to delete
     * @return true if successful
     */
    bool delete_user(const std::string& username);

    /**
     * @brief Delete user by ID
     * @param user_id User UUID to delete
     * @return true if successful
     */
    bool delete_user_by_id(const std::string& user_id);

    /**
     * @brief Validate email format
     * @param email Email to validate
     * @return true if valid format
     */
    bool is_valid_email(const std::string& email);

    /**
     * @brief Validate username format
     * @param username Username to validate
     * @return true if valid format
     */
    bool is_valid_username(const std::string& username);

private:
    DatabaseManager* db_manager_;
    SecurityManager* sec_manager_;
};
