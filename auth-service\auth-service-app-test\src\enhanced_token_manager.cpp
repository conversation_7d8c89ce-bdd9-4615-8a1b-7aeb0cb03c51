#include "enhanced_token_manager.hpp"
#include "database_manager.hpp"
#include "rbac_manager.hpp"
#include "jwt_manager.hpp"
#include <iostream>
#include <sstream>
#include <algorithm>
#include <chrono>

EnhancedTokenManager::EnhancedTokenManager(DatabaseManager* db_manager, 
                                         RBACManager* rbac_manager,
                                         JWTManager* jwt_manager)
    : db_manager_(db_manager), rbac_manager_(rbac_manager), jwt_manager_(jwt_manager) {
    if (!db_manager_) {
        throw std::invalid_argument("DatabaseManager cannot be null");
    }
    if (!rbac_manager_) {
        throw std::invalid_argument("RBACManager cannot be null");
    }
    if (!jwt_manager_) {
        throw std::invalid_argument("JWTManager cannot be null");
    }
    
    std::cout << "EnhancedTokenManager initialized with RBAC support" << std::endl;
}

EnhancedTokenManager::~EnhancedTokenManager() = default;

// ============================================================================
// Enhanced Token Generation
// ============================================================================

std::pair<std::string, std::string> EnhancedTokenManager::generateProjectTokens(const TokenRequest& request) {
    try {
        // Validate request parameters
        if (!db_manager_->isValidUUID(request.user_id) || !db_manager_->isValidUUID(request.project_id)) {
            std::cerr << "Invalid user_id or project_id in token request" << std::endl;
            return {"", ""};
        }

        // Verify user has access to the project
        auto user_roles = rbac_manager_->getUserProjectRoles(request.user_id, request.project_id);
        if (user_roles.empty()) {
            std::cerr << "User has no roles in the specified project" << std::endl;
            return {"", ""};
        }

        // Calculate effective permissions for the user in this project
        std::vector<std::string> permissions = calculateEffectivePermissions(request.user_id, request.project_id);

        // Generate access token
        std::string access_token = generateAccessToken(request);
        if (access_token.empty()) {
            std::cerr << "Failed to generate access token" << std::endl;
            return {"", ""};
        }

        // Generate refresh token (simplified for now)
        std::string refresh_token = "refresh_" + access_token.substr(0, 20);

        // Store token information in database
        std::string token_id = db_manager_->generateUUID();
        std::string query = R"(
            INSERT INTO auth_tokens (token_id, user_id, project_id, token_hash, token_type, 
                                   created_at, expires_at, is_active, client_info)
            VALUES ($1, $2, $3, $4, 'access', CURRENT_TIMESTAMP, 
                    CURRENT_TIMESTAMP + INTERVAL '1 hour', true, $5)
        )";

        std::string token_hash = generateTokenHash(access_token);
        std::string client_info = request.client_ip + "|" + request.user_agent;
        
        std::vector<std::string> params = {token_id, request.user_id, request.project_id, 
                                         token_hash, client_info};
        
        auto result = db_manager_->executeRBACQuery(query, params);
        if (!result.has_value()) {
            std::cerr << "Failed to store token in database" << std::endl;
            return {"", ""};
        }

        std::cout << "Project-scoped tokens generated for user: " << request.user_id 
                  << " in project: " << request.project_id << std::endl;

        return {access_token, refresh_token};

    } catch (const std::exception& e) {
        std::cerr << "Error generating project tokens: " << e.what() << std::endl;
        return {"", ""};
    }
}

std::string EnhancedTokenManager::generateAccessToken(const TokenRequest& request) {
    try {
        // Get project information
        auto project = rbac_manager_->getProject(request.project_id);
        if (!project.has_value()) {
            std::cerr << "Project not found: " << request.project_id << std::endl;
            return "";
        }

        // Calculate effective permissions
        std::vector<std::string> permissions = calculateEffectivePermissions(request.user_id, request.project_id);

        // Create JWT payload with project context
        EnhancedToken token_data;
        token_data.token_id = db_manager_->generateUUID();
        token_data.user_id = request.user_id;
        token_data.project_id = request.project_id;
        token_data.org_id = project->org_id;
        token_data.token_hash = "";  // Will be set later
        token_data.token_type = "access";
        token_data.jti = token_data.token_id;
        token_data.scopes = request.requested_scopes;
        token_data.permissions = permissions;
        token_data.created_at = std::chrono::system_clock::now();
        token_data.expires_at = std::chrono::system_clock::now() + request.access_token_lifetime;
        token_data.last_used_at = token_data.created_at;
        token_data.is_active = true;
        token_data.client_info = request.client_ip + "|" + request.user_agent;

        std::string payload = createJWTPayload(token_data);

        // Generate JWT token (simplified - would use actual JWT library)
        std::string token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9." + payload + ".signature";
        
        return token;

    } catch (const std::exception& e) {
        std::cerr << "Error generating access token: " << e.what() << std::endl;
        return "";
    }
}

// ============================================================================
// Enhanced Token Validation
// ============================================================================

EnhancedTokenManager::TokenValidationResult EnhancedTokenManager::validateProjectToken(const std::string& token,
                                                                const std::string& project_id,
                                                                const std::string& required_permission) {
    TokenValidationResult result;
    result.is_valid = false;
    result.project_id = project_id;

    try {
        // Get token information from database
        std::string token_hash = generateTokenHash(token);
        std::string query = R"(
            SELECT user_id, project_id, org_id, created_at, expires_at, is_active
            FROM auth_tokens
            WHERE token_hash = $1 AND token_type = 'access' AND is_active = true
        )";

        std::vector<std::string> params = {token_hash};
        auto db_result = db_manager_->executeRBACQuery(query, params);

        if (!db_result.has_value() || db_result->empty()) {
            result.error_message = "Token not found or inactive";
            return result;
        }

        const auto& row = (*db_result)[0];
        result.user_id = row["user_id"].as<std::string>();
        std::string token_project_id = row["project_id"].as<std::string>();
        result.org_id = row["org_id"].as<std::string>();

        // Verify project ID matches
        if (token_project_id != project_id) {
            result.error_message = "Token project mismatch";
            return result;
        }

        // Check if token is expired (simplified timestamp parsing)
        // In production, would parse actual timestamps
        result.expires_at = std::chrono::system_clock::now() + std::chrono::hours(1);

        // Get effective permissions
        result.permissions = calculateEffectivePermissions(result.user_id, project_id);

        // Check required permission if specified
        if (!required_permission.empty()) {
            auto perm_result = rbac_manager_->validatePermission(result.user_id, project_id, required_permission);
            if (!perm_result.has_permission) {
                result.error_message = "Insufficient permissions";
                return result;
            }
        }

        // Update last used timestamp
        updateTokenUsage(token, "");

        result.is_valid = true;
        return result;

    } catch (const std::exception& e) {
        result.error_message = "Token validation error: " + std::string(e.what());
        return result;
    }
}

bool EnhancedTokenManager::validateTokenPermission(const std::string& token,
                                                  const std::string& permission_name) {
    try {
        // Get token info
        auto token_info = getTokenInfo(token);
        if (!token_info.has_value()) {
            return false;
        }

        // Validate permission
        auto result = rbac_manager_->validatePermission(token_info->user_id, 
                                                       token_info->project_id, 
                                                       permission_name);
        return result.has_permission;

    } catch (const std::exception& e) {
        std::cerr << "Error validating token permission: " << e.what() << std::endl;
        return false;
    }
}

// ============================================================================
// Helper Methods
// ============================================================================

std::string EnhancedTokenManager::generateTokenHash(const std::string& token) {
    // Simple hash generation (in production, use proper cryptographic hash)
    std::hash<std::string> hasher;
    size_t hash_value = hasher(token);
    
    std::stringstream ss;
    ss << std::hex << hash_value;
    return ss.str();
}

std::vector<std::string> EnhancedTokenManager::calculateEffectivePermissions(const std::string& user_id,
                                                                           const std::string& project_id) {
    try {
        // Get user's roles in the project
        auto roles = rbac_manager_->getUserProjectRoles(user_id, project_id);
        
        std::vector<std::string> permissions;
        
        // For each role, get its permissions
        for (const auto& role : roles) {
            auto role_permissions = rbac_manager_->getRolePermissions(role.role_id);
            for (const auto& permission : role_permissions) {
                // Add permission if not already present
                if (std::find(permissions.begin(), permissions.end(), permission.permission_name) == permissions.end()) {
                    permissions.push_back(permission.permission_name);
                }
            }
        }

        return permissions;

    } catch (const std::exception& e) {
        std::cerr << "Error calculating effective permissions: " << e.what() << std::endl;
        return {};
    }
}

std::string EnhancedTokenManager::createJWTPayload(const EnhancedToken& token_data) {
    // Create JSON payload for JWT (simplified)
    std::stringstream payload;
    payload << "{"
            << "\"sub\":\"" << token_data.user_id << "\","
            << "\"project_id\":\"" << token_data.project_id << "\","
            << "\"org_id\":\"" << token_data.org_id << "\","
            << "\"jti\":\"" << token_data.token_id << "\","
            << "\"iat\":" << std::chrono::duration_cast<std::chrono::seconds>(
                   token_data.created_at.time_since_epoch()).count() << ","
            << "\"exp\":" << std::chrono::duration_cast<std::chrono::seconds>(
                   token_data.expires_at.time_since_epoch()).count()
            << "}";
    
    return payload.str();
}

bool EnhancedTokenManager::updateTokenUsage(const std::string& token, const std::string& client_ip) {
    try {
        std::string token_hash = generateTokenHash(token);
        std::string query = R"(
            UPDATE auth_tokens 
            SET last_used_at = CURRENT_TIMESTAMP
            WHERE token_hash = $1 AND is_active = true
        )";

        std::vector<std::string> params = {token_hash};
        auto result = db_manager_->executeRBACQuery(query, params);
        
        return result.has_value();

    } catch (const std::exception& e) {
        std::cerr << "Error updating token usage: " << e.what() << std::endl;
        return false;
    }
}

std::optional<EnhancedTokenManager::EnhancedToken> EnhancedTokenManager::getTokenInfo(const std::string& token) {
    try {
        std::string token_hash = generateTokenHash(token);
        std::string query = R"(
            SELECT token_id, user_id, project_id, org_id, token_type,
                   created_at, expires_at, last_used_at, is_active, client_info
            FROM auth_tokens
            WHERE token_hash = $1
        )";

        std::vector<std::string> params = {token_hash};
        auto result = db_manager_->executeRBACQuery(query, params);

        if (result.has_value() && !result->empty()) {
            const auto& row = (*result)[0];

            EnhancedToken token_info;
            token_info.token_id = row["token_id"].as<std::string>();
            token_info.user_id = row["user_id"].as<std::string>();
            token_info.project_id = row["project_id"].as<std::string>();
            token_info.org_id = row["org_id"].as<std::string>("");
            token_info.token_type = row["token_type"].as<std::string>();
            token_info.is_active = row["is_active"].as<bool>();
            token_info.client_info = row["client_info"].as<std::string>("");

            // Parse timestamps (simplified)
            token_info.created_at = std::chrono::system_clock::now();
            token_info.expires_at = std::chrono::system_clock::now() + std::chrono::hours(1);
            token_info.last_used_at = std::chrono::system_clock::now();

            // Get effective permissions
            token_info.permissions = calculateEffectivePermissions(token_info.user_id, token_info.project_id);

            return token_info;
        }

        return std::nullopt;

    } catch (const std::exception& e) {
        std::cerr << "Error getting token info: " << e.what() << std::endl;
        return std::nullopt;
    }
}

// ============================================================================
// Token Lifecycle Management
// ============================================================================

bool EnhancedTokenManager::revokeToken(const std::string& token, const std::string& reason) {
    try {
        std::string token_hash = generateTokenHash(token);
        std::string query = R"(
            UPDATE auth_tokens
            SET is_active = false, updated_at = CURRENT_TIMESTAMP
            WHERE token_hash = $1
        )";

        std::vector<std::string> params = {token_hash};
        auto result = db_manager_->executeRBACQuery(query, params);

        if (result.has_value()) {
            std::cout << "Token revoked successfully. Reason: " << reason << std::endl;
            return true;
        }

        return false;

    } catch (const std::exception& e) {
        std::cerr << "Error revoking token: " << e.what() << std::endl;
        return false;
    }
}

int EnhancedTokenManager::revokeUserProjectTokens(const std::string& user_id,
                                                 const std::string& project_id,
                                                 const std::string& reason) {
    try {
        std::string query = R"(
            UPDATE auth_tokens
            SET is_active = false, updated_at = CURRENT_TIMESTAMP
            WHERE user_id = $1 AND project_id = $2 AND is_active = true
        )";

        std::vector<std::string> params = {user_id, project_id};
        auto result = db_manager_->executeRBACQuery(query, params);

        if (result.has_value()) {
            std::cout << "User project tokens revoked. User: " << user_id
                      << ", Project: " << project_id << ", Reason: " << reason << std::endl;
            // Return count would require additional query in real implementation
            return 1;
        }

        return 0;

    } catch (const std::exception& e) {
        std::cerr << "Error revoking user project tokens: " << e.what() << std::endl;
        return 0;
    }
}

std::string EnhancedTokenManager::refreshAccessToken(const std::string& refresh_token,
                                                    const std::string& project_id,
                                                    const std::string& client_ip) {
    try {
        // Validate refresh token (simplified)
        if (refresh_token.substr(0, 8) != "refresh_") {
            std::cerr << "Invalid refresh token format" << std::endl;
            return "";
        }

        // Extract original token info (simplified)
        std::string original_token = refresh_token.substr(8);
        auto token_info = getTokenInfo(original_token);

        if (!token_info.has_value() || token_info->project_id != project_id) {
            std::cerr << "Invalid refresh token or project mismatch" << std::endl;
            return "";
        }

        // Generate new access token
        TokenRequest request;
        request.user_id = token_info->user_id;
        request.project_id = project_id;
        request.client_ip = client_ip;
        request.access_token_lifetime = std::chrono::hours(1);

        return generateAccessToken(request);

    } catch (const std::exception& e) {
        std::cerr << "Error refreshing access token: " << e.what() << std::endl;
        return "";
    }
}
