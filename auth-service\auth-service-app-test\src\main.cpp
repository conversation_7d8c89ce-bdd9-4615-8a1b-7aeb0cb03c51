﻿#include <iostream>
#include <memory>
#include <boost/program_options.hpp>
#include "auth_service.hpp"
#include "config_manager.hpp"

namespace po = boost::program_options;

int main(int argc, char* argv[]) {
    try {
        po::options_description desc("Auth Service Options");
        desc.add_options()
            ("help,h", "Show help message")
            ("config,c", po::value<std::string>()->default_value("/etc/auth-service/auth-service.conf"), "Configuration file path")
            ("port,p", po::value<int>()->default_value(8082), "Server port");

        po::variables_map vm;
        po::store(po::parse_command_line(argc, argv, desc), vm);
        po::notify(vm);

        if (vm.count("help")) {
            std::cout << desc << std::endl;
            return 0;
        }

        std::cout << "Creating ConfigManager with config file: " << vm["config"].as<std::string>() << std::endl;
        auto config_manager = std::make_unique<ConfigManager>(vm["config"].as<std::string>());
        std::cout << "ConfigManager created successfully" << std::endl;
        auto auth_service = std::make_unique<AuthService>(std::move(config_manager));
        
        std::cout << "Starting Auth Service on port " << vm["port"].as<int>() << std::endl;
        auth_service->start(vm["port"].as<int>());
        
        return 0;
    }
    catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}
