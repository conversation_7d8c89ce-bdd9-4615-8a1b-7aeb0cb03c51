cmake_minimum_required(VERSION 3.20)
project(auth-service-test VERSION 1.0.0 LANGUAGES CXX)

# C++23 Standard Configuration
set(CMAKE_CXX_STANDARD 23)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Compiler-specific flags for better optimization and debugging
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -O0 -Wall -Wextra")
elseif(CMAKE_BUILD_TYPE STREQUAL "Release")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -O3 -DNDEBUG")
endif()

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)

# Test compilation of individual components
add_library(rbac_manager_test STATIC
    src/rbac_manager.cpp
)

add_library(enhanced_token_manager_test STATIC
    src/enhanced_token_manager.cpp
)

add_library(database_manager_test STATIC
    src/database_manager.cpp
)

# Compiler definitions for testing
target_compile_definitions(rbac_manager_test
    PRIVATE
    RBAC_ENABLED=1
    $<$<CONFIG:Debug>:DEBUG_BUILD=1>
)

target_compile_definitions(enhanced_token_manager_test
    PRIVATE
    ENHANCED_TOKENS_ENABLED=1
    $<$<CONFIG:Debug>:DEBUG_BUILD=1>
)

target_compile_definitions(database_manager_test
    PRIVATE
    $<$<CONFIG:Debug>:DEBUG_BUILD=1>
)

# Print build configuration summary
message(STATUS "=== Auth Service Test Build Configuration ===")
message(STATUS "Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Testing individual components for compilation errors")
message(STATUS "==========================================")
