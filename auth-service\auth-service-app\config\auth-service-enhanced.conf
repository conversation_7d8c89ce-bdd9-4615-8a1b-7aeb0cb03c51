{"database": {"host": "127.0.0.1", "port": 5432, "name": "auth_service_db", "user": "auth_service_user", "password": "auth_service_password"}, "server": {"port": 8082, "log_level": "info"}, "oauth2": {"jwt": {"secret": "production-jwt-secret-key-change-this-in-production", "access_token_expiry": 3600, "refresh_token_expiry": 6048000, "algorithm": "HS256"}, "argon2": {"memory_cost": 65536, "time_cost": 3, "parallelism": 4, "salt_length": 32}, "session": {"timeout": 86400, "cleanup_interval": 3600, "max_sessions_per_user": 5}}, "multi_tenant": {"enabled": true, "default_organization": "Default Organization", "default_project": "Default Project"}, "rbac": {"enabled": true, "cache_permissions": true, "permission_cache_ttl": 300}}