# Auth Service Database - Argon2id Password Hashing

This directory contains database schema and scripts for the Auth Service with **Argon2id password hashing** support.

## Overview

The Auth Service database has been updated to use **Argon2id** password hashing instead of SHA256 for enhanced security. Argon2id is the recommended password hashing algorithm and provides better protection against various attacks.

## Files

### Schema Files
- **`auth_schema.sql`** - Complete database schema with Argon2id support
- **`update_password.sql`** - Template for updating user passwords with Argon2id hashes

### Scripts
- **`update_database.sh`** - Creates/updates database with Argon2id password hashes
- **`migrate_to_argon2id.sh`** - Migrates existing databases from SHA256 to Argon2id

### Documentation
- **`README_ARGON2ID.md`** - This file

## Argon2id Configuration

The database is configured to use Argon2id with the following parameters:
- **Memory cost**: 65536 (64 MB)
- **Time cost**: 3
- **Parallelism**: 4
- **Salt length**: 32 bytes (64 hex characters)
- **Hash length**: 64 bytes

## Database Schema Changes

### Updated Columns
- **`auth_users.salt`**: Increased from `VARCHAR(32)` to `VARCHAR(128)` to accommodate longer hex-encoded salts
- **`auth_users.password_hash`**: Now stores Argon2id hashes in format: `$argon2id$v=19$m=65536,t=3,p=4$...`

## Usage

### For New Installations

1. **Build the hash generator**:
   ```bash
   cd /opt/auth-service/tools
   ./build-hash-generator.sh
   ```

2. **Create the database**:
   ```bash
   cd /opt/auth-service/database
   ./update_database.sh
   ```

### For Existing Installations (Migration)

1. **Build the hash generator** (if not already built):
   ```bash
   cd /opt/auth-service/tools
   ./build-hash-generator.sh
   ```

2. **Run the migration script**:
   ```bash
   cd /opt/auth-service/database
   ./migrate_to_argon2id.sh
   ```

### Manual Password Updates

1. **Generate Argon2id hash**:
   ```bash
   cd /opt/auth-service/tools
   ./hash-generator "your_password_here"
   ```

2. **Update database manually**:
   ```sql
   UPDATE auth_users 
   SET password_hash = '$argon2id$v=19$m=65536,t=3,p=4$...', 
       salt = 'hex_salt_from_generator' 
   WHERE username = 'username';
   ```

## Default Users

After running the setup scripts, the following users are created:

| Username | Password | Role | System Admin |
|----------|----------|------|--------------|
| `testuser` | `testpass123` | Viewer | No |
| `btaylor-admin` | `AdminPass123!` | Super Admin | Yes |

## Security Features

### Argon2id Benefits
- **Memory-hard**: Resistant to GPU and ASIC attacks
- **Time-hard**: Configurable time cost for additional security
- **Side-channel resistant**: Protects against timing attacks
- **Future-proof**: Recommended by security experts

### Database Security
- **Unique salts**: Each password has a cryptographically random salt
- **Long salts**: 32-byte salts provide excellent entropy
- **Proper encoding**: Salts are hex-encoded for safe storage

## Verification

### Check Hash Format
```sql
SELECT username, 
       CASE 
           WHEN password_hash LIKE '$argon2id$%' THEN 'Argon2id ✅'
           ELSE 'Legacy Hash ❌'
       END as hash_format,
       LENGTH(salt) as salt_length
FROM auth_users;
```

### Test Authentication
```bash
curl -X POST http://localhost:8082/oauth/token \
     -H "Content-Type: application/json" \
     -d '{"username": "testuser", "password": "testpass123"}'
```

## Troubleshooting

### Common Issues

1. **"Hash generator not found"**
   - Build the hash generator: `cd tools && ./build-hash-generator.sh`

2. **"Salt too long for column"**
   - Run: `ALTER TABLE auth_users ALTER COLUMN salt TYPE character varying(128);`

3. **"Authentication failed"**
   - Verify auth-service is configured for Argon2id verification
   - Check password hash format in database

### Migration Verification

After migration, verify that:
- All password hashes start with `$argon2id$v=19$`
- Salt column length is 128 characters
- Authentication works with existing passwords
- Hash generator produces compatible hashes

## Dependencies

- **PostgreSQL 12+**
- **libargon2-dev** (for hash generator)
- **Auth Service tools** (hash-generator)

## Security Notes

⚠️ **Important Security Considerations**:
- Never store plaintext passwords
- Always use the hash-generator tool for password hashing
- Regularly update Argon2id parameters as computing power increases
- Monitor for failed authentication attempts
- Use HTTPS for all authentication endpoints

## Support

For issues related to Argon2id implementation:
1. Check that libargon2-dev is installed
2. Verify hash-generator tool is built and working
3. Ensure auth-service is configured for Argon2id verification
4. Review auth-service logs for detailed error messages
