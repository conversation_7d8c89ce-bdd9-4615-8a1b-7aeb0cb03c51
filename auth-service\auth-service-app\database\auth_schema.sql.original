-- Auth Service OAuth 2.0 Database Schema
-- PostgreSQL 17 Compatible
-- Created: 2025-07-05
-- Purpose: OAuth 2.0 authentication and authorization

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Drop tables if they exist (for clean reinstall)
DROP TABLE IF EXISTS auth_sessions CASCADE;
DROP TABLE IF EXISTS auth_tokens CASCADE;
DROP TABLE IF EXISTS auth_users CASCADE;

-- ============================================================================
-- Users Table - Core user accounts
-- ============================================================================
CREATE TABLE auth_users (
    user_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL, -- Argon2id hash
    salt VARCHAR(32) NOT NULL, -- Password salt
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP WITH TIME ZONE,
    
    -- Constraints
    CONSTRAINT auth_users_username_length CHECK (LENGTH(username) >= 3),
    CONSTRAINT auth_users_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT auth_users_password_hash_length CHECK (LENGTH(password_hash) >= 32)
);

-- Indexes for performance
CREATE INDEX idx_auth_users_username ON auth_users(username);
CREATE INDEX idx_auth_users_email ON auth_users(email);
CREATE INDEX idx_auth_users_active ON auth_users(is_active);
CREATE INDEX idx_auth_users_last_login ON auth_users(last_login);

-- ============================================================================
-- Tokens Table - JWT access and refresh tokens
-- ============================================================================
CREATE TABLE auth_tokens (
    token_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth_users(user_id) ON DELETE CASCADE,
    token_type VARCHAR(20) NOT NULL, -- 'access' or 'refresh'
    token_hash VARCHAR(64) NOT NULL, -- SHA256 hash of the actual token
    jti VARCHAR(36) NOT NULL UNIQUE, -- JWT ID for token identification
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    revoked_at TIMESTAMP WITH TIME ZONE,
    is_revoked BOOLEAN DEFAULT false,
    revoke_reason VARCHAR(100), -- 'logout', 'security', 'expired', etc.
    
    -- Constraints
    CONSTRAINT auth_tokens_type_check CHECK (token_type IN ('access', 'refresh')),
    CONSTRAINT auth_tokens_hash_length CHECK (LENGTH(token_hash) = 64),
    CONSTRAINT auth_tokens_expires_future CHECK (expires_at > created_at)
);

-- Indexes for performance
CREATE INDEX idx_auth_tokens_user_id ON auth_tokens(user_id);
CREATE INDEX idx_auth_tokens_type ON auth_tokens(token_type);
CREATE INDEX idx_auth_tokens_hash ON auth_tokens(token_hash);
CREATE INDEX idx_auth_tokens_jti ON auth_tokens(jti);
CREATE INDEX idx_auth_tokens_expires ON auth_tokens(expires_at);
CREATE INDEX idx_auth_tokens_active ON auth_tokens(is_revoked, expires_at);

-- ============================================================================
-- Sessions Table - User session management
-- ============================================================================
CREATE TABLE auth_sessions (
    session_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth_users(user_id) ON DELETE CASCADE,
    access_token_id UUID REFERENCES auth_tokens(token_id) ON DELETE SET NULL,
    refresh_token_id UUID REFERENCES auth_tokens(token_id) ON DELETE SET NULL,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    
    -- Constraints
    CONSTRAINT auth_sessions_expires_future CHECK (expires_at > created_at),
    CONSTRAINT auth_sessions_activity_valid CHECK (last_activity >= created_at)
);

-- Indexes for performance
CREATE INDEX idx_auth_sessions_user_id ON auth_sessions(user_id);
CREATE INDEX idx_auth_sessions_access_token ON auth_sessions(access_token_id);
CREATE INDEX idx_auth_sessions_refresh_token ON auth_sessions(refresh_token_id);
CREATE INDEX idx_auth_sessions_active ON auth_sessions(is_active, expires_at);
CREATE INDEX idx_auth_sessions_ip ON auth_sessions(ip_address);
CREATE INDEX idx_auth_sessions_last_activity ON auth_sessions(last_activity);

-- ============================================================================
-- Update Triggers - Automatic timestamp updates
-- ============================================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for auth_users table
CREATE TRIGGER update_auth_users_updated_at 
    BEFORE UPDATE ON auth_users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Function to update last_activity timestamp
CREATE OR REPLACE FUNCTION update_last_activity_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.last_activity = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for auth_sessions table
CREATE TRIGGER update_auth_sessions_last_activity 
    BEFORE UPDATE ON auth_sessions 
    FOR EACH ROW 
    EXECUTE FUNCTION update_last_activity_column();

-- ============================================================================
-- Cleanup Functions - Remove expired tokens and sessions
-- ============================================================================

-- Function to clean up expired tokens
CREATE OR REPLACE FUNCTION cleanup_expired_tokens()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM auth_tokens 
    WHERE expires_at < CURRENT_TIMESTAMP 
    AND is_revoked = false;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up expired sessions
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    UPDATE auth_sessions 
    SET is_active = false 
    WHERE expires_at < CURRENT_TIMESTAMP 
    AND is_active = true;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- Sample Data (for testing) - Remove in production
-- ============================================================================

-- Insert a test user (password: "testpass123" - will be replaced with Argon2id hash)
INSERT INTO auth_users (username, email, password_hash, salt, email_verified) 
VALUES (
    'testuser', 
    '<EMAIL>', 
    'PLACEHOLDER_HASH_TO_BE_REPLACED_WITH_ARGON2ID', 
    'PLACEHOLDER_SALT_TO_BE_REPLACED',
    true
);

-- ============================================================================
-- Grants and Permissions
-- ============================================================================

-- Grant permissions to auth service user (create user if needed)
-- Note: Replace 'auth_service_user' with actual service account name
-- CREATE USER auth_service_user WITH PASSWORD 'secure_password_here';
-- GRANT CONNECT ON DATABASE auth_service TO auth_service_user;
-- GRANT USAGE ON SCHEMA public TO auth_service_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO auth_service_user;
-- GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO auth_service_user;

-- ============================================================================
-- Schema Information
-- ============================================================================

-- Display table information
\echo 'Auth Service OAuth 2.0 Schema Created Successfully!'
\echo ''
\echo 'Tables created:'
\dt

\echo ''
\echo 'auth_users table structure:'
\d auth_users

\echo ''
\echo 'auth_tokens table structure:'
\d auth_tokens

\echo ''
\echo 'auth_sessions table structure:'
\d auth_sessions

-- ============================================================================
-- JWT Token Management (Step 4: JWT Token Management)
-- ============================================================================

-- JWT Token Storage and Tracking
CREATE TABLE jwt_tokens (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES auth_users(id) ON DELETE CASCADE,
    token_hash VARCHAR(64) UNIQUE NOT NULL,
    token_type VARCHAR(20) NOT NULL CHECK (token_type IN ('access', 'refresh')),
    scope TEXT,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    revoked_at TIMESTAMP NULL,
    last_used_at TIMESTAMP NULL,

    -- Constraints
    CONSTRAINT jwt_tokens_expires_future CHECK (expires_at > created_at),
    CONSTRAINT jwt_tokens_revoked_valid CHECK (revoked_at IS NULL OR revoked_at >= created_at)
);

-- JWT Token Blacklist for revoked tokens
CREATE TABLE jwt_token_blacklist (
    id SERIAL PRIMARY KEY,
    token_hash VARCHAR(64) UNIQUE NOT NULL,
    user_id INTEGER REFERENCES auth_users(id) ON DELETE CASCADE,
    revoked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reason VARCHAR(255),
    revoked_by INTEGER REFERENCES auth_users(id) ON DELETE SET NULL
);

-- Indexes for efficient JWT token operations
CREATE INDEX idx_jwt_tokens_hash ON jwt_tokens(token_hash);
CREATE INDEX idx_jwt_tokens_user_type ON jwt_tokens(user_id, token_type);
CREATE INDEX idx_jwt_tokens_expires ON jwt_tokens(expires_at);
CREATE INDEX idx_jwt_tokens_active ON jwt_tokens(user_id, token_type, expires_at) WHERE revoked_at IS NULL;

-- Indexes for JWT blacklist
CREATE INDEX idx_jwt_blacklist_hash ON jwt_token_blacklist(token_hash);
CREATE INDEX idx_jwt_blacklist_user ON jwt_token_blacklist(user_id);

\echo ''
\echo 'JWT token tables created successfully!'
\echo ''
\echo 'Schema creation complete. Ready for OAuth 2.0 Step 4: JWT Token Management!'
