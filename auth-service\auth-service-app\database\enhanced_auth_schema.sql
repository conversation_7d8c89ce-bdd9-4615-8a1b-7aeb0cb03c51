-- Enhanced Auth Service Database Schema with Role-Based Access Control
-- Multi-tenant support with projects and organizations

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ============================================================================
-- Organizations Table - Multi-tenant support
-- ============================================================================
CREATE TABLE IF NOT EXISTS auth_organizations (
    org_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    org_name VARCHAR(100) NOT NULL UNIQUE,
    org_domain VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- ============================================================================
-- Projects Table - Multi-project support within organizations
-- ============================================================================
CREATE TABLE IF NOT EXISTS auth_projects (
    project_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    org_id UUID NOT NULL REFERENCES auth_organizations(org_id) ON DELETE CASCADE,
    project_name VARCHAR(100) NOT NULL,
    project_description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    UNIQUE(org_id, project_name)
);

-- ============================================================================
-- Roles Table - System and project-level roles
-- ============================================================================
CREATE TABLE IF NOT EXISTS auth_roles (
    role_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    role_name VARCHAR(50) NOT NULL,
    role_description TEXT,
    is_system_role BOOLEAN DEFAULT false, -- true for system-wide roles like super_admin
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ============================================================================
-- Permissions Table - Granular permissions
-- ============================================================================
CREATE TABLE IF NOT EXISTS auth_permissions (
    permission_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    permission_name VARCHAR(100) NOT NULL UNIQUE,
    permission_description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ============================================================================
-- Role Permissions - Many-to-many relationship
-- ============================================================================
CREATE TABLE IF NOT EXISTS auth_role_permissions (
    role_id UUID NOT NULL REFERENCES auth_roles(role_id) ON DELETE CASCADE,
    permission_id UUID NOT NULL REFERENCES auth_permissions(permission_id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (role_id, permission_id)
);

-- ============================================================================
-- Enhanced Users Table with organization support
-- ============================================================================
CREATE TABLE IF NOT EXISTS auth_users (
    user_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    salt VARCHAR(32) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP WITH TIME ZONE,
    is_system_admin BOOLEAN DEFAULT false -- Super admin flag
);

-- ============================================================================
-- User Organization Memberships
-- ============================================================================
CREATE TABLE IF NOT EXISTS auth_user_organizations (
    user_id UUID NOT NULL REFERENCES auth_users(user_id) ON DELETE CASCADE,
    org_id UUID NOT NULL REFERENCES auth_organizations(org_id) ON DELETE CASCADE,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_org_admin BOOLEAN DEFAULT false,
    PRIMARY KEY (user_id, org_id)
);

-- ============================================================================
-- User Project Roles - Users can have different roles in different projects
-- ============================================================================
CREATE TABLE IF NOT EXISTS auth_user_project_roles (
    user_id UUID NOT NULL REFERENCES auth_users(user_id) ON DELETE CASCADE,
    project_id UUID NOT NULL REFERENCES auth_projects(project_id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES auth_roles(role_id) ON DELETE CASCADE,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    assigned_by UUID REFERENCES auth_users(user_id),
    PRIMARY KEY (user_id, project_id, role_id)
);

-- ============================================================================
-- Enhanced Tokens Table
-- ============================================================================
CREATE TABLE IF NOT EXISTS auth_tokens (
    token_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth_users(user_id) ON DELETE CASCADE,
    project_id UUID REFERENCES auth_projects(project_id) ON DELETE CASCADE, -- Project-specific tokens
    token_type VARCHAR(20) NOT NULL, -- 'access' or 'refresh'
    token_hash VARCHAR(64) NOT NULL,
    jti VARCHAR(36) NOT NULL UNIQUE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    revoked_at TIMESTAMP WITH TIME ZONE,
    is_revoked BOOLEAN DEFAULT false,
    scope TEXT,
    client_ip INET,
    user_agent TEXT
);

-- ============================================================================
-- Insert Default Data
-- ============================================================================

-- Default organization
INSERT INTO auth_organizations (org_name, org_domain) 
VALUES ('Default Organization', 'chcit.org') 
ON CONFLICT (org_name) DO NOTHING;

-- Default project
INSERT INTO auth_projects (org_id, project_name, project_description)
SELECT org_id, 'Default Project', 'Default project for the organization'
FROM auth_organizations WHERE org_name = 'Default Organization'
ON CONFLICT (org_id, project_name) DO NOTHING;

-- System roles
INSERT INTO auth_roles (role_name, role_description, is_system_role) VALUES
    ('super_admin', 'System super administrator with full access', true),
    ('org_admin', 'Organization administrator', false),
    ('project_admin', 'Project administrator', false),
    ('developer', 'Developer with code access', false),
    ('viewer', 'Read-only access', false)
ON CONFLICT DO NOTHING;

-- Permissions
INSERT INTO auth_permissions (permission_name, permission_description) VALUES
    ('system.admin', 'Full system administration'),
    ('org.admin', 'Organization administration'),
    ('project.admin', 'Project administration'),
    ('project.create', 'Create new projects'),
    ('user.manage', 'Manage users'),
    ('user.view', 'View users'),
    ('token.manage', 'Manage authentication tokens'),
    ('token.view', 'View token information'),
    ('audit.view', 'View audit logs'),
    ('settings.manage', 'Manage system settings')
ON CONFLICT (permission_name) DO NOTHING;

-- Assign permissions to roles
INSERT INTO auth_role_permissions (role_id, permission_id)
SELECT r.role_id, p.permission_id 
FROM auth_roles r, auth_permissions p 
WHERE r.role_name = 'super_admin' -- Super admin gets all permissions
ON CONFLICT DO NOTHING;

INSERT INTO auth_role_permissions (role_id, permission_id)
SELECT r.role_id, p.permission_id 
FROM auth_roles r, auth_permissions p 
WHERE r.role_name = 'org_admin' 
AND p.permission_name IN ('org.admin', 'project.create', 'user.manage', 'user.view', 'token.view')
ON CONFLICT DO NOTHING;

INSERT INTO auth_role_permissions (role_id, permission_id)
SELECT r.role_id, p.permission_id 
FROM auth_roles r, auth_permissions p 
WHERE r.role_name = 'project_admin' 
AND p.permission_name IN ('project.admin', 'user.view', 'token.view')
ON CONFLICT DO NOTHING;

-- Create default users
-- testuser (basic user)
INSERT INTO auth_users (username, email, password_hash, salt, first_name, last_name, email_verified, is_system_admin) 
VALUES (
    'testuser', 
    '<EMAIL>', 
    'PLACEHOLDER_HASH_TO_BE_REPLACED_WITH_ARGON2ID', 
    'PLACEHOLDER_SALT_TO_BE_REPLACED',
    'Test',
    'User',
    true,
    false
) ON CONFLICT (username) DO NOTHING;

-- btaylor-admin (system admin)
INSERT INTO auth_users (username, email, password_hash, salt, first_name, last_name, email_verified, is_system_admin) 
VALUES (
    'btaylor-admin', 
    '<EMAIL>', 
    'PLACEHOLDER_HASH_TO_BE_REPLACED_WITH_ARGON2ID', 
    'PLACEHOLDER_SALT_TO_BE_REPLACED',
    'Brian',
    'Taylor',
    true,
    true
) ON CONFLICT (username) DO NOTHING;

-- Assign users to default organization
INSERT INTO auth_user_organizations (user_id, org_id, is_org_admin)
SELECT u.user_id, o.org_id, u.is_system_admin
FROM auth_users u, auth_organizations o 
WHERE o.org_name = 'Default Organization'
AND u.username IN ('testuser', 'btaylor-admin')
ON CONFLICT DO NOTHING;

-- Assign roles to users in default project
INSERT INTO auth_user_project_roles (user_id, project_id, role_id)
SELECT u.user_id, p.project_id, r.role_id
FROM auth_users u, auth_projects p, auth_roles r, auth_organizations o
WHERE u.username = 'btaylor-admin'
AND p.project_name = 'Default Project'
AND p.org_id = o.org_id
AND o.org_name = 'Default Organization'
AND r.role_name = 'super_admin'
ON CONFLICT DO NOTHING;

INSERT INTO auth_user_project_roles (user_id, project_id, role_id)
SELECT u.user_id, p.project_id, r.role_id
FROM auth_users u, auth_projects p, auth_roles r, auth_organizations o
WHERE u.username = 'testuser'
AND p.project_name = 'Default Project'
AND p.org_id = o.org_id
AND o.org_name = 'Default Organization'
AND r.role_name = 'viewer'
ON CONFLICT DO NOTHING;

-- ============================================================================
-- Indexes for Performance
-- ============================================================================
CREATE INDEX IF NOT EXISTS idx_auth_users_username ON auth_users(username);
CREATE INDEX IF NOT EXISTS idx_auth_users_email ON auth_users(email);
CREATE INDEX IF NOT EXISTS idx_auth_users_active ON auth_users(is_active);
CREATE INDEX IF NOT EXISTS idx_auth_tokens_user_id ON auth_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_auth_tokens_expires ON auth_tokens(expires_at);
CREATE INDEX IF NOT EXISTS idx_auth_tokens_revoked ON auth_tokens(is_revoked);
CREATE INDEX IF NOT EXISTS idx_auth_user_project_roles_user ON auth_user_project_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_auth_user_project_roles_project ON auth_user_project_roles(project_id);

-- ============================================================================
-- Functions for Role Checking
-- ============================================================================
CREATE OR REPLACE FUNCTION check_user_permission(
    p_username VARCHAR(50),
    p_permission_name VARCHAR(100),
    p_project_id UUID DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
    has_permission BOOLEAN := false;
BEGIN
    -- Check if user is system admin
    SELECT is_system_admin INTO has_permission
    FROM auth_users 
    WHERE username = p_username AND is_active = true;
    
    IF has_permission THEN
        RETURN true;
    END IF;
    
    -- Check project-specific permissions
    IF p_project_id IS NOT NULL THEN
        SELECT COUNT(*) > 0 INTO has_permission
        FROM auth_users u
        JOIN auth_user_project_roles upr ON u.user_id = upr.user_id
        JOIN auth_role_permissions rp ON upr.role_id = rp.role_id
        JOIN auth_permissions p ON rp.permission_id = p.permission_id
        WHERE u.username = p_username 
        AND u.is_active = true
        AND upr.project_id = p_project_id
        AND p.permission_name = p_permission_name;
    ELSE
        -- Check system-wide permissions
        SELECT COUNT(*) > 0 INTO has_permission
        FROM auth_users u
        JOIN auth_user_project_roles upr ON u.user_id = upr.user_id
        JOIN auth_role_permissions rp ON upr.role_id = rp.role_id
        JOIN auth_permissions p ON rp.permission_id = p.permission_id
        WHERE u.username = p_username 
        AND u.is_active = true
        AND p.permission_name = p_permission_name;
    END IF;
    
    RETURN has_permission;
END;
$$ LANGUAGE plpgsql;
