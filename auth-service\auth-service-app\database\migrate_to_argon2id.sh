#!/bin/bash

# Migrate Auth Service Database from SHA256 to Argon2id
# This script migrates existing databases to use Argon2id password hashing

echo "🔄 Migrating Auth Service Database to Argon2id..."

# Database connection details
DB_NAME="auth_service_db"
DB_USER="auth_service_user"
DB_HOST="localhost"

# Check if PostgreSQL is running
if ! systemctl is-active --quiet postgresql; then
    echo "❌ PostgreSQL is not running. Starting PostgreSQL..."
    sudo systemctl start postgresql
    sleep 3
fi

# Check if database exists
DB_EXISTS=$(sudo -u postgres psql -lqt | cut -d \| -f 1 | grep -qw $DB_NAME && echo "yes" || echo "no")

if [ "$DB_EXISTS" = "no" ]; then
    echo "❌ Database $DB_NAME does not exist. Please create it first."
    exit 1
fi

echo "🔍 Checking current database schema..."

# Check if hash-generator tool exists
HASH_GENERATOR_PATH="/opt/auth-service/tools/hash-generator"
if [ ! -f "$HASH_GENERATOR_PATH" ]; then
    echo "❌ Hash generator tool not found at $HASH_GENERATOR_PATH"
    echo "   Please build the hash generator first:"
    echo "   cd /opt/auth-service/tools && ./build-hash-generator.sh"
    exit 1
fi

echo "🔧 Updating database schema for Argon2id..."

# Update salt column to support longer Argon2id salts
sudo -u postgres psql -d $DB_NAME -c "
ALTER TABLE auth_users ALTER COLUMN salt TYPE character varying(128);
"

echo "🔐 Generating new Argon2id password hashes..."

# Generate Argon2id hash for testuser (password: testpass123)
echo "   Generating hash for testuser..."
TESTUSER_OUTPUT=$($HASH_GENERATOR_PATH "testpass123")
TESTUSER_HASH=$(echo "$TESTUSER_OUTPUT" | grep "Argon2id Hash:" | cut -d' ' -f3)
TESTUSER_SALT=$(echo "$TESTUSER_OUTPUT" | grep "Salt (hex):" | cut -d' ' -f3)

if [ -z "$TESTUSER_HASH" ] || [ -z "$TESTUSER_SALT" ]; then
    echo "❌ Failed to generate hash for testuser"
    exit 1
fi

# Generate Argon2id hash for btaylor-admin (password: AdminPass123!)
echo "   Generating hash for btaylor-admin..."
ADMIN_OUTPUT=$($HASH_GENERATOR_PATH "AdminPass123!")
ADMIN_HASH=$(echo "$ADMIN_OUTPUT" | grep "Argon2id Hash:" | cut -d' ' -f3)
ADMIN_SALT=$(echo "$ADMIN_OUTPUT" | grep "Salt (hex):" | cut -d' ' -f3)

if [ -z "$ADMIN_HASH" ] || [ -z "$ADMIN_SALT" ]; then
    echo "❌ Failed to generate hash for btaylor-admin"
    exit 1
fi

echo "🔄 Updating user passwords with Argon2id hashes..."

# Update testuser password
echo "   Updating testuser..."
sudo -u postgres psql -d $DB_NAME -c "
UPDATE auth_users 
SET password_hash = '$TESTUSER_HASH', salt = '$TESTUSER_SALT' 
WHERE username = 'testuser';
"

# Update btaylor-admin password
echo "   Updating btaylor-admin..."
sudo -u postgres psql -d $DB_NAME -c "
UPDATE auth_users 
SET password_hash = '$ADMIN_HASH', salt = '$ADMIN_SALT' 
WHERE username = 'btaylor-admin';
"

echo "✅ Verifying migration..."

# Verify users have Argon2id hashes
echo "🔍 Checking password hash formats..."
sudo -u postgres psql -d $DB_NAME -c "
SELECT username, 
       CASE 
           WHEN password_hash LIKE '\$argon2id\$%' THEN 'Argon2id ✅'
           ELSE 'Legacy Hash ❌'
       END as hash_format,
       LENGTH(salt) as salt_length,
       is_system_admin
FROM auth_users 
WHERE username IN ('testuser', 'btaylor-admin')
ORDER BY username;
"

# Show user details
echo "👥 User details after migration:"
sudo -u postgres psql -d $DB_NAME -c "
SELECT username, email, first_name, last_name, is_system_admin, is_active 
FROM auth_users 
WHERE username IN ('testuser', 'btaylor-admin')
ORDER BY username;
"

echo "🎉 Argon2id migration completed successfully!"
echo ""
echo "📋 User Credentials (now using Argon2id):"
echo "   testuser: testpass123 (Standard User)"
echo "   btaylor-admin: AdminPass123! (System Administrator)"
echo ""
echo "🔧 Next steps:"
echo "   1. Restart the auth-service application"
echo "   2. Test login with both users"
echo "   3. Verify authentication is working with Argon2id"
echo "   4. All passwords are now securely hashed with Argon2id"
echo ""
echo "⚠️  Important: Make sure your auth-service application is configured"
echo "   to verify Argon2id hashes, not SHA256 hashes."
