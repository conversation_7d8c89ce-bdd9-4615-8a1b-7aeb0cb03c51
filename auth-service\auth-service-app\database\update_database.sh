#!/bin/bash

# Update Auth Service Database with Enhanced Schema and Admin User
# This script updates the database schema and creates the btaylor-admin user

echo "🔄 Updating Auth Service Database Schema..."

# Database connection details
DB_NAME="auth_service_db"
DB_USER="auth_service_user"
DB_HOST="localhost"

# Check if PostgreSQL is running
if ! systemctl is-active --quiet postgresql; then
    echo "❌ PostgreSQL is not running. Starting PostgreSQL..."
    sudo systemctl start postgresql
    sleep 3
fi

# Check if database exists
DB_EXISTS=$(sudo -u postgres psql -lqt | cut -d \| -f 1 | grep -qw $DB_NAME && echo "yes" || echo "no")

if [ "$DB_EXISTS" = "no" ]; then
    echo "📦 Creating auth service database..."
    sudo -u postgres createdb $DB_NAME
    sudo -u postgres psql -c "CREATE USER $DB_USER WITH PASSWORD 'auth_service_password';"
    sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;"
fi

echo "🔧 Applying database schema updates..."

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Apply the updated schema
sudo -u postgres psql -d $DB_NAME -f "$SCRIPT_DIR/auth_schema.sql"

echo "👤 Creating admin user with proper password hash..."

# Generate proper password hashes for users
# For testuser (password: testpass123)
TESTUSER_SALT=$(openssl rand -hex 16)
TESTUSER_HASH=$(echo -n "testpass123$TESTUSER_SALT" | sha256sum | cut -d' ' -f1)

# For btaylor-admin (password: AdminPass123!)
ADMIN_SALT=$(openssl rand -hex 16)
ADMIN_HASH=$(echo -n "AdminPass123!$ADMIN_SALT" | sha256sum | cut -d' ' -f1)

echo "🔐 Updating user password hashes..."

# Update testuser password
sudo -u postgres psql -d $DB_NAME -c "
UPDATE auth_users 
SET password_hash = '$TESTUSER_HASH', salt = '$TESTUSER_SALT' 
WHERE username = 'testuser';
"

# Update btaylor-admin password
sudo -u postgres psql -d $DB_NAME -c "
UPDATE auth_users 
SET password_hash = '$ADMIN_HASH', salt = '$ADMIN_SALT' 
WHERE username = 'btaylor-admin';
"

# Grant all permissions to auth_service_user
echo "🔑 Granting database permissions..."
sudo -u postgres psql -d $DB_NAME -c "GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO $DB_USER;"
sudo -u postgres psql -d $DB_NAME -c "GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO $DB_USER;"
sudo -u postgres psql -d $DB_NAME -c "GRANT USAGE ON SCHEMA public TO $DB_USER;"

echo "✅ Verifying database setup..."

# Verify users exist
USER_COUNT=$(sudo -u postgres psql -d $DB_NAME -t -c "SELECT COUNT(*) FROM auth_users WHERE username IN ('testuser', 'btaylor-admin');")
echo "👥 Users created: $USER_COUNT"

# Verify roles exist
ROLE_COUNT=$(sudo -u postgres psql -d $DB_NAME -t -c "SELECT COUNT(*) FROM auth_roles;")
echo "🎭 Roles created: $ROLE_COUNT"

# Verify permissions exist
PERM_COUNT=$(sudo -u postgres psql -d $DB_NAME -t -c "SELECT COUNT(*) FROM auth_permissions;")
echo "🔑 Permissions created: $PERM_COUNT"

# Show admin user details
echo "🔍 Admin user details:"
sudo -u postgres psql -d $DB_NAME -c "
SELECT username, email, first_name, last_name, is_system_admin, is_active 
FROM auth_users 
WHERE username = 'btaylor-admin';
"

# Show user roles
echo "🎭 User role assignments:"
sudo -u postgres psql -d $DB_NAME -c "
SELECT u.username, r.role_name, p.project_name
FROM auth_users u
JOIN auth_user_project_roles upr ON u.user_id = upr.user_id
JOIN auth_roles r ON upr.role_id = r.role_id
JOIN auth_projects p ON upr.project_id = p.project_id
ORDER BY u.username, r.role_name;
"

echo "🎉 Database update completed successfully!"
echo ""
echo "📋 User Credentials:"
echo "   testuser: testpass123 (Standard User)"
echo "   btaylor-admin: AdminPass123! (System Administrator)"
echo ""
echo "🔧 Next steps:"
echo "   1. Update C++ auth-service to use enhanced schema"
echo "   2. Restart the auth-service application"
echo "   3. Test login with both users"
echo "   4. Verify admin access control"
