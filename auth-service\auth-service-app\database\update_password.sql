-- Update user passwords in auth-service database
-- This script provides examples for updating user passwords with proper hashes

-- Example 1: Update testuser password
-- Password: testpass123
-- Use the hash-generator tool to generate these values
UPDATE auth_users 
SET password_hash = 'REPLACE_WITH_GENERATED_HASH', 
    salt = 'REPLACE_WITH_GENERATED_SALT' 
WHERE username = 'testuser';

-- Example 2: Update btaylor-admin password  
-- Password: AdminPass123!
-- Use the hash-generator tool to generate these values
UPDATE auth_users 
SET password_hash = 'REPLACE_WITH_GENERATED_HASH', 
    salt = 'REPLACE_WITH_GENERATED_SALT' 
WHERE username = 'btaylor-admin';

-- Verify the updates
SELECT username, email, 
       CASE WHEN password_hash IS NOT NULL AND password_hash != '' THEN 'Set' ELSE 'Not Set' END as password_status,
       CASE WHEN salt IS NOT NULL AND salt != '' THEN 'Set' ELSE 'Not Set' END as salt_status,
       is_system_admin, is_active
FROM auth_users 
WHERE username IN ('testuser', 'btaylor-admin');

-- Instructions:
-- 1. Build the hash generator: cd tools && ./build-hash-generator.sh
-- 2. Generate hash for testuser: ./hash-generator "testpass123"
-- 3. Generate hash for btaylor-admin: ./hash-generator "AdminPass123!"
-- 4. Replace the REPLACE_WITH_GENERATED_* placeholders above with actual values
-- 5. Run this script: psql -d auth_service_db -f update_password.sql
