-- Update user passwords in auth-service database with Argon2id hashes
-- This script provides examples for updating user passwords with proper Argon2id hashes

-- First, ensure salt column can handle longer Argon2id salts
ALTER TABLE auth_users ALTER COLUMN salt TYPE character varying(128);

-- Example 1: Update testuser password
-- Password: testpass123
-- Use the hash-generator tool to generate Argon2id hash and hex salt
UPDATE auth_users
SET password_hash = 'REPLACE_WITH_ARGON2ID_HASH',
    salt = 'REPLACE_WITH_HEX_SALT'
WHERE username = 'testuser';

-- Example 2: Update btaylor-admin password
-- Password: AdminPass123!
-- Use the hash-generator tool to generate Argon2id hash and hex salt
UPDATE auth_users
SET password_hash = 'REPLACE_WITH_ARGON2ID_HASH',
    salt = 'REPLACE_WITH_HEX_SALT'
WHERE username = 'btaylor-admin';

-- Verify the updates
SELECT username, email, 
       CASE WHEN password_hash IS NOT NULL AND password_hash != '' THEN 'Set' ELSE 'Not Set' END as password_status,
       CASE WHEN salt IS NOT NULL AND salt != '' THEN 'Set' ELSE 'Not Set' END as salt_status,
       is_system_admin, is_active
FROM auth_users 
WHERE username IN ('testuser', 'btaylor-admin');

-- Instructions for Argon2id password hashing:
-- 1. Build the hash generator: cd tools && ./build-hash-generator.sh
-- 2. Generate Argon2id hash for testuser: ./hash-generator "testpass123"
-- 3. Generate Argon2id hash for btaylor-admin: ./hash-generator "AdminPass123!"
-- 4. Replace the REPLACE_WITH_ARGON2ID_HASH placeholders with the Argon2id Hash output
-- 5. Replace the REPLACE_WITH_HEX_SALT placeholders with the Salt (hex) output
-- 6. Run this script: psql -d auth_service_db -f update_password.sql
--
-- Example hash-generator output:
-- Password: testpass123
-- Salt (hex): 25904446477da07b35a82b15a80337156d534b9fb85d1b66aec25251e9253633
-- Argon2id Hash: $argon2id$v=19$m=65536,t=3,p=4$JZBERkd9oHs1qCsVqAM3FW1TS5+4XRtmrsJSUeklNjM$5iPVJik2UVUK/oQpdO2pr1NfWkK86UjnHUolOKTj1H+yDPvt93SyOYH8qsQ3MG06Q3FIRB+IVxlx4apalvF4bQ
