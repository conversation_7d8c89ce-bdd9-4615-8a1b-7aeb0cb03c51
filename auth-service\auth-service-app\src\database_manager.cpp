﻿#include "database_manager.hpp"
#include "config_manager.hpp"
#include <iostream>
#include <sstream>
#include <iomanip>

DatabaseManager::DatabaseManager(ConfigManager* config)
    : config_(config), initialized_(false), rbac_logging_enabled_(true) {
    std::cout << "DatabaseManager created for PostgreSQL integration with RBAC support" << std::endl;
}

DatabaseManager::~DatabaseManager() {
    if (rbac_connection_ && rbac_connection_->is_open()) {
        rbac_connection_->close();
        std::cout << "RBAC database connection closed" << std::endl;
    }

    if (connection_ && connection_->is_open()) {
        connection_->close();
        std::cout << "Database connection closed" << std::endl;
    }
}

void DatabaseManager::initialize() {
    std::cout << "Initializing PostgreSQL database connection..." << std::endl;

    try {
        std::string conn_str = get_connection_string();
        connection_ = std::make_unique<pqxx::connection>(conn_str);

        if (connection_->is_open()) {
            std::cout << "Database connection established successfully" << std::endl;
            std::cout << "Connected to: " << connection_->dbname() << std::endl;
            initialized_ = true;
        } else {
            throw std::runtime_error("Failed to establish database connection");
        }

    } catch (const std::exception& e) {
        std::cerr << "Database initialization error: " << e.what() << std::endl;
        throw;
    }
}

bool DatabaseManager::test_connection() {
    std::cout << "Testing PostgreSQL database connection..." << std::endl;

    if (!initialized_ || !connection_ || !connection_->is_open()) {
        std::cout << "Database not initialized or connection closed" << std::endl;
        return false;
    }

    try {
        pqxx::work txn(*connection_);
        pqxx::result result = txn.exec("SELECT version()");
        txn.commit();

        if (!result.empty()) {
            std::cout << "Database connection test successful" << std::endl;
            std::cout << "PostgreSQL version: " << result[0][0].c_str() << std::endl;
            return true;
        }

    } catch (const std::exception& e) {
        std::cerr << "Database connection test failed: " << e.what() << std::endl;
    }

    return false;
}

std::string DatabaseManager::get_connection_string() {
    // Get database configuration from config manager
    std::string host = config_->get_database_host();
    std::string port = std::to_string(config_->get_database_port());
    std::string dbname = config_->get_database_name();
    std::string user = config_->get_database_user();
    std::string password = config_->get_database_password();

    std::ostringstream conn_str;
    conn_str << "host=" << host
             << " port=" << port
             << " dbname=" << dbname
             << " user=" << user
             << " password=" << password
             << " connect_timeout=10";

    std::cout << "Database connection string: host=" << host << " port=" << port
              << " dbname=" << dbname << " user=" << user << std::endl;

    return conn_str.str();
}

// User Management Operations
std::string DatabaseManager::create_user(const std::string& username, const std::string& email,
                                        const std::string& password_hash, const std::string& salt) {
    if (!initialized_ || !connection_) {
        std::cerr << "Database not initialized" << std::endl;
        return "";
    }

    try {
        pqxx::work txn(*connection_);

        // Check if username already exists
        pqxx::result check_result = txn.exec_params(
            "SELECT user_id FROM auth_users WHERE username = $1", username);

        if (!check_result.empty()) {
            std::cout << "User already exists: " << username << std::endl;
            return "";
        }

        // Insert new user
        pqxx::result result = txn.exec_params(
            "INSERT INTO auth_users (username, email, password_hash, salt, email_verified, is_active) "
            "VALUES ($1, $2, $3, $4, false, true) RETURNING user_id",
            username, email, password_hash, salt);

        txn.commit();

        if (!result.empty()) {
            std::string user_id = result[0][0].as<std::string>();
            std::cout << "User created successfully: " << username << " (ID: " << user_id << ")" << std::endl;
            return user_id;
        }

    } catch (const std::exception& e) {
        std::cerr << "Error creating user: " << e.what() << std::endl;
    }

    return "";
}

std::optional<DatabaseManager::User> DatabaseManager::get_user_by_username(const std::string& username) {
    if (!initialized_ || !connection_) {
        std::cerr << "Database not initialized" << std::endl;
        return std::nullopt;
    }

    try {
        pqxx::work txn(*connection_);

        pqxx::result result = txn.exec_params(
            "SELECT user_id, username, email, password_hash, salt, created_at, updated_at, "
            "last_login, is_active, email_verified, failed_login_attempts "
            "FROM auth_users WHERE username = $1 AND is_active = true",
            username);

        txn.commit();

        if (!result.empty()) {
            return row_to_user(result[0]);
        }

    } catch (const std::exception& e) {
        std::cerr << "Error getting user by username: " << e.what() << std::endl;
    }

    return std::nullopt;
}

std::optional<DatabaseManager::User> DatabaseManager::get_user_by_id(const std::string& user_id) {
    if (!initialized_ || !connection_) {
        std::cerr << "Database not initialized" << std::endl;
        return std::nullopt;
    }

    try {
        pqxx::work txn(*connection_);

        pqxx::result result = txn.exec_params(
            "SELECT user_id, username, email, password_hash, salt, created_at, updated_at, "
            "last_login, is_active, email_verified, failed_login_attempts "
            "FROM auth_users WHERE user_id = $1 AND is_active = true",
            user_id);

        txn.commit();

        if (!result.empty()) {
            return row_to_user(result[0]);
        }

    } catch (const std::exception& e) {
        std::cerr << "Error getting user by ID: " << e.what() << std::endl;
    }

    return std::nullopt;
}

bool DatabaseManager::update_user_last_login(const std::string& user_id) {
    if (!initialized_ || !connection_) {
        std::cerr << "Database not initialized" << std::endl;
        return false;
    }

    try {
        pqxx::work txn(*connection_);

        pqxx::result result = txn.exec_params(
            "UPDATE auth_users SET last_login = NOW(), updated_at = NOW() WHERE user_id = $1 AND is_active = true",
            user_id);

        txn.commit();

        return result.affected_rows() > 0;

    } catch (const std::exception& e) {
        std::cerr << "Error updating user last login: " << e.what() << std::endl;
    }

    return false;
}

bool DatabaseManager::delete_user(const std::string& user_id) {
    if (!initialized_ || !connection_) {
        std::cerr << "Database not initialized" << std::endl;
        return false;
    }

    try {
        pqxx::work txn(*connection_);

        // Soft delete - mark as inactive
        pqxx::result result = txn.exec_params(
            "UPDATE auth_users SET is_active = false, updated_at = NOW() WHERE user_id = $1",
            user_id);

        txn.commit();

        if (result.affected_rows() > 0) {
            std::cout << "User deleted successfully (ID: " << user_id << ")" << std::endl;
            return true;
        }

    } catch (const std::exception& e) {
        std::cerr << "Error deleting user: " << e.what() << std::endl;
    }

    return false;
}

// JWT Token Operations
std::string DatabaseManager::store_jwt_token(const std::string& user_id, const std::string& token_hash,
                                            const std::string& token_type, const std::string& jti,
                                            const std::chrono::system_clock::time_point& expires_at) {
    if (!initialized_ || !connection_) {
        std::cerr << "Database not initialized" << std::endl;
        return "";
    }

    try {
        pqxx::work txn(*connection_);

        // Convert expires_at to timestamp
        auto expires_time_t = std::chrono::system_clock::to_time_t(expires_at);
        std::ostringstream expires_str;
        expires_str << std::put_time(std::gmtime(&expires_time_t), "%Y-%m-%d %H:%M:%S");

        pqxx::result result = txn.exec_params(
            "INSERT INTO auth_tokens (user_id, token_type, token_hash, jti, expires_at, is_revoked) "
            "VALUES ($1, $2, $3, $4, $5, false) RETURNING token_id",
            user_id, token_type, token_hash, jti, expires_str.str());

        txn.commit();

        if (!result.empty()) {
            std::string token_id = result[0][0].as<std::string>();
            std::cout << "JWT token stored: " << token_type << " for user " << user_id
                      << " (Token ID: " << token_id << ")" << std::endl;
            return token_id;
        }

    } catch (const std::exception& e) {
        std::cerr << "Error storing JWT token: " << e.what() << std::endl;
    }

    return "";
}

std::optional<DatabaseManager::JWTTokenRecord> DatabaseManager::get_jwt_token(const std::string& token_hash) {
    if (!initialized_ || !connection_) {
        std::cerr << "Database not initialized" << std::endl;
        return std::nullopt;
    }

    try {
        pqxx::work txn(*connection_);

        pqxx::result result = txn.exec_params(
            "SELECT token_id, user_id, token_hash, token_type, jti, expires_at, created_at, is_revoked "
            "FROM auth_tokens WHERE token_hash = $1",
            token_hash);

        txn.commit();

        if (!result.empty()) {
            return row_to_jwt_token(result[0]);
        }

    } catch (const std::exception& e) {
        std::cerr << "Error getting JWT token: " << e.what() << std::endl;
    }

    return std::nullopt;
}

bool DatabaseManager::revoke_jwt_token(const std::string& token_hash) {
    if (!initialized_ || !connection_) {
        std::cerr << "Database not initialized" << std::endl;
        return false;
    }

    try {
        pqxx::work txn(*connection_);

        pqxx::result result = txn.exec_params(
            "UPDATE auth_tokens SET is_revoked = true, revoked_at = NOW() WHERE token_hash = $1",
            token_hash);

        txn.commit();

        if (result.affected_rows() > 0) {
            std::cout << "JWT token revoked successfully" << std::endl;
            return true;
        }

    } catch (const std::exception& e) {
        std::cerr << "Error revoking JWT token: " << e.what() << std::endl;
    }

    return false;
}

bool DatabaseManager::blacklist_jwt_token(const std::string& token_hash,
                                         const std::chrono::system_clock::time_point& expires_at) {
    if (!initialized_ || !connection_) {
        std::cerr << "Database not initialized" << std::endl;
        return false;
    }

    try {
        pqxx::work txn(*connection_);

        // Convert expires_at to timestamp
        auto expires_time_t = std::chrono::system_clock::to_time_t(expires_at);
        std::ostringstream expires_str;
        expires_str << std::put_time(std::gmtime(&expires_time_t), "%Y-%m-%d %H:%M:%S");

        pqxx::result result = txn.exec_params(
            "INSERT INTO jwt_token_blacklist (token_hash, expires_at, created_at) "
            "VALUES ($1, $2, NOW()) ON CONFLICT (token_hash) DO NOTHING",
            token_hash, expires_str.str());

        txn.commit();

        std::cout << "JWT token added to blacklist" << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cerr << "Error blacklisting JWT token: " << e.what() << std::endl;
    }

    return false;
}

bool DatabaseManager::is_token_blacklisted(const std::string& token_hash) {
    if (!initialized_ || !connection_) {
        std::cerr << "Database not initialized" << std::endl;
        return false;
    }

    try {
        pqxx::work txn(*connection_);

        pqxx::result result = txn.exec_params(
            "SELECT 1 FROM jwt_token_blacklist WHERE token_hash = $1 AND expires_at > NOW()",
            token_hash);

        txn.commit();

        return !result.empty();

    } catch (const std::exception& e) {
        std::cerr << "Error checking token blacklist: " << e.what() << std::endl;
    }

    return false;
}

int DatabaseManager::cleanup_expired_tokens() {
    if (!initialized_ || !connection_) {
        std::cerr << "Database not initialized" << std::endl;
        return 0;
    }

    try {
        pqxx::work txn(*connection_);

        // Clean up expired tokens
        pqxx::result token_result = txn.exec(
            "DELETE FROM jwt_tokens WHERE expires_at < NOW()");

        // Clean up expired blacklist entries
        pqxx::result blacklist_result = txn.exec(
            "DELETE FROM jwt_token_blacklist WHERE expires_at < NOW()");

        txn.commit();

        int total_cleaned = token_result.affected_rows() + blacklist_result.affected_rows();

        if (total_cleaned > 0) {
            std::cout << "Cleaned up " << total_cleaned << " expired token records" << std::endl;
        }

        return total_cleaned;

    } catch (const std::exception& e) {
        std::cerr << "Error cleaning up expired tokens: " << e.what() << std::endl;
    }

    return 0;
}

// Utility Methods
DatabaseManager::User DatabaseManager::row_to_user(const pqxx::row& row) {
    User user;
    user.user_id = row["user_id"].as<std::string>();
    user.username = row["username"].as<std::string>();
    user.email = row["email"].as<std::string>();
    user.password_hash = row["password_hash"].as<std::string>();
    user.salt = row["salt"].as<std::string>();
    user.is_active = row["is_active"].as<bool>();
    user.email_verified = row["email_verified"].as<bool>();
    user.failed_login_attempts = row["failed_login_attempts"].as<int>();

    // Convert timestamps - for simplicity, using current time
    // In production, parse the actual timestamps from the database
    user.created_at = std::chrono::system_clock::now();
    user.updated_at = std::chrono::system_clock::now();
    user.last_login = std::chrono::system_clock::now();

    return user;
}

DatabaseManager::JWTTokenRecord DatabaseManager::row_to_jwt_token(const pqxx::row& row) {
    JWTTokenRecord token;
    token.token_id = row["token_id"].as<std::string>();
    token.user_id = row["user_id"].as<std::string>();
    token.token_hash = row["token_hash"].as<std::string>();
    token.token_type = row["token_type"].as<std::string>();
    token.jti = row["jti"].as<std::string>();
    token.is_revoked = row["is_revoked"].as<bool>();

    // Convert timestamps - for simplicity, using current time
    // In production, parse the actual timestamps from the database
    token.expires_at = std::chrono::system_clock::now() + std::chrono::hours(1);
    token.created_at = std::chrono::system_clock::now();

    return token;
}

// ============================================================================
// RBAC Database Operations Implementation
// ============================================================================

std::optional<pqxx::result> DatabaseManager::executeRBACQuery(const std::string& query,
                                                             const std::vector<std::string>& params) {
    try {
        if (!initialized_ || !connection_ || !connection_->is_open()) {
            std::cerr << "Database not initialized for RBAC query" << std::endl;
            return std::nullopt;
        }

        pqxx::work txn(*connection_);
        pqxx::result result;

        if (params.empty()) {
            result = txn.exec(query);
        } else {
            result = executeParameterizedQuery(query, params);
        }

        txn.commit();

        logRBACOperation("executeRBACQuery", query, params, true);
        return result;

    } catch (const std::exception& e) {
        std::cerr << "RBAC query execution error: " << e.what() << std::endl;
        logRBACOperation("executeRBACQuery", query, params, false);
        return std::nullopt;
    }
}

bool DatabaseManager::executeRBACTransaction(const std::vector<std::pair<std::string, std::vector<std::string>>>& queries) {
    try {
        if (!initialized_ || !connection_ || !connection_->is_open()) {
            std::cerr << "Database not initialized for RBAC transaction" << std::endl;
            return false;
        }

        pqxx::work txn(*connection_);

        for (const auto& query_pair : queries) {
            const std::string& query = query_pair.first;
            const std::vector<std::string>& params = query_pair.second;

            if (params.empty()) {
                txn.exec(query);
            } else {
                // For parameterized queries in transaction
                pqxx::result result = executeParameterizedQuery(query, params);
            }
        }

        txn.commit();

        logRBACOperation("executeRBACTransaction",
                        "Transaction with " + std::to_string(queries.size()) + " queries",
                        {}, true);
        return true;

    } catch (const std::exception& e) {
        std::cerr << "RBAC transaction error: " << e.what() << std::endl;
        logRBACOperation("executeRBACTransaction", "Transaction failed", {}, false);
        return false;
    }
}

std::string DatabaseManager::generateUUID() {
    try {
        if (!initialized_ || !connection_ || !connection_->is_open()) {
            std::cerr << "Database not initialized for UUID generation" << std::endl;
            return "";
        }

        pqxx::work txn(*connection_);
        pqxx::result result = txn.exec("SELECT uuid_generate_v4()::text");
        txn.commit();

        if (!result.empty() && result.size() > 0) {
            std::string uuid = result[0][0].as<std::string>();
            logRBACOperation("generateUUID", "UUID generated", {uuid}, true);
            return uuid;
        }

        return "";

    } catch (const std::exception& e) {
        std::cerr << "UUID generation error: " << e.what() << std::endl;
        logRBACOperation("generateUUID", "UUID generation failed", {}, false);
        return "";
    }
}

bool DatabaseManager::isValidUUID(const std::string& uuid) {
    // UUID v4 regex pattern: 8-4-4-4-12 hexadecimal digits
    if (uuid.length() != 36) {
        return false;
    }

    // Check format: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
    if (uuid[8] != '-' || uuid[13] != '-' || uuid[18] != '-' || uuid[23] != '-') {
        return false;
    }

    // Check if all other characters are hexadecimal
    for (size_t i = 0; i < uuid.length(); ++i) {
        if (i == 8 || i == 13 || i == 18 || i == 23) {
            continue; // Skip dashes
        }

        char c = uuid[i];
        if (!((c >= '0' && c <= '9') || (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F'))) {
            return false;
        }
    }

    return true;
}

pqxx::connection* DatabaseManager::getRBACConnection() {
    try {
        // Create dedicated RBAC connection if it doesn't exist
        if (!rbac_connection_ || !rbac_connection_->is_open()) {
            std::string conn_str = get_connection_string();
            rbac_connection_ = std::make_unique<pqxx::connection>(conn_str);

            if (rbac_connection_->is_open()) {
                std::cout << "RBAC database connection established" << std::endl;
            }
        }

        return rbac_connection_.get();

    } catch (const std::exception& e) {
        std::cerr << "RBAC connection error: " << e.what() << std::endl;
        return nullptr;
    }
}

pqxx::result DatabaseManager::executeParameterizedQuery(const std::string& query,
                                                       const std::vector<std::string>& params) {
    pqxx::work txn(*connection_);

    // Prepare the query with parameters
    std::string prepared_query = query;

    // Simple parameter substitution (in production, use proper prepared statements)
    for (size_t i = 0; i < params.size(); ++i) {
        std::string placeholder = "$" + std::to_string(i + 1);
        std::string escaped_param = txn.esc(params[i]);

        size_t pos = prepared_query.find(placeholder);
        if (pos != std::string::npos) {
            prepared_query.replace(pos, placeholder.length(), "'" + escaped_param + "'");
        }
    }

    return txn.exec(prepared_query);
}

void DatabaseManager::logRBACOperation(const std::string& operation,
                                      const std::string& query,
                                      const std::vector<std::string>& params,
                                      bool success) {
    if (!rbac_logging_enabled_) {
        return;
    }

    std::cout << "[RBAC] " << operation << ": " << (success ? "SUCCESS" : "FAILED") << std::endl;
    std::cout << "[RBAC] Query: " << query << std::endl;

    if (!params.empty()) {
        std::cout << "[RBAC] Params: ";
        for (size_t i = 0; i < params.size(); ++i) {
            std::cout << "$" << (i + 1) << "=" << params[i];
            if (i < params.size() - 1) std::cout << ", ";
        }
        std::cout << std::endl;
    }
}
