#include "rate_limiter.hpp"
#include <iostream>
#include <algorithm>

RateLimiter::RateLimiter() {
    // Configure default rate limits for OAuth endpoints
    RateLimitConfig token_config;
    token_config.requests_per_minute = 5;  // Very strict for token generation
    token_config.burst_limit = 3;
    endpoint_configs_["/oauth/token"] = token_config;

    RateLimitConfig api_config;
    api_config.requests_per_minute = 30;   // Moderate for other API calls
    api_config.burst_limit = 10;
    endpoint_configs_["/oauth/refresh"] = api_config;
    endpoint_configs_["/oauth/validate"] = api_config;
    endpoint_configs_["/oauth/revoke"] = api_config;

    RateLimitConfig health_config;
    health_config.requests_per_minute = 100; // Generous for health checks
    health_config.burst_limit = 20;
    endpoint_configs_["/health"] = health_config;

    std::cout << "RateLimiter initialized with OAuth 2.0 endpoint configurations" << std::endl;
}

RateLimiter::RateLimitResult RateLimiter::checkLimit(const std::string& client_ip, const std::string& endpoint) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    stats_.total_requests++;
    auto now = std::chrono::system_clock::now();

    // Check blacklist first
    if (isBlacklisted(client_ip)) {
        stats_.blacklisted_requests++;
        stats_.blocked_requests++;
        return {false, 0, now + std::chrono::minutes(1), "Client is blacklisted"};
    }

    // Get configuration for this endpoint
    const auto& config = getEndpointConfig(endpoint);
    
    // Get or create client tracker for this endpoint
    auto& endpoint_trackers = client_trackers_[endpoint];
    auto& tracker = endpoint_trackers[client_ip];

    // Check sliding window rate limit
    if (!checkSlidingWindow(tracker, config, now)) {
        stats_.blocked_requests++;
        auto reset_time = now + config.window_size;
        
        // Auto-blacklist if too many violations
        if (tracker.request_times.size() > config.requests_per_minute * 2) {
            blacklistClient(client_ip, std::chrono::minutes(5));
            return {false, 0, reset_time, "Rate limit exceeded - client temporarily blacklisted"};
        }
        
        return {false, 0, reset_time, "Rate limit exceeded"};
    }

    // Check burst limit
    if (!checkBurstLimit(tracker, config, now)) {
        stats_.blocked_requests++;
        auto reset_time = now + std::chrono::seconds(1);
        return {false, 0, reset_time, "Burst limit exceeded"};
    }

    // Allow request and update tracker
    tracker.request_times.push_back(now);
    tracker.last_request = now;
    tracker.burst_count++;
    stats_.allowed_requests++;

    // Calculate remaining requests
    int remaining = config.requests_per_minute - static_cast<int>(tracker.request_times.size());
    auto reset_time = now + config.window_size;

    // Cleanup old entries periodically
    if (stats_.total_requests % 100 == 0) {
        cleanup();
    }

    return {true, remaining, reset_time, "Request allowed"};
}

void RateLimiter::configureEndpoint(const std::string& endpoint, const RateLimitConfig& config) {
    std::lock_guard<std::mutex> lock(mutex_);
    endpoint_configs_[endpoint] = config;
    std::cout << "Rate limit configured for " << endpoint 
              << ": " << config.requests_per_minute << " req/min, burst: " << config.burst_limit << std::endl;
}

void RateLimiter::blacklistClient(const std::string& client_ip, std::chrono::seconds duration) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto expires_at = (duration.count() == 0) ? 
        std::chrono::system_clock::time_point::max() :
        std::chrono::system_clock::now() + duration;
    
    blacklist_[client_ip] = {expires_at, "Rate limit violations"};
    std::cout << "Client blacklisted: " << client_ip << " for " << duration.count() << " seconds" << std::endl;
}

void RateLimiter::removeFromBlacklist(const std::string& client_ip) {
    std::lock_guard<std::mutex> lock(mutex_);
    blacklist_.erase(client_ip);
    std::cout << "Client removed from blacklist: " << client_ip << std::endl;
}

RateLimiter::Stats RateLimiter::getStats() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return stats_;
}

const RateLimiter::RateLimitConfig& RateLimiter::getEndpointConfig(const std::string& endpoint) const {
    auto it = endpoint_configs_.find(endpoint);
    if (it != endpoint_configs_.end()) {
        return it->second;
    }
    
    // Return default config for unknown endpoints
    static RateLimitConfig default_config;
    default_config.requests_per_minute = 60;
    default_config.burst_limit = 10;
    return default_config;
}

bool RateLimiter::isBlacklisted(const std::string& client_ip) const {
    auto it = blacklist_.find(client_ip);
    if (it == blacklist_.end()) {
        return false;
    }
    
    // Check if blacklist entry has expired
    if (it->second.expires_at <= std::chrono::system_clock::now()) {
        // Remove expired entry (const_cast is safe here as we're cleaning up)
        const_cast<std::unordered_map<std::string, BlacklistEntry>&>(blacklist_).erase(it);
        return false;
    }
    
    return true;
}

void RateLimiter::cleanup() {
    auto now = std::chrono::system_clock::now();
    
    // Clean up expired blacklist entries
    for (auto it = blacklist_.begin(); it != blacklist_.end();) {
        if (it->second.expires_at <= now) {
            it = blacklist_.erase(it);
        } else {
            ++it;
        }
    }
    
    // Clean up old request times for all clients
    for (auto& endpoint_pair : client_trackers_) {
        for (auto& client_pair : endpoint_pair.second) {
            auto& tracker = client_pair.second;
            auto& config = getEndpointConfig(endpoint_pair.first);
            
            // Remove requests older than the window
            auto cutoff = now - config.window_size;
            tracker.request_times.erase(
                std::remove_if(tracker.request_times.begin(), tracker.request_times.end(),
                    [cutoff](const auto& time) { return time < cutoff; }),
                tracker.request_times.end()
            );
        }
    }
}

bool RateLimiter::checkSlidingWindow(ClientTracker& tracker, const RateLimitConfig& config, 
                                   const std::chrono::system_clock::time_point& now) {
    // Remove old requests outside the window
    auto cutoff = now - config.window_size;
    tracker.request_times.erase(
        std::remove_if(tracker.request_times.begin(), tracker.request_times.end(),
            [cutoff](const auto& time) { return time < cutoff; }),
        tracker.request_times.end()
    );
    
    return static_cast<int>(tracker.request_times.size()) < config.requests_per_minute;
}

bool RateLimiter::checkBurstLimit(ClientTracker& tracker, const RateLimitConfig& config,
                                const std::chrono::system_clock::time_point& now) {
    // Reset burst counter if more than 1 second has passed
    if (now - tracker.burst_start >= std::chrono::seconds(1)) {
        tracker.burst_count = 0;
        tracker.burst_start = now;
    }
    
    return tracker.burst_count < config.burst_limit;
}
