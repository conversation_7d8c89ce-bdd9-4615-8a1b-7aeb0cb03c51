#include "rbac_manager.hpp"
#include "database_manager.hpp"
#include <iostream>
#include <sstream>
#include <regex>
#include <random>

RBACManager::RBACManager(DatabaseManager* db_manager) 
    : db_manager_(db_manager) {
    if (!db_manager_) {
        throw std::invalid_argument("DatabaseManager cannot be null");
    }
}

RBACManager::~RBACManager() = default;

// ============================================================================
// Organization Management
// ============================================================================

std::string RBACManager::createOrganization(const std::string& org_name, 
                                           const std::string& org_domain) {
    try {
        if (org_name.empty()) {
            logError("createOrganization", "Organization name cannot be empty");
            return "";
        }

        // Check if organization name already exists
        auto existing = getOrganizationByName(org_name);
        if (existing.has_value()) {
            logError("createOrganization", "Organization name already exists: " + org_name);
            return "";
        }

        std::string org_id = db_manager_->generateUUID();
        if (org_id.empty()) {
            logError("createOrganization", "Failed to generate UUID");
            return "";
        }

        // SQL query to insert new organization
        std::string query = R"(
            INSERT INTO auth_organizations (org_id, org_name, org_domain, created_at, updated_at, is_active)
            VALUES ($1, $2, $3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, true)
            RETURNING org_id
        )";

        // Execute query through database manager
        std::vector<std::string> params = {org_id, org_name, org_domain};
        auto result = db_manager_->executeRBACQuery(query, params);

        if (result.has_value() && !result->empty()) {
            std::cout << "Organization created successfully: " << org_name << " with ID: " << org_id << std::endl;
            return org_id;
        } else {
            logError("createOrganization", "Database insertion failed");
            return "";
        }
        
    } catch (const std::exception& e) {
        logError("createOrganization", e.what());
        return "";
    }
}

std::optional<RBACManager::Organization> RBACManager::getOrganization(const std::string& org_id) {
    try {
        if (!isValidUUID(org_id)) {
            logError("getOrganization", "Invalid organization ID format");
            return std::nullopt;
        }

        // SQL query to get organization by ID
        std::string query = R"(
            SELECT org_id, org_name, org_domain, created_at, updated_at, is_active
            FROM auth_organizations
            WHERE org_id = $1 AND is_active = true
        )";

        std::vector<std::string> params = {org_id};
        auto result = db_manager_->executeRBACQuery(query, params);

        if (result.has_value() && !result->empty()) {
            const auto& row = (*result)[0];

            Organization org;
            org.org_id = row["org_id"].as<std::string>();
            org.org_name = row["org_name"].as<std::string>();
            org.org_domain = row["org_domain"].as<std::string>("");
            org.is_active = row["is_active"].as<bool>();

            // Parse timestamps (simplified for now)
            org.created_at = std::chrono::system_clock::now();
            org.updated_at = std::chrono::system_clock::now();

            return org;
        }

        return std::nullopt;
        
    } catch (const std::exception& e) {
        logError("getOrganization", e.what());
        return std::nullopt;
    }
}

std::optional<RBACManager::Organization> RBACManager::getOrganizationByName(const std::string& org_name) {
    try {
        if (org_name.empty()) {
            logError("getOrganizationByName", "Organization name cannot be empty");
            return std::nullopt;
        }

        // SQL query to get organization by name
        std::string query = R"(
            SELECT org_id, org_name, org_domain, created_at, updated_at, is_active
            FROM auth_organizations
            WHERE org_name = $1 AND is_active = true
        )";

        std::vector<std::string> params = {org_name};
        auto result = db_manager_->executeRBACQuery(query, params);

        if (result.has_value() && !result->empty()) {
            const auto& row = (*result)[0];

            Organization org;
            org.org_id = row["org_id"].as<std::string>();
            org.org_name = row["org_name"].as<std::string>();
            org.org_domain = row["org_domain"].as<std::string>("");
            org.is_active = row["is_active"].as<bool>();

            // Parse timestamps (simplified for now)
            org.created_at = std::chrono::system_clock::now();
            org.updated_at = std::chrono::system_clock::now();

            return org;
        }

        return std::nullopt;
        
    } catch (const std::exception& e) {
        logError("getOrganizationByName", e.what());
        return std::nullopt;
    }
}

bool RBACManager::updateOrganization(const std::string& org_id, 
                                    const std::string& org_name,
                                    const std::string& org_domain) {
    try {
        if (!isValidUUID(org_id) || org_name.empty()) {
            logError("updateOrganization", "Invalid parameters");
            return false;
        }

        // SQL query to update organization
        std::string query = R"(
            UPDATE auth_organizations
            SET org_name = $2, org_domain = $3, updated_at = CURRENT_TIMESTAMP
            WHERE org_id = $1 AND is_active = true
        )";

        std::vector<std::string> params = {org_id, org_name, org_domain};
        auto result = db_manager_->executeRBACQuery(query, params);

        if (result.has_value()) {
            std::cout << "Organization updated successfully: " << org_id << " to name: " << org_name << std::endl;
            return true;
        } else {
            logError("updateOrganization", "Database update failed");
            return false;
        }
        
    } catch (const std::exception& e) {
        logError("updateOrganization", e.what());
        return false;
    }
}

bool RBACManager::deleteOrganization(const std::string& org_id) {
    try {
        if (!isValidUUID(org_id)) {
            logError("deleteOrganization", "Invalid organization ID format");
            return false;
        }

        // Soft delete - set is_active to false
        std::string query = R"(
            UPDATE auth_organizations
            SET is_active = false, updated_at = CURRENT_TIMESTAMP
            WHERE org_id = $1
        )";

        std::vector<std::string> params = {org_id};
        auto result = db_manager_->executeRBACQuery(query, params);

        if (result.has_value()) {
            std::cout << "Organization soft deleted successfully: " << org_id << std::endl;
            return true;
        } else {
            logError("deleteOrganization", "Database update failed");
            return false;
        }
        
    } catch (const std::exception& e) {
        logError("deleteOrganization", e.what());
        return false;
    }
}

std::vector<RBACManager::Organization> RBACManager::listOrganizations(bool include_inactive) {
    try {
        std::string query = R"(
            SELECT org_id, org_name, org_domain, created_at, updated_at, is_active
            FROM auth_organizations
        )";
        
        if (!include_inactive) {
            query += " WHERE is_active = true";
        }
        
        query += " ORDER BY org_name";

        // TODO: Implement actual database query
        std::vector<Organization> organizations;
        return organizations;
        
    } catch (const std::exception& e) {
        logError("listOrganizations", e.what());
        return {};
    }
}

// ============================================================================
// Project Management
// ============================================================================

std::string RBACManager::createProject(const std::string& org_id,
                                      const std::string& project_name,
                                      const std::string& project_description) {
    try {
        if (!isValidUUID(org_id) || project_name.empty()) {
            logError("createProject", "Invalid parameters");
            return "";
        }

        // Verify organization exists
        auto org = getOrganization(org_id);
        if (!org.has_value()) {
            logError("createProject", "Organization not found: " + org_id);
            return "";
        }

        std::string project_id = db_manager_->generateUUID();
        if (project_id.empty()) {
            logError("createProject", "Failed to generate UUID");
            return "";
        }

        // SQL query to insert new project
        std::string query = R"(
            INSERT INTO auth_projects (project_id, org_id, project_name, project_description, created_at, updated_at, is_active)
            VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, true)
            RETURNING project_id
        )";

        std::vector<std::string> params = {project_id, org_id, project_name, project_description};
        auto result = db_manager_->executeRBACQuery(query, params);

        if (result.has_value() && !result->empty()) {
            std::cout << "Project created successfully: " << project_name << " in org: " << org_id << std::endl;
            return project_id;
        } else {
            logError("createProject", "Database insertion failed");
            return "";
        }
        
    } catch (const std::exception& e) {
        logError("createProject", e.what());
        return "";
    }
}

std::optional<RBACManager::Project> RBACManager::getProject(const std::string& project_id) {
    try {
        if (!isValidUUID(project_id)) {
            logError("getProject", "Invalid project ID format");
            return std::nullopt;
        }

        // SQL query to get project by ID
        std::string query = R"(
            SELECT project_id, org_id, project_name, project_description, created_at, updated_at, is_active
            FROM auth_projects
            WHERE project_id = $1 AND is_active = true
        )";

        std::vector<std::string> params = {project_id};
        auto result = db_manager_->executeRBACQuery(query, params);

        if (result.has_value() && !result->empty()) {
            const auto& row = (*result)[0];

            Project project;
            project.project_id = row["project_id"].as<std::string>();
            project.org_id = row["org_id"].as<std::string>();
            project.project_name = row["project_name"].as<std::string>();
            project.project_description = row["project_description"].as<std::string>("");
            project.is_active = row["is_active"].as<bool>();

            // Parse timestamps (simplified for now)
            project.created_at = std::chrono::system_clock::now();
            project.updated_at = std::chrono::system_clock::now();

            return project;
        }

        return std::nullopt;
        
    } catch (const std::exception& e) {
        logError("getProject", e.what());
        return std::nullopt;
    }
}

// ============================================================================
// Permission Validation
// ============================================================================

RBACManager::PermissionResult RBACManager::validatePermission(const std::string& user_id,
                                                             const std::string& project_id,
                                                             const std::string& permission_name) {
    PermissionResult result;
    result.has_permission = false;
    result.user_id = user_id;
    result.project_id = project_id;
    result.permission_name = permission_name;

    try {
        if (!isValidUUID(user_id) || !isValidUUID(project_id) || permission_name.empty()) {
            logError("validatePermission", "Invalid parameters");
            return result;
        }

        // Complex query to check user permissions through roles
        std::string query = R"(
            SELECT DISTINCT r.role_name
            FROM auth_user_project_roles upr
            JOIN auth_roles r ON upr.role_id = r.role_id
            JOIN auth_role_permissions rp ON r.role_id = rp.role_id
            JOIN auth_permissions p ON rp.permission_id = p.permission_id
            WHERE upr.user_id = $1
              AND upr.project_id = $2
              AND p.permission_name = $3
              AND upr.is_active = true
              AND (upr.expires_at IS NULL OR upr.expires_at > CURRENT_TIMESTAMP)
        )";

        std::vector<std::string> params = {user_id, project_id, permission_name};
        auto db_result = db_manager_->executeRBACQuery(query, params);

        if (db_result.has_value() && !db_result->empty()) {
            result.has_permission = true;

            // Collect roles that granted the permission
            for (const auto& row : *db_result) {
                result.roles.push_back(row["role_name"].as<std::string>());
            }
        }

        return result;
        
    } catch (const std::exception& e) {
        logError("validatePermission", e.what());
        return result;
    }
}

bool RBACManager::validateSystemPermission(const std::string& user_id,
                                         const std::string& permission_name) {
    try {
        if (!isValidUUID(user_id) || permission_name.empty()) {
            logError("validateSystemPermission", "Invalid parameters");
            return false;
        }

        // Check system-level permissions through system roles
        std::string query = R"(
            SELECT COUNT(*)
            FROM auth_user_project_roles upr
            JOIN auth_roles r ON upr.role_id = r.role_id
            JOIN auth_role_permissions rp ON r.role_id = rp.role_id
            JOIN auth_permissions p ON rp.permission_id = p.permission_id
            WHERE upr.user_id = $1
              AND p.permission_name = $2
              AND r.is_system_role = true
              AND upr.is_active = true
              AND (upr.expires_at IS NULL OR upr.expires_at > CURRENT_TIMESTAMP)
        )";

        std::vector<std::string> params = {user_id, permission_name};
        auto result = db_manager_->executeRBACQuery(query, params);

        if (result.has_value() && !result->empty()) {
            int count = (*result)[0][0].as<int>();
            return count > 0;
        }

        return false;
        
    } catch (const std::exception& e) {
        logError("validateSystemPermission", e.what());
        return false;
    }
}

// ============================================================================
// Helper Methods
// ============================================================================

bool RBACManager::isValidUUID(const std::string& uuid) {
    // Use database manager's UUID validation
    return db_manager_->isValidUUID(uuid);
}

std::string RBACManager::generateUUID() {
    // Use database manager's UUID generation
    return db_manager_->generateUUID();
}

void RBACManager::logError(const std::string& operation, const std::string& error) {
    std::cerr << "[RBACManager::" << operation << "] Error: " << error << std::endl;
}
