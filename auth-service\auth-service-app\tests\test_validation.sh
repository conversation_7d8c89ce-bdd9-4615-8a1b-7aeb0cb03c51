#!/bin/bash

# OAuth 2.0 Validation Test Script
# Tests the auth-service OAuth endpoints for proper functionality

echo "🧪 Testing Auth Service OAuth 2.0 Validation..."

# Configuration
AUTH_SERVICE_URL="http://localhost:8082"
HTTPS_URL="https://auth-dev.chcit.org"

# Test credentials
TEST_USER="testuser"
TEST_PASS="testpass123"
ADMIN_USER="btaylor-admin"
ADMIN_PASS="AdminPass123!"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
print_test() {
    echo -e "${BLUE}🔍 Testing: $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Test 1: Health Check
print_test "Health Check"
HEALTH_RESPONSE=$(curl -s "$AUTH_SERVICE_URL/health")
if echo "$HEALTH_RESPONSE" | grep -q "status"; then
    print_success "Health check passed"
    echo "Response: $HEALTH_RESPONSE"
else
    print_error "Health check failed"
    echo "Response: $HEALTH_RESPONSE"
fi
echo ""

# Test 2: Token Generation (Test User)
print_test "Token Generation - Test User"
TOKEN_RESPONSE=$(curl -s -X POST "$AUTH_SERVICE_URL/oauth/token" \
    -H "Content-Type: application/json" \
    -d "{\"username\":\"$TEST_USER\",\"password\":\"$TEST_PASS\",\"grant_type\":\"password\"}")

if echo "$TOKEN_RESPONSE" | grep -q "access_token"; then
    print_success "Token generation successful for test user"
    TEST_TOKEN=$(echo "$TOKEN_RESPONSE" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
    echo "Token: ${TEST_TOKEN:0:50}..."
else
    print_error "Token generation failed for test user"
    echo "Response: $TOKEN_RESPONSE"
fi
echo ""

# Test 3: Token Generation (Admin User)
print_test "Token Generation - Admin User"
ADMIN_TOKEN_RESPONSE=$(curl -s -X POST "$AUTH_SERVICE_URL/oauth/token" \
    -H "Content-Type: application/json" \
    -d "{\"username\":\"$ADMIN_USER\",\"password\":\"$ADMIN_PASS\",\"grant_type\":\"password\"}")

if echo "$ADMIN_TOKEN_RESPONSE" | grep -q "access_token"; then
    print_success "Token generation successful for admin user"
    ADMIN_TOKEN=$(echo "$ADMIN_TOKEN_RESPONSE" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
    echo "Token: ${ADMIN_TOKEN:0:50}..."
else
    print_error "Token generation failed for admin user"
    echo "Response: $ADMIN_TOKEN_RESPONSE"
fi
echo ""

# Test 4: Token Validation
if [ ! -z "$TEST_TOKEN" ]; then
    print_test "Token Validation"
    VALIDATION_RESPONSE=$(curl -s -X POST "$AUTH_SERVICE_URL/oauth/validate" \
        -H "Content-Type: application/json" \
        -d "{\"token\":\"$TEST_TOKEN\"}")
    
    if echo "$VALIDATION_RESPONSE" | grep -q "valid"; then
        print_success "Token validation successful"
        echo "Response: $VALIDATION_RESPONSE"
    else
        print_error "Token validation failed"
        echo "Response: $VALIDATION_RESPONSE"
    fi
    echo ""
fi

# Test 5: Token Refresh (if refresh token available)
if echo "$TOKEN_RESPONSE" | grep -q "refresh_token"; then
    print_test "Token Refresh"
    REFRESH_TOKEN=$(echo "$TOKEN_RESPONSE" | grep -o '"refresh_token":"[^"]*"' | cut -d'"' -f4)
    
    REFRESH_RESPONSE=$(curl -s -X POST "$AUTH_SERVICE_URL/oauth/refresh" \
        -H "Content-Type: application/json" \
        -d "{\"refresh_token\":\"$REFRESH_TOKEN\"}")
    
    if echo "$REFRESH_RESPONSE" | grep -q "access_token"; then
        print_success "Token refresh successful"
        echo "New token generated"
    else
        print_error "Token refresh failed"
        echo "Response: $REFRESH_RESPONSE"
    fi
    echo ""
fi

# Test 6: Token Revocation
if [ ! -z "$TEST_TOKEN" ]; then
    print_test "Token Revocation"
    REVOKE_RESPONSE=$(curl -s -X POST "$AUTH_SERVICE_URL/oauth/revoke" \
        -H "Content-Type: application/json" \
        -d "{\"token\":\"$TEST_TOKEN\"}")
    
    if echo "$REVOKE_RESPONSE" | grep -q "revoked\|success" || [ -z "$REVOKE_RESPONSE" ]; then
        print_success "Token revocation successful"
    else
        print_error "Token revocation failed"
        echo "Response: $REVOKE_RESPONSE"
    fi
    echo ""
fi

# Test 7: HTTPS Endpoint Test
print_test "HTTPS Endpoint Test"
HTTPS_HEALTH=$(curl -s -k "$HTTPS_URL/health")
if echo "$HTTPS_HEALTH" | grep -q "status"; then
    print_success "HTTPS endpoint accessible"
    echo "HTTPS health check passed"
else
    print_warning "HTTPS endpoint may not be accessible"
    echo "Response: $HTTPS_HEALTH"
fi
echo ""

# Test 8: Invalid Credentials Test
print_test "Invalid Credentials Test"
INVALID_RESPONSE=$(curl -s -X POST "$AUTH_SERVICE_URL/oauth/token" \
    -H "Content-Type: application/json" \
    -d "{\"username\":\"invalid\",\"password\":\"invalid\",\"grant_type\":\"password\"}")

if echo "$INVALID_RESPONSE" | grep -q "error\|invalid"; then
    print_success "Invalid credentials properly rejected"
else
    print_error "Invalid credentials test failed - should have been rejected"
    echo "Response: $INVALID_RESPONSE"
fi
echo ""

# Summary
echo "🏁 Test Summary:"
echo "=================="
echo "✅ Tests completed"
echo "📊 Check individual test results above"
echo "🔧 If any tests failed, check:"
echo "   - Auth service is running on port 8082"
echo "   - Database is accessible"
echo "   - User credentials are correct"
echo "   - Network connectivity"
