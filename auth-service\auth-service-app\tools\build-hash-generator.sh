#!/bin/bash

# Build script for password hash generator utility
# This utility helps generate password hashes for the auth-service database

echo "🔨 Building Password Hash Generator..."

# Check if we're in the right directory
if [ ! -f "hash-generator.cpp" ]; then
    echo "❌ Error: hash-generator.cpp not found in current directory"
    echo "Please run this script from the tools directory"
    exit 1
fi

# Check for required dependencies
echo "📦 Checking dependencies..."

# Check for g++
if ! command -v g++ &> /dev/null; then
    echo "❌ Error: g++ compiler not found"
    echo "Please install g++ (build-essential package on Ubuntu/Debian)"
    exit 1
fi

# Check for Argon2 development headers
if ! pkg-config --exists libargon2; then
    echo "❌ Error: Argon2 development headers not found"
    echo "Please install libargon2-dev package:"
    echo "  sudo apt-get install libargon2-dev"
    exit 1
fi

# Build the hash generator
echo "🔧 Compiling hash generator..."
g++ -std=c++17 -o hash-generator hash-generator.cpp -largon2

if [ $? -eq 0 ]; then
    echo "✅ Hash generator built successfully!"
    echo ""
    echo "Usage:"
    echo "  ./hash-generator \"your_password_here\""
    echo ""
    echo "Example:"
    echo "  ./hash-generator \"testpass123\""
    echo "  ./hash-generator \"AdminPass123!\""
    echo ""
    echo "The tool will output:"
    echo "  - Generated salt (hex)"
    echo "  - Argon2id password hash"
    echo "  - SQL update command"
else
    echo "❌ Build failed!"
    exit 1
fi
