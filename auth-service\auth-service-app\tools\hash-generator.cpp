#include <iostream>
#include <string>
#include <random>
#include <iomanip>
#include <sstream>
#include <openssl/sha.h>

class PasswordHasher {
private:
    std::string generateSalt(size_t length = 32) {
        const std::string chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(0, chars.size() - 1);
        
        std::string salt;
        for (size_t i = 0; i < length; ++i) {
            salt += chars[dis(gen)];
        }
        return salt;
    }
    
    std::string sha256(const std::string& input) {
        unsigned char hash[SHA256_DIGEST_LENGTH];
        SHA256_CTX sha256;
        SHA256_Init(&sha256);
        SHA256_Update(&sha256, input.c_str(), input.length());
        SHA256_Final(hash, &sha256);
        
        std::stringstream ss;
        for (int i = 0; i < SHA256_DIGEST_LENGTH; i++) {
            ss << std::hex << std::setw(2) << std::setfill('0') << (int)hash[i];
        }
        return ss.str();
    }

public:
    struct HashResult {
        std::string hash;
        std::string salt;
    };
    
    HashResult hashPassword(const std::string& password) {
        std::string salt = generateSalt();
        std::string saltedPassword = password + salt;
        std::string hash = sha256(saltedPassword);
        
        return {hash, salt};
    }
    
    bool verifyPassword(const std::string& password, const std::string& hash, const std::string& salt) {
        std::string saltedPassword = password + salt;
        std::string computedHash = sha256(saltedPassword);
        return computedHash == hash;
    }
};

int main(int argc, char* argv[]) {
    if (argc != 2) {
        std::cerr << "Usage: " << argv[0] << " <password>" << std::endl;
        std::cerr << "Example: " << argv[0] << " \"mypassword123\"" << std::endl;
        return 1;
    }
    
    std::string password = argv[1];
    PasswordHasher hasher;
    
    auto result = hasher.hashPassword(password);
    
    std::cout << "Password: " << password << std::endl;
    std::cout << "Salt: " << result.salt << std::endl;
    std::cout << "Hash: " << result.hash << std::endl;
    std::cout << std::endl;
    std::cout << "SQL Update Commands:" << std::endl;
    std::cout << "UPDATE auth_users SET password_hash = '" << result.hash 
              << "', salt = '" << result.salt << "' WHERE username = 'your_username';" << std::endl;
    
    // Verify the hash works
    if (hasher.verifyPassword(password, result.hash, result.salt)) {
        std::cout << std::endl << "✅ Hash verification successful!" << std::endl;
    } else {
        std::cout << std::endl << "❌ Hash verification failed!" << std::endl;
        return 1;
    }
    
    return 0;
}
