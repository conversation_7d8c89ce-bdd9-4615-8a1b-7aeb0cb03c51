#include <iostream>
#include <string>
#include <random>
#include <iomanip>
#include <sstream>
#include <argon2.h>
#include <cstring>

class PasswordHasher {
private:
    // Argon2id parameters matching auth-service configuration
    static constexpr uint32_t MEMORY_COST = 65536;  // 64 MB
    static constexpr uint32_t TIME_COST = 3;
    static constexpr uint32_t PARALLELISM = 4;
    static constexpr uint32_t SALT_LENGTH = 32;
    static constexpr uint32_t HASH_LENGTH = 64;

    std::string generateSalt(size_t length = SALT_LENGTH) {
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<uint8_t> dis(0, 255);

        std::string salt;
        salt.reserve(length);
        for (size_t i = 0; i < length; ++i) {
            salt.push_back(static_cast<char>(dis(gen)));
        }
        return salt;
    }

    std::string bytesToHex(const uint8_t* bytes, size_t length) {
        std::stringstream ss;
        for (size_t i = 0; i < length; ++i) {
            ss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(bytes[i]);
        }
        return ss.str();
    }

    std::string argon2idHash(const std::string& password, const std::string& salt) {
        uint8_t hash[HASH_LENGTH];

        int result = argon2id_hash_raw(
            TIME_COST,
            MEMORY_COST,
            PARALLELISM,
            password.c_str(),
            password.length(),
            salt.c_str(),
            salt.length(),
            hash,
            HASH_LENGTH
        );

        if (result != ARGON2_OK) {
            throw std::runtime_error("Argon2id hashing failed: " + std::string(argon2_error_message(result)));
        }

        // Format as Argon2id encoded string
        char encoded[256];
        result = argon2id_hash_encoded(
            TIME_COST,
            MEMORY_COST,
            PARALLELISM,
            password.c_str(),
            password.length(),
            salt.c_str(),
            salt.length(),
            HASH_LENGTH,
            encoded,
            sizeof(encoded)
        );

        if (result != ARGON2_OK) {
            throw std::runtime_error("Argon2id encoding failed: " + std::string(argon2_error_message(result)));
        }

        return std::string(encoded);
    }

public:
    struct HashResult {
        std::string hash;
        std::string salt;
    };

    HashResult hashPassword(const std::string& password) {
        std::string salt = generateSalt();
        std::string hash = argon2idHash(password, salt);

        return {hash, bytesToHex(reinterpret_cast<const uint8_t*>(salt.c_str()), salt.length())};
    }

    bool verifyPassword(const std::string& password, const std::string& hash, const std::string& saltHex) {
        try {
            // Convert hex salt back to bytes
            std::string salt;
            for (size_t i = 0; i < saltHex.length(); i += 2) {
                std::string byteString = saltHex.substr(i, 2);
                uint8_t byte = static_cast<uint8_t>(std::stoi(byteString, nullptr, 16));
                salt.push_back(static_cast<char>(byte));
            }

            std::string computedHash = argon2idHash(password, salt);
            return computedHash == hash;
        } catch (const std::exception& e) {
            return false;
        }
    }
};

int main(int argc, char* argv[]) {
    if (argc != 2) {
        std::cerr << "Usage: " << argv[0] << " <password>" << std::endl;
        std::cerr << "Example: " << argv[0] << " \"mypassword123\"" << std::endl;
        return 1;
    }
    
    std::string password = argv[1];
    PasswordHasher hasher;
    
    auto result = hasher.hashPassword(password);
    
    std::cout << "Password: " << password << std::endl;
    std::cout << "Salt (hex): " << result.salt << std::endl;
    std::cout << "Argon2id Hash: " << result.hash << std::endl;
    std::cout << std::endl;
    std::cout << "SQL Update Commands:" << std::endl;
    std::cout << "UPDATE auth_users SET password_hash = '" << result.hash
              << "', salt = '" << result.salt << "' WHERE username = 'your_username';" << std::endl;
    
    // Verify the hash works
    if (hasher.verifyPassword(password, result.hash, result.salt)) {
        std::cout << std::endl << "✅ Hash verification successful!" << std::endl;
    } else {
        std::cout << std::endl << "❌ Hash verification failed!" << std::endl;
        return 1;
    }
    
    return 0;
}
