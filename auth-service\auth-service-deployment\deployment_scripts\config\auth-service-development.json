{"environment": "development", "ssh": {"port": 22, "host": "auth-dev.chcit.org", "ip": "***********", "username": "btaylor-admin", "local_key_path": "C:\\Users\\<USER>\\.ssh\\id_rsa"}, "service": {"description": "auth-service for development environment", "name": "auth-service", "user": "auth-service", "group": "auth-service"}, "version": {"created": "2025-06-24 16:15:27", "updated": "2025-06-24 16:15:27", "number": 2}, "database": {"port": 5432, "host": "***********", "name": "auth_service", "password": "VOUGaH&Lr-p6#(oB1r$JoGXk", "user": "auth_service"}, "project": {"remote_install_dir": "/opt/auth-service", "local_source_dir": "D:\\Coding_Projects\\auth-service\\auth-service-app", "name": "auth-service", "description": "auth-service for development environment", "remote_build_dir": "/home/<USER>/auth-service-build"}}