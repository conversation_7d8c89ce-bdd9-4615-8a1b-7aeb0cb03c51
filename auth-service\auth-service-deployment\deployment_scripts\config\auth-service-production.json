{"paths": {"staging_dir": "/home/<USER>/auth-service-staging", "ui_staging_dir": "/home/<USER>/auth-service-ui-staging", "build_dir": "/home/<USER>/auth-service-build", "ui_build_dir": "/home/<USER>/auth-service-ui-build"}, "service": {"port": 8082, "name": "auth-service", "group": "auth-service", "description": "Authentication Service for git.chcit.org", "install_dir": "/opt/auth-service", "user": "auth-service"}, "environment": "backend-production", "ssh": {"username": "btaylor-admin", "host": "authbe.chcit.org", "port": 22, "local_key_path": "C:\\Users\\<USER>\\.ssh\\id_rsa"}, "database": {"port": 5432, "host": "localhost", "name": "auth_service", "password": "aGwO4!^SfvWz!fAqXAvsk9_V", "username": "auth_service"}, "ui": {"install_dir": "/opt/auth-service-ui", "port": 3000}, "project": {"remote_build_dir": "/home/<USER>/auth-service-build", "description": "auth-service for production environment", "name": "auth-service", "remote_install_dir": "/opt/auth-service", "local_source_dir": "D:\\Coding_Projects\\auth-service\\auth-service-app"}}