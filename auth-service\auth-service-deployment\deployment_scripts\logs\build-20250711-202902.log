Running CMake configuration...
-- The CXX compiler identification is GNU 14.2.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/g++-14 - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found PkgConfig: /usr/bin/pkg-config (found version "1.8.1") 
-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfig.cmake (found version "1.83.0") found components: system program_options 
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")  
-- Checking for module 'libpqxx'
--   Found libpqxx, version 7.8.1
-- Found nlohmann_json: /usr/share/cmake/nlohmann_json/nlohmann_jsonConfig.cmake (found version "3.11.3") 
-- === Auth Service Build Configuration ===
-- Build Type: Release
-- C++ Standard: 23
-- Boost Version: 1.83.0
-- OpenSSL Version: 3.0.13
-- PostgreSQL (libpqxx): 7.8.1
-- nlohmann_json: Found
-- Argon2 Library: /usr/lib/x86_64-linux-gnu/libargon2.so
-- === Installation Paths ===
-- Binary: /opt/auth-service/bin/auth-service
-- Config: /opt/auth-service/config/
-- Database: /opt/auth-service/database/
-- Logs: /opt/auth-service/logs/
-- === OAuth 2.0 Features ===
-- ✅ Step 1: Database Schema - Ready
-- ✅ Step 2: Configuration System - Ready
-- ✅ Step 3: Argon2id Password Security - Ready
-- ✅ Step 4: JWT Token Management - Implemented
-- 🎯 Next: HTTP API Endpoints and Integration Testing
-- ==========================================
-- Configuring done (0.3s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/auth-service-build/build
Starting compilation...
[ 11%] Building CXX object CMakeFiles/auth-service.dir/src/main.cpp.o
[ 22%] Building CXX object CMakeFiles/auth-service.dir/src/auth_service.cpp.o
[ 33%] Building CXX object CMakeFiles/auth-service.dir/src/database_manager.cpp.o
[ 44%] Building CXX object CMakeFiles/auth-service.dir/src/security_manager.cpp.o
[ 55%] Building CXX object CMakeFiles/auth-service.dir/src/config_manager.cpp.o
[ 66%] Building CXX object CMakeFiles/auth-service.dir/src/jwt_manager.cpp.o
[ 77%] Building CXX object CMakeFiles/auth-service.dir/src/http_server.cpp.o
[ 88%] Building CXX object CMakeFiles/auth-service.dir/src/user_manager.cpp.o
[100%] Linking CXX executable auth-service
[100%] Built target auth-service
