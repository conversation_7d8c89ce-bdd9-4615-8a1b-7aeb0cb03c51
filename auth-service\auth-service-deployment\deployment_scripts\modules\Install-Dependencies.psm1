# Install-Dependencies-Clean.psm1 - Clean module for installing dependencies

<#
.SYNOPSIS
    Provides functionality for installing system dependencies.

.DESCRIPTION
    This module handles the installation of system dependencies required
    specifically for the auth-service C++23 application.

.NOTES
    File Name      : Install-Dependencies-Clean.psm1
    Author         : Auth Service Deployment Team
    Prerequisite   : PowerShell 5.1 or later
    Copyright      : (c) 2025 Augment
#>

# Helper function to get environment from hostname
function Get-EnvironmentFromHostname {
    param([string]$Hostname)

    switch -Regex ($Hostname) {
        "auth-dev" { return "auth-dev" }
        "authbe" { return "authbe" }
        "authfe" { return "authfe" }
        default { return $null }
    }
}

# Helper function to load requirements for environment
function Get-RequirementsForEnvironment {
    param([string]$Environment)

    if (-not $Environment) { return $null }

    $requirementsFile = Join-Path $PSScriptRoot "..\requirements\$Environment-requirements.json"
    if (Test-Path $requirementsFile) {
        try {
            return Get-Content $requirementsFile -Raw | ConvertFrom-Json
        } catch {
            Write-Warning "Failed to load requirements from $requirementsFile`: $_"
            return $null
        }
    }
    return $null
}

# Function to install dependencies from requirements JSON
function Install-DependenciesFromRequirements {
    param([PSCustomObject]$Requirements)

    if (-not $Requirements) {
        Write-Host "❌ No requirements provided" -ForegroundColor Red
        return $false
    }

    Write-Host "📋 Installing dependencies based on requirements for: $($Requirements.metadata.environment)" -ForegroundColor Cyan
    Write-Host "Target Server: $($Requirements.metadata.target_server)" -ForegroundColor Yellow
    Write-Host ""

    $success = $true

    # Install system dependencies by category
    foreach ($category in $Requirements.system_dependencies.PSObject.Properties) {
        Write-Host "Installing $($category.Name)..." -ForegroundColor Yellow

        foreach ($dependency in $category.Value) {
            Write-Host "  Installing $($dependency.name) (min version: $($dependency.version_minimum))" -ForegroundColor White

            # Check if already installed (use verification field if available, otherwise try version_command)
            $versionCheck = if ($dependency.verification) { $dependency.verification } else { $dependency.version_command }
            if ($versionCheck) {
                $checkCmd = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$versionCheck 2>/dev/null || echo 'NOT_INSTALLED'`""
                $checkResult = Invoke-Expression $checkCmd
            } else {
                $checkResult = "NOT_INSTALLED"
            }

            if ($checkResult -notlike "*NOT_INSTALLED*") {
                Write-Host "    ✅ Already installed: $checkResult" -ForegroundColor Green
            } else {
                # Install the dependency (use command field from JSON)
                $installCommand = if ($dependency.install_command) { $dependency.install_command } else { $dependency.command }
                if ($installCommand) {
                    # Add non-interactive environment variable to prevent debconf warnings
                    $nonInteractiveCommand = "DEBIAN_FRONTEND=noninteractive $installCommand"
                    $installCmd = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$nonInteractiveCommand`""
                    Write-Host "    Executing: $nonInteractiveCommand" -ForegroundColor Gray
                    $installResult = Invoke-Expression $installCmd

                    if ($LASTEXITCODE -eq 0) {
                        Write-Host "    ✅ Successfully installed: $($dependency.name)" -ForegroundColor Green
                    } else {
                        Write-Host "    ❌ Failed to install: $($dependency.name)" -ForegroundColor Red
                        Write-Host "    Error output: $installResult" -ForegroundColor Gray
                        $success = $false
                    }
                } else {
                    Write-Host "    ⚠️ No install command found for: $($dependency.name)" -ForegroundColor Yellow
                    $success = $false
                }
            }
        }
        Write-Host ""
    }

    return $success
}

function Install-Dependencies {
    [CmdletBinding()]
    param()

    Write-Host "=== Installing System Dependencies ===" -ForegroundColor Cyan
    Write-Host ""

    # Get configuration from multiple sources
    $config = $null
    if (Get-Command -Name Get-Configuration -ErrorAction SilentlyContinue) {
        $config = Get-Configuration
    } elseif ($Global:Config) {
        $config = $Global:Config
    } elseif ($Global:CurrentConfig) {
        $config = $Global:CurrentConfig
    } elseif ($script:Config) {
        $config = $script:Config
    }

    # Load auth-service configuration
    if ($null -eq $config) {
        Write-Host "❌ Configuration is not loaded. Please set environment first (Menu Option 1)." -ForegroundColor Red
        Write-Host "💡 No configuration available from any source." -ForegroundColor Yellow
        return $false
    }

    # Use the found configuration
    $script:Config = $config
    Write-Host "✅ Using loaded configuration" -ForegroundColor Green

    # Determine environment and load requirements
    $environment = Get-EnvironmentFromHostname -Hostname $script:Config.ssh.host
    $requirements = Get-RequirementsForEnvironment -Environment $environment

    if ($requirements) {
        Write-Host "🎯 Using requirements-based installation for environment: $environment" -ForegroundColor Green
        Write-Host "Requirements file: $environment-requirements.json" -ForegroundColor Cyan
        Write-Host ""

        # Use requirements-based installation
        $requirementsSuccess = Install-DependenciesFromRequirements -Requirements $requirements

        if (-not $requirementsSuccess) {
            Write-Host "⚠️ Requirements-based installation had issues, continuing with fallback..." -ForegroundColor Yellow
        }
    } else {
        Write-Host "⚠️ No requirements file found for environment: $environment" -ForegroundColor Yellow
        Write-Host "Falling back to generic dependency installation..." -ForegroundColor Yellow
        Write-Host ""
    }

    Write-Host "=== Installing Auth-Service Dependencies ===" -ForegroundColor Cyan
    Write-Host ""

    # Auth-service specific settings
    $serviceName = "auth-service"
    $installDir = "/opt/auth-service"

    Write-Host "Target Host: $($script:Config.ssh.host)" -ForegroundColor Yellow
    Write-Host "Service: $serviceName" -ForegroundColor Yellow
    Write-Host "Install Directory: $installDir" -ForegroundColor Yellow
    Write-Host "Database: auth_service" -ForegroundColor Yellow
    Write-Host ""

    # Check if this is a certificate-related environment using requirements
    $needsCertificateSetup = $false
    if ($requirements -and $requirements.ssl_certificates -and $requirements.ssl_certificates.required) {
        $needsCertificateSetup = $true
        Write-Host "🔒 Certificate setup will be included for auth-service environment" -ForegroundColor Cyan
        Write-Host "SSL Domain: $($requirements.ssl_certificates.full_domain)" -ForegroundColor Cyan
        Write-Host ""
    } elseif ($script:Config.ssh.host -match "auth-dev|authbe|authfe") {
        # Fallback for environments without requirements
        $needsCertificateSetup = $true
        Write-Host "🔒 Certificate setup will be included (fallback detection)" -ForegroundColor Cyan
        Write-Host ""
    }

    # Determine environment-specific requirements file
    $environment = if ($script:Config -and $script:Config.PSObject.Properties['environment']) {
        $script:Config.environment
    } elseif ($env:DeploymentEnvironment) {
        $env:DeploymentEnvironment
    } elseif ($Global:Environment) {
        $Global:Environment
    } elseif ($script:Config -and $script:Config.ssh -and $script:Config.ssh.host) {
        # Derive environment from hostname if not explicitly set
        if ($script:Config.ssh.host -match "auth-dev|dev\.chcit\.org") { "development" }
        elseif ($script:Config.ssh.host -match "authbe") { "backend-production" }
        elseif ($script:Config.ssh.host -match "authfe") { "frontend-production" }
        else { "development" }  # Default fallback
    } else {
        "development"  # Default fallback
    }

    # Map environment to requirements file
    $requirementsFileName = switch ($environment) {
        "development" { "auth-dev-requirements.json" }
        "backend-production" { "authbe-requirements.json" }
        "frontend-production" { "authfe-requirements.json" }
        default { "auth-dev-requirements.json" }  # Default fallback
    }

    # Load environment-specific requirements
    $requirementsPath = Join-Path $PSScriptRoot "..\requirements\$requirementsFileName"
    if (-not (Test-Path $requirementsPath)) {
        Write-Host "❌ Requirements file not found: $requirementsPath" -ForegroundColor Red
        Write-Host "💡 Expected environment: $environment, file: $requirementsFileName" -ForegroundColor Gray
        return $false
    }

    try {
        $requirements = Get-Content $requirementsPath | ConvertFrom-Json
        Write-Host "✅ Using requirements file: $requirementsFileName (Environment: $environment)" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to parse requirements file: $_" -ForegroundColor Red
        return $false
    }

    # Combine system dependency categories (excluding database dependencies)
    $allDependencies = @()

    if ($requirements.system_dependencies.essential_tools) {
        $allDependencies += $requirements.system_dependencies.essential_tools
    }
    if ($requirements.system_dependencies.cpp_libraries) {
        $allDependencies += $requirements.system_dependencies.cpp_libraries
    }
    if ($requirements.system_dependencies.security_libraries) {
        $allDependencies += $requirements.system_dependencies.security_libraries
    }
    if ($requirements.system_dependencies.http_libraries) {
        $allDependencies += $requirements.system_dependencies.http_libraries
    }

    # Add cache dependencies (Valkey client tools)
    if ($requirements.cache_requirements -and $requirements.cache_requirements.valkey) {
        $valkeyDep = @{
            name = "Valkey Client Tools"
            package = "redis-tools"
            command = $requirements.cache_requirements.valkey.install_command
            description = $requirements.cache_requirements.valkey.description
            verification = $requirements.cache_requirements.valkey.verification
        }
        $allDependencies += $valkeyDep
    }

    # Note: Database dependencies are handled by Menu Option 11 (Install-Database-Dependencies)

    $dependencies = $allDependencies

    if (-not $dependencies) {
        Write-Host "❌ No dependencies found in configuration" -ForegroundColor Red
        return $false
    }

    Write-Host "Found $($dependencies.Count) system dependencies:" -ForegroundColor Green
    Write-Host ""

    # List all dependencies for review
    Write-Host "📦 Dependencies to be installed:" -ForegroundColor Cyan
    Write-Host "================================" -ForegroundColor Cyan
    Write-Host ""

    $index = 1
    foreach ($dep in $dependencies) {
        $depName = if ($dep.name) { $dep.name } else { "Unknown Package" }
        $depDesc = if ($dep.description) { $dep.description } else { "No description available" }
        Write-Host "   $index. $depName" -ForegroundColor White
        Write-Host "      $depDesc" -ForegroundColor Gray
        Write-Host ""
        $index++
    }

    Write-Host ""
    Write-Host "Note: Database dependencies (PostgreSQL) are installed separately via Menu Option 11" -ForegroundColor Yellow
    Write-Host "Note: Cache dependencies (Valkey client tools) are included in this installation" -ForegroundColor Cyan
    Write-Host ""

    # Ask for confirmation
    $confirmation = Read-Host "Do you want to proceed with installing these dependencies? (y/N)"
    if ($confirmation -ne "y" -and $confirmation -ne "Y") {
        Write-Host "Installation cancelled by user." -ForegroundColor Yellow
        return $false
    }

    Write-Host ""
    Write-Host "Proceeding with installation..." -ForegroundColor Green
    Write-Host ""

    # Update package list first
    Write-Host "Updating package list..." -ForegroundColor Yellow

    # Define the package update command with non-interactive frontend
    $updateCmd = "export DEBIAN_FRONTEND=noninteractive && sudo apt-get update -y"

    # Check if user has sudo access first
    Write-Host "Checking sudo access..." -ForegroundColor Yellow
    # Simplified approach: Test SSH connection and proceed
    try {
        # Use configuration values for SSH connection test
        $sshHost = $script:Config.ssh.host
        $sshUser = $script:Config.ssh.username
        $sshKeyPath = $script:Config.ssh.local_key_path
        $sshPort = $script:Config.ssh.port

        Write-Host "Testing SSH connection to $sshHost as $sshUser..." -ForegroundColor Yellow
        $connectionTest = Test-SSHConnection -HostName $sshHost -User $sshUser -KeyPath $sshKeyPath -Port $sshPort
        if ($connectionTest) {
            Write-Host "✅ SSH connection verified" -ForegroundColor Green
        } else {
            Write-Host "❌ SSH connection failed" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ SSH connection error: $_" -ForegroundColor Red
        return $false
    }
    try {
        Write-Host "Executing package update (this may take a few minutes)..." -ForegroundColor Yellow

        $sshCommand = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no -o ConnectTimeout=30 $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$updateCmd`""

        # Use Start-Process with timeout instead of Invoke-Expression
        $processInfo = New-Object System.Diagnostics.ProcessStartInfo
        $processInfo.FileName = "ssh"
        $processInfo.Arguments = "-i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no -o ConnectTimeout=30 $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$updateCmd`""
        $processInfo.RedirectStandardOutput = $true
        $processInfo.RedirectStandardError = $true
        $processInfo.UseShellExecute = $false
        $processInfo.CreateNoWindow = $true

        $process = New-Object System.Diagnostics.Process
        $process.StartInfo = $processInfo
        $process.Start() | Out-Null

        # Wait for process with timeout (5 minutes)
        $timeoutMs = 300000  # 5 minutes
        if ($process.WaitForExit($timeoutMs)) {
            $stdout = $process.StandardOutput.ReadToEnd()
            $stderr = $process.StandardError.ReadToEnd()
            $exitCode = $process.ExitCode

            if ($exitCode -eq 0) {
                Write-Host "✅ Package list updated successfully" -ForegroundColor Green
            } else {
                Write-Host "❌ Failed to update package list (exit code: $exitCode)" -ForegroundColor Red
                return $false
            }
        } else {
            Write-Host "❌ Package update timed out after 5 minutes" -ForegroundColor Red
            $process.Kill()
            return $false
        }
    } catch {
        Write-Host "❌ Error updating package list: $_" -ForegroundColor Red
        return $false
    }

    Write-Host ""

    # Install each dependency
    $successCount = 0
    $totalCount = $dependencies.Count

    foreach ($dependency in $dependencies) {
        $depName = if ($dependency.name) { $dependency.name } else { "Unknown Package" }
        $depCommand = if ($dependency.command) {
            $dependency.command
        } elseif ($dependency.install_command) {
            $dependency.install_command
        } else {
            ""
        }

        Write-Host ""
        Write-Host "📦 Installing: $depName" -ForegroundColor Yellow

        # Skip if command is empty or null
        if ([string]::IsNullOrWhiteSpace($depCommand)) {
            Write-Host "  ⚠️ Skipping $depName - no command specified" -ForegroundColor Yellow
            continue
        }

        # Add non-interactive frontend to the command if it contains apt-get
        if ($depCommand -like "*apt-get*") {
            $depCommand = "export DEBIAN_FRONTEND=noninteractive && $depCommand"
        }

        try {
            $sshCommand = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$depCommand`""
            $result = Invoke-Expression $sshCommand
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "  ✅ $depName installed successfully" -ForegroundColor Green
                $successCount++
            } else {
                Write-Host "  ❌ Failed to install $depName" -ForegroundColor Red
                Write-Host "  Output: $result" -ForegroundColor Gray
            }
        } catch {
            Write-Host "  ❌ Error installing $depName`: $_" -ForegroundColor Red
        }

        Write-Host ""
    }

    # Summary
    Write-Host ""
    Write-Host "📊 Installation Summary" -ForegroundColor Cyan
    Write-Host "=======================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "   Total Dependencies: $totalCount" -ForegroundColor White
    Write-Host "   Successfully Installed: $successCount" -ForegroundColor Green
    Write-Host "   Failed: $($totalCount - $successCount)" -ForegroundColor $(if ($successCount -eq $totalCount) { "Green" } else { "Red" })
    Write-Host ""

    if ($successCount -eq $totalCount) {
        Write-Host "🎉 All dependencies installed successfully!" -ForegroundColor Green
        
        # Create service user and directories
        Write-Host ""
        Write-Host "Creating service user and directories..." -ForegroundColor Yellow
        
        $setupCmd = "sudo useradd -r -s /bin/false $serviceName 2>/dev/null || echo 'User already exists'; sudo mkdir -p $installDir/bin $installDir/config $installDir/logs $installDir/sql; sudo chown -R $serviceName`:$serviceName $installDir; sudo chmod -R 775 $installDir"

        try {
            $sshCommand = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$setupCmd`""
            $result = Invoke-Expression $sshCommand
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ Service user and directories created" -ForegroundColor Green
            } else {
                Write-Host "❌ Failed to create service user and directories" -ForegroundColor Red
            }
        } catch {
            Write-Host "❌ Error creating service setup: $_" -ForegroundColor Red
        }

        # Certificate setup for auth-service environments
        if ($needsCertificateSetup) {
            Write-Host ""
            Write-Host "🔒 Setting up SSL certificate access..." -ForegroundColor Cyan

            # Upload certificate setup script
            $localScriptPath = Join-Path $PSScriptRoot "..\scripts\setup-certificate-access.sh"
            if (Test-Path $localScriptPath) {
                try {
                    # Create scripts directory on remote server
                    $createDirCmd = "sudo mkdir -p /opt/auth-service/scripts"
                    $sshCommand = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$createDirCmd`""
                    Invoke-Expression $sshCommand | Out-Null

                    # Copy script to remote server
                    $scpCommand = "scp -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no `"$localScriptPath`" $($script:Config.ssh.username)@$($script:Config.ssh.host):/tmp/setup-certificate-access.sh"
                    $result = Invoke-Expression $scpCommand

                    if ($LASTEXITCODE -eq 0) {
                        # Move script to final location and make executable
                        $moveCmd = "sudo mv /tmp/setup-certificate-access.sh /opt/auth-service/scripts/ && sudo chmod +x /opt/auth-service/scripts/setup-certificate-access.sh"
                        $sshCommand = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$moveCmd`""
                        Invoke-Expression $sshCommand | Out-Null

                        # Run certificate setup script
                        Write-Host "  Running certificate access setup..." -ForegroundColor Yellow
                        $certSetupCmd = "sudo /opt/auth-service/scripts/setup-certificate-access.sh"
                        $sshCommand = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$certSetupCmd`""
                        $certResult = Invoke-Expression $sshCommand

                        if ($LASTEXITCODE -eq 0) {
                            Write-Host "  ✅ Certificate access setup completed" -ForegroundColor Green
                        } else {
                            Write-Host "  ⚠️ Certificate setup completed with warnings" -ForegroundColor Yellow
                            Write-Host "  Output: $certResult" -ForegroundColor Gray
                        }
                    } else {
                        Write-Host "  ❌ Failed to upload certificate setup script" -ForegroundColor Red
                    }
                } catch {
                    Write-Host "  ❌ Error during certificate setup: $_" -ForegroundColor Red
                }
            } else {
                Write-Host "  ⚠️ Certificate setup script not found: $localScriptPath" -ForegroundColor Yellow
            }

            # Setup cron job for certificate sync
            Write-Host ""
            Write-Host "⏰ Setting up certificate sync cron job..." -ForegroundColor Cyan

            # Note: Certificate sync cron job is now managed separately
            # Use SSL-Sync User Management menu (Option 26) for cron job setup
            Write-Host "  💡 Certificate sync cron job can be configured via SSL-Sync User Management menu" -ForegroundColor Cyan
        }

        Write-Host ""
        Write-Host "Press Enter to continue..." -ForegroundColor Yellow
        Read-Host | Out-Null
        return $true
    } else {
        Write-Host ""
        Write-Host "❌ Some dependencies failed to install. Please check the errors above." -ForegroundColor Red
        Write-Host ""
        Write-Host "Press Enter to continue..." -ForegroundColor Yellow
        Read-Host | Out-Null
        return $false
    }
}

function Install-CacheDependencies {
    <#
    .SYNOPSIS
    Installs Valkey cache server and client tools

    .DESCRIPTION
    Installs and configures Valkey server for caching based on environment requirements
    #>

    Write-Host "=== Installing Cache Dependencies (Valkey) ===" -ForegroundColor Cyan
    Write-Host ""

    # Check if configuration is loaded
    if ($null -eq $script:Config) {
        Write-Host "❌ Configuration not loaded" -ForegroundColor Red
        return $false
    }

    $targetHost = $script:Config.ssh.host
    $environment = Get-EnvironmentFromHostname -Hostname $targetHost
    $requirements = Get-RequirementsForEnvironment -Environment $environment

    if (-not $requirements -or -not $requirements.cache_requirements -or -not $requirements.cache_requirements.valkey) {
        Write-Host "❌ No Valkey cache requirements found for this environment" -ForegroundColor Red
        return $false
    }

    $valkeyConfig = $requirements.cache_requirements.valkey
    Write-Host "Target Host: $targetHost" -ForegroundColor Yellow
    Write-Host "Environment: $environment" -ForegroundColor Yellow
    Write-Host "Valkey Version: $($valkeyConfig.version_minimum)+" -ForegroundColor Yellow
    Write-Host ""

    # Install Valkey server and client tools
    Write-Host "📦 Installing Valkey server and client tools..." -ForegroundColor Cyan

    try {
        $installCmd = $valkeyConfig.install_command
        $sshCmd = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"$installCmd`""

        Write-Host "Executing: $installCmd" -ForegroundColor Gray
        $installResult = Invoke-Expression $sshCmd

        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Valkey installation completed successfully" -ForegroundColor Green

            # Start and enable Valkey service
            if ($valkeyConfig.service_name) {
                Write-Host ""
                Write-Host "🔄 Starting Valkey service..." -ForegroundColor Cyan
                $serviceCmd = "sudo systemctl enable $($valkeyConfig.service_name) && sudo systemctl start $($valkeyConfig.service_name)"
                $sshServiceCmd = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"$serviceCmd`""

                $serviceResult = Invoke-Expression $sshServiceCmd
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "✅ Valkey service started and enabled" -ForegroundColor Green
                } else {
                    Write-Host "⚠️ Valkey service start had issues" -ForegroundColor Yellow
                }
            }

            # Test connection
            Write-Host ""
            Write-Host "🔍 Testing Valkey connection..." -ForegroundColor Cyan
            $testCmd = $valkeyConfig.connection_test
            $sshTestCmd = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"$testCmd`""

            $testResult = Invoke-Expression $sshTestCmd
            if ($testResult -like "*PONG*") {
                Write-Host "✅ Valkey connection test successful: $testResult" -ForegroundColor Green
            } else {
                Write-Host "⚠️ Valkey connection test failed: $testResult" -ForegroundColor Yellow
            }

        } else {
            Write-Host "❌ Valkey installation failed" -ForegroundColor Red
            Write-Host "Output: $installResult" -ForegroundColor Gray
            return $false
        }

    } catch {
        Write-Host "❌ Error during Valkey installation: $_" -ForegroundColor Red
        return $false
    }

    Write-Host ""
    Write-Host "✅ Cache dependencies installation completed" -ForegroundColor Green
    return $true
}

# Export functions
Export-ModuleMember -Function Install-Dependencies, Install-CacheDependencies
