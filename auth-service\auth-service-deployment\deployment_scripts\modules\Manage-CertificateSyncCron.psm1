# Manage-CertificateSyncCron.psm1
# Module for managing certificate sync cron jobs using ssl-sync user

function Set-CertificateSyncConfig {
    <#
    .SYNOPSIS
    Sets the configuration for certificate sync operations

    .DESCRIPTION
    Accepts configuration from the parent module to enable certificate sync operations
    #>
    param(
        [object]$Config
    )

    $script:Config = $Config
}

function Install-CertificateSyncCronJob {
    <#
    .SYNOPSIS
    Installs certificate sync cron job for ssl-sync user

    .DESCRIPTI<PERSON>
    Deploys the certificate sync script and creates a cron job that runs every 6 hours
    to sync certificates from project-tracker using the ssl-sync user for secure certificate synchronization
    #>

    Write-Host "=== Installing Certificate Sync Cron Job ===" -ForegroundColor Cyan
    Write-Host ""

    # Check if configuration is loaded
    if ($null -eq $script:Config) {
        Write-Host "❌ Configuration not loaded" -ForegroundColor Red
        Write-Host "💡 Please ensure environment configuration is available" -ForegroundColor Yellow
        return
    }

    $targetHost = $script:Config.ssh.host
    Write-Host "Target Server: $targetHost" -ForegroundColor Cyan
    Write-Host ""

    # Step 1: Deploy certificate sync script
    Write-Host "📤 Deploying certificate sync script..." -ForegroundColor Yellow
    if (-not (Deploy-CertificateSyncScript)) {
        Write-Host "❌ Failed to deploy certificate sync script" -ForegroundColor Red
        return
    }

    # Step 2: Install cron job
    Write-Host "⏰ Installing cron job for ssl-sync user..." -ForegroundColor Yellow
    
    try {
        # Create cron job for certificate sync (every 6 hours) - runs as ssl-sync user
        # Determine environment from target host
        $environment = "development"  # Default
        if ($script:Config.ssh.host -match "authbe") { $environment = "backend-production" }
        elseif ($script:Config.ssh.host -match "authfe") { $environment = "frontend-production" }
        elseif ($script:Config.ssh.host -match "auth-dev") { $environment = "development" }

        $cronEntry = "0 */6 * * * /opt/auth-service/scripts/sync-auth-certificates.sh $environment >> /home/<USER>/logs/auth-cert-sync.log 2>&1"

        # Use a simpler approach - write to temp file then install
        $tempCronFile = "/tmp/ssl-sync-cron-$((Get-Date).Ticks)"
        $setupCronCmd = "sudo -u ssl-sync crontab -l 2>/dev/null | grep -v 'sync-auth-certificates' > $tempCronFile; echo '$cronEntry' >> $tempCronFile; sudo -u ssl-sync crontab $tempCronFile; rm -f $tempCronFile"

        # Use Start-Process instead of Invoke-Expression for better SSH handling
        $sshArgs = @(
            "-i", "`"$($script:Config.ssh.local_key_path)`"",
            "-o", "StrictHostKeyChecking=no",
            "$($script:Config.ssh.username)@$targetHost",
            $setupCronCmd
        )

        $outputFile = "temp_cron_install.txt"
        $process = Start-Process -FilePath "ssh" -ArgumentList $sshArgs -Wait -NoNewWindow -PassThru -RedirectStandardOutput $outputFile -RedirectStandardError "temp_cron_install_error.txt"

        $cronResult = ""
        if (Test-Path $outputFile) {
            $cronResult = Get-Content $outputFile -Raw
            Remove-Item $outputFile -ErrorAction SilentlyContinue
        }
        Remove-Item "temp_cron_install_error.txt" -ErrorAction SilentlyContinue

        if ($process.ExitCode -eq 0) {
            Write-Host "✅ Certificate sync cron job installed successfully" -ForegroundColor Green
            Write-Host ""
            Write-Host "📋 Cron Job Details:" -ForegroundColor Cyan
            Write-Host "  Schedule: Every 6 hours (00:00, 06:00, 12:00, 18:00)" -ForegroundColor Gray
            Write-Host "  User: ssl-sync" -ForegroundColor Gray
            Write-Host "  Script: /opt/auth-service/scripts/sync-auth-certificates.sh" -ForegroundColor Gray
            Write-Host "  Log: /opt/auth-service/logs/cert-sync.log" -ForegroundColor Gray
        } else {
            Write-Host "❌ Failed to install cron job" -ForegroundColor Red
            Write-Host "Exit Code: $($process.ExitCode)" -ForegroundColor Gray
            if ($cronResult) {
                Write-Host "Output: $cronResult" -ForegroundColor Gray
            }
        }
    } catch {
        Write-Host "❌ Error installing cron job: $_" -ForegroundColor Red
    }
}

function Remove-CertificateSyncCronJob {
    <#
    .SYNOPSIS
    Removes certificate sync cron job for ssl-sync user
    
    .DESCRIPTION
    Removes the certificate sync cron job from ssl-sync user's crontab
    #>
    
    Write-Host "=== Removing Certificate Sync Cron Job ===" -ForegroundColor Cyan
    Write-Host ""
    
    # Check if configuration is loaded
    if ($null -eq $script:Config) {
        Write-Host "❌ Configuration not loaded" -ForegroundColor Red
        return
    }
    
    $targetHost = $script:Config.ssh.host
    Write-Host "Target Server: $targetHost" -ForegroundColor Cyan
    Write-Host "Removing cron job for ssl-sync user..." -ForegroundColor Yellow
    Write-Host ""
    
    try {
        # Remove cron job for certificate sync using temp file approach
        $tempCronFile = "/tmp/ssl-sync-cron-remove-$((Get-Date).Ticks)"
        $removeCronCmd = "sudo -u ssl-sync crontab -l 2>/dev/null | grep -v 'sync-auth-certificates' > $tempCronFile; sudo -u ssl-sync crontab $tempCronFile; rm -f $tempCronFile"

        # Use Start-Process instead of Invoke-Expression for better SSH handling
        $sshArgs = @(
            "-i", "`"$($script:Config.ssh.local_key_path)`"",
            "-o", "StrictHostKeyChecking=no",
            "$($script:Config.ssh.username)@$targetHost",
            $removeCronCmd
        )

        $outputFile = "temp_cron_remove.txt"
        $process = Start-Process -FilePath "ssh" -ArgumentList $sshArgs -Wait -NoNewWindow -PassThru -RedirectStandardOutput $outputFile -RedirectStandardError "temp_cron_remove_error.txt"

        $cronResult = ""
        if (Test-Path $outputFile) {
            $cronResult = Get-Content $outputFile -Raw
            Remove-Item $outputFile -ErrorAction SilentlyContinue
        }
        Remove-Item "temp_cron_remove_error.txt" -ErrorAction SilentlyContinue

        if ($process.ExitCode -eq 0) {
            Write-Host "✅ Certificate sync cron job removed successfully" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to remove cron job" -ForegroundColor Red
            Write-Host "Exit Code: $($process.ExitCode)" -ForegroundColor Gray
            if ($cronResult) {
                Write-Host "Output: $cronResult" -ForegroundColor Gray
            }
        }
    } catch {
        Write-Host "❌ Error removing cron job: $_" -ForegroundColor Red
    }
}

function Show-CertificateSyncCronStatus {
    <#
    .SYNOPSIS
    Shows status of certificate sync cron job
    
    .DESCRIPTION
    Displays current cron job status, schedule, and recent sync activity
    #>
    
    Write-Host "=== Certificate Sync Cron Job Status ===" -ForegroundColor Cyan
    Write-Host ""
    
    # Check if configuration is loaded
    if ($null -eq $script:Config) {
        Write-Host "❌ Configuration not loaded" -ForegroundColor Red
        return
    }
    
    $targetHost = $script:Config.ssh.host
    Write-Host "Target Server: $targetHost" -ForegroundColor Cyan
    Write-Host ""
    
    try {
        # Check if ssl-sync user exists
        $userCheckCmd = "id ssl-sync 2>/dev/null && echo 'USER_EXISTS' || echo 'USER_NOT_FOUND'"
        $sshArgs = @(
            "-i", "`"$($script:Config.ssh.local_key_path)`"",
            "-o", "StrictHostKeyChecking=no",
            "$($script:Config.ssh.username)@$targetHost",
            $userCheckCmd
        )

        $outputFile = "temp_user_check.txt"
        $process = Start-Process -FilePath "ssh" -ArgumentList $sshArgs -Wait -NoNewWindow -PassThru -RedirectStandardOutput $outputFile -RedirectStandardError "temp_user_check_error.txt"

        $userResult = ""
        if (Test-Path $outputFile) {
            $userResult = Get-Content $outputFile -Raw
            Remove-Item $outputFile -ErrorAction SilentlyContinue
        }
        Remove-Item "temp_user_check_error.txt" -ErrorAction SilentlyContinue

        if ($userResult -like "*USER_NOT_FOUND*") {
            Write-Host "❌ SSL-Sync user not found" -ForegroundColor Red
            Write-Host "💡 Please set up SSL-Sync user first" -ForegroundColor Yellow
            return
        }

        Write-Host "✅ SSL-Sync user exists" -ForegroundColor Green

        # Check if certificate sync script exists
        $scriptCheckCmd = "test -f /opt/auth-service/scripts/sync-auth-certificates.sh && echo 'SCRIPT_EXISTS' || echo 'SCRIPT_NOT_FOUND'"
        $sshArgs = @(
            "-i", "`"$($script:Config.ssh.local_key_path)`"",
            "-o", "StrictHostKeyChecking=no",
            "$($script:Config.ssh.username)@$targetHost",
            $scriptCheckCmd
        )

        $outputFile = "temp_script_check.txt"
        $process = Start-Process -FilePath "ssh" -ArgumentList $sshArgs -Wait -NoNewWindow -PassThru -RedirectStandardOutput $outputFile -RedirectStandardError "temp_script_check_error.txt"

        $scriptResult = ""
        if (Test-Path $outputFile) {
            $scriptResult = Get-Content $outputFile -Raw
            Remove-Item $outputFile -ErrorAction SilentlyContinue
        }
        Remove-Item "temp_script_check_error.txt" -ErrorAction SilentlyContinue

        if ($scriptResult -like "*SCRIPT_NOT_FOUND*") {
            Write-Host "❌ Certificate sync script not found" -ForegroundColor Red
            Write-Host "💡 Use option 1 to install the certificate sync script and cron job" -ForegroundColor Yellow
        } else {
            Write-Host "✅ Certificate sync script is deployed" -ForegroundColor Green
        }
        
        # Check cron job status
        $cronCheckCmd = "sudo -u ssl-sync crontab -l 2>/dev/null | grep 'sync-auth-certificates' || echo 'NO_CRON_JOB'"
        $sshArgs = @(
            "-i", "`"$($script:Config.ssh.local_key_path)`"",
            "-o", "StrictHostKeyChecking=no",
            "$($script:Config.ssh.username)@$targetHost",
            $cronCheckCmd
        )

        $outputFile = "temp_cron_check.txt"
        $process = Start-Process -FilePath "ssh" -ArgumentList $sshArgs -Wait -NoNewWindow -PassThru -RedirectStandardOutput $outputFile -RedirectStandardError "temp_cron_check_error.txt"

        $cronResult = ""
        if (Test-Path $outputFile) {
            $cronResult = Get-Content $outputFile -Raw
            Remove-Item $outputFile -ErrorAction SilentlyContinue
        }
        Remove-Item "temp_cron_check_error.txt" -ErrorAction SilentlyContinue

        if ($cronResult -like "*NO_CRON_JOB*") {
            Write-Host "❌ Certificate sync cron job not found" -ForegroundColor Red
            Write-Host "💡 Use option 1 to install the cron job" -ForegroundColor Yellow
        } else {
            Write-Host "✅ Certificate sync cron job is active" -ForegroundColor Green
            Write-Host ""
            Write-Host "📋 Cron Job Details:" -ForegroundColor Cyan
            Write-Host "  $cronResult" -ForegroundColor Gray
        }

        # Check recent sync activity
        Write-Host ""
        Write-Host "📊 Recent Sync Activity:" -ForegroundColor Cyan
        $logCheckCmd = "tail -10 /opt/auth-service/logs/cert-sync.log 2>/dev/null || echo 'NO_LOG_FILE'"
        $sshArgs = @(
            "-i", "`"$($script:Config.ssh.local_key_path)`"",
            "-o", "StrictHostKeyChecking=no",
            "$($script:Config.ssh.username)@$targetHost",
            $logCheckCmd
        )

        $outputFile = "temp_log_check.txt"
        $process = Start-Process -FilePath "ssh" -ArgumentList $sshArgs -Wait -NoNewWindow -PassThru -RedirectStandardOutput $outputFile -RedirectStandardError "temp_log_check_error.txt"

        $logResult = ""
        if (Test-Path $outputFile) {
            $logResult = Get-Content $outputFile -Raw
            Remove-Item $outputFile -ErrorAction SilentlyContinue
        }
        Remove-Item "temp_log_check_error.txt" -ErrorAction SilentlyContinue

        if ($logResult -like "*NO_LOG_FILE*") {
            Write-Host "  ⚠️ No sync log file found" -ForegroundColor Yellow
        } else {
            $logResult.Split("`n") | ForEach-Object {
                if ($_.Trim()) {
                    Write-Host "  $_" -ForegroundColor Gray
                }
            }
        }
        
    } catch {
        Write-Host "❌ Error checking cron job status: $_" -ForegroundColor Red
    }
}

function Test-CertificateSyncCronJob {
    <#
    .SYNOPSIS
    Tests certificate sync cron job by running it manually

    .DESCRIPTION
    Executes the certificate sync script manually to test functionality
    #>

    Write-Host "=== Testing Certificate Sync Cron Job ===" -ForegroundColor Cyan
    Write-Host ""

    # Check if configuration is loaded
    if ($null -eq $script:Config) {
        Write-Host "❌ Configuration not loaded" -ForegroundColor Red
        return
    }

    $targetHost = $script:Config.ssh.host
    Write-Host "Target Server: $targetHost" -ForegroundColor Cyan
    Write-Host "Running certificate sync manually..." -ForegroundColor Yellow
    Write-Host ""

    try {
        # Determine environment from target host
        $environment = "development"  # Default
        if ($script:Config.ssh.host -match "authbe") { $environment = "backend-production" }
        elseif ($script:Config.ssh.host -match "authfe") { $environment = "frontend-production" }
        elseif ($script:Config.ssh.host -match "auth-dev") { $environment = "development" }

        # Run certificate sync script manually as ssl-sync user with environment parameter
        $testCmd = "sudo -u ssl-sync /opt/auth-service/scripts/sync-auth-certificates.sh $environment"

        Write-Host "Executing certificate sync..." -ForegroundColor Yellow

        # Use Start-Process instead of Invoke-Expression for better SSH handling
        $sshArgs = @(
            "-i", "`"$($script:Config.ssh.local_key_path)`"",
            "-o", "StrictHostKeyChecking=no",
            "$($script:Config.ssh.username)@$targetHost",
            $testCmd
        )

        $outputFile = "temp_cert_sync_test.txt"
        $errorFile = "temp_cert_sync_error.txt"

        $process = Start-Process -FilePath "ssh" -ArgumentList $sshArgs -Wait -NoNewWindow -PassThru -RedirectStandardOutput $outputFile -RedirectStandardError $errorFile

        # Read output
        $testResult = ""
        if (Test-Path $outputFile) {
            $testResult = Get-Content $outputFile -Raw
            Remove-Item $outputFile -ErrorAction SilentlyContinue
        }

        # Read error output
        $errorOutput = ""
        if (Test-Path $errorFile) {
            $errorOutput = Get-Content $errorFile -Raw
            Remove-Item $errorFile -ErrorAction SilentlyContinue
        }

        if ($process.ExitCode -eq 0) {
            Write-Host "✅ Certificate sync test completed successfully" -ForegroundColor Green
            Write-Host ""
            Write-Host "📋 Sync Output:" -ForegroundColor Cyan
            if ($testResult) {
                $testResult.Split("`n") | ForEach-Object {
                    if ($_.Trim()) {
                        Write-Host "  $_" -ForegroundColor Gray
                    }
                }
            }
        } else {
            Write-Host "❌ Certificate sync test failed" -ForegroundColor Red
            Write-Host "Exit Code: $($process.ExitCode)" -ForegroundColor Gray
            if ($testResult) {
                Write-Host "Output: $testResult" -ForegroundColor Gray
            }
            if ($errorOutput) {
                Write-Host "Error: $errorOutput" -ForegroundColor Red
            }
        }
    } catch {
        Write-Host "❌ Error testing certificate sync: $_" -ForegroundColor Red
    }
}

function Deploy-CertificateSyncScript {
    <#
    .SYNOPSIS
    Deploys the certificate sync script to the target server

    .DESCRIPTION
    Copies the sync-auth-certificates.sh script to /opt/auth-service/scripts/ on the target server
    and ensures it has proper permissions
    #>

    $localScriptPath = Join-Path $PSScriptRoot "..\scripts\sync-auth-certificates.sh"

    if (-not (Test-Path $localScriptPath)) {
        Write-Host "❌ Certificate sync script not found: $localScriptPath" -ForegroundColor Red
        return $false
    }

    try {
        # Create scripts directory on remote server
        $createDirCmd = "sudo mkdir -p /opt/auth-service/scripts"
        $sshArgs = @(
            "-i", "`"$($script:Config.ssh.local_key_path)`"",
            "-o", "StrictHostKeyChecking=no",
            "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
            $createDirCmd
        )

        $outputFile = "temp_create_dir.txt"
        $process = Start-Process -FilePath "ssh" -ArgumentList $sshArgs -Wait -NoNewWindow -PassThru -RedirectStandardOutput $outputFile -RedirectStandardError "temp_create_dir_error.txt"
        Remove-Item $outputFile -ErrorAction SilentlyContinue
        Remove-Item "temp_create_dir_error.txt" -ErrorAction SilentlyContinue

        # Read script content and encode as base64 for reliable transfer
        Write-Host "  Uploading sync-auth-certificates.sh..." -ForegroundColor Gray
        $scriptContent = Get-Content $localScriptPath -Raw
        $encodedContent = [Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes($scriptContent))

        # Transfer script using base64 encoding (more reliable than SCP)
        $transferCmd = "echo '$encodedContent' | base64 -d > /tmp/sync-auth-certificates.sh"
        $sshArgs = @(
            "-i", "`"$($script:Config.ssh.local_key_path)`"",
            "-o", "StrictHostKeyChecking=no",
            "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
            $transferCmd
        )

        $outputFile = "temp_transfer.txt"
        $process = Start-Process -FilePath "ssh" -ArgumentList $sshArgs -Wait -NoNewWindow -PassThru -RedirectStandardOutput $outputFile -RedirectStandardError "temp_transfer_error.txt"
        Remove-Item $outputFile -ErrorAction SilentlyContinue
        Remove-Item "temp_transfer_error.txt" -ErrorAction SilentlyContinue

        if ($process.ExitCode -ne 0) {
            Write-Host "❌ Failed to upload certificate sync script" -ForegroundColor Red
            return $false
        }

        # Move script to final location and make executable
        $moveCmd = "sudo mv /tmp/sync-auth-certificates.sh /opt/auth-service/scripts/ && sudo chmod +x /opt/auth-service/scripts/sync-auth-certificates.sh && sudo chown ssl-sync /opt/auth-service/scripts/sync-auth-certificates.sh"
        $sshArgs = @(
            "-i", "`"$($script:Config.ssh.local_key_path)`"",
            "-o", "StrictHostKeyChecking=no",
            "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
            $moveCmd
        )

        $outputFile = "temp_move.txt"
        $process = Start-Process -FilePath "ssh" -ArgumentList $sshArgs -Wait -NoNewWindow -PassThru -RedirectStandardOutput $outputFile -RedirectStandardError "temp_move_error.txt"
        Remove-Item $outputFile -ErrorAction SilentlyContinue
        Remove-Item "temp_move_error.txt" -ErrorAction SilentlyContinue

        if ($process.ExitCode -eq 0) {
            Write-Host "  ✅ Certificate sync script deployed successfully" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Failed to move certificate sync script to final location" -ForegroundColor Red
            return $false
        }

    } catch {
        Write-Host "❌ Error deploying certificate sync script: $_" -ForegroundColor Red
        return $false
    }
}

function Show-CertificateSyncCronMenu {
    <#
    .SYNOPSIS
    Shows the certificate sync cron job management menu

    .DESCRIPTION
    Displays menu options for managing certificate sync cron jobs
    #>

    do {
        Clear-Host
        Write-Host "========================================================" -ForegroundColor Cyan
        Write-Host "            Certificate Sync Cron Job Management" -ForegroundColor Cyan
        Write-Host "========================================================" -ForegroundColor Cyan
        Write-Host ""

        # Show current environment info
        if ($script:Config -and $script:Config.ssh) {
            Write-Host "Current Target Server: $($script:Config.ssh.host)" -ForegroundColor Cyan
            Write-Host "Environment: $($script:Config.environment)" -ForegroundColor Cyan
        } else {
            Write-Host "⚠️ No environment configuration loaded" -ForegroundColor Yellow
        }
        Write-Host ""

        Write-Host "Certificate Sync Cron Job Options:" -ForegroundColor Yellow
        Write-Host "[1] Install Certificate Sync Cron Job" -ForegroundColor Green
        Write-Host "[2] Show Cron Job Status" -ForegroundColor White
        Write-Host "[3] Test Certificate Sync (Manual Run)" -ForegroundColor Yellow
        Write-Host "[4] Remove Certificate Sync Cron Job" -ForegroundColor Red
        Write-Host "[0] Back to SSL-Sync Menu" -ForegroundColor Gray
        Write-Host ""

        $choice = Read-Host "Select an option (0-4)"

        switch ($choice) {
            "1" {
                Install-CertificateSyncCronJob
                Read-Host "Press Enter to continue"
            }
            "2" {
                Show-CertificateSyncCronStatus
                Read-Host "Press Enter to continue"
            }
            "3" {
                Test-CertificateSyncCronJob
                Read-Host "Press Enter to continue"
            }
            "4" {
                Write-Host ""
                $confirm = Read-Host "Are you sure you want to remove the certificate sync cron job? (y/N)"
                if ($confirm -eq "y" -or $confirm -eq "Y") {
                    Remove-CertificateSyncCronJob
                } else {
                    Write-Host "Operation cancelled." -ForegroundColor Yellow
                }
                Read-Host "Press Enter to continue"
            }
            "0" {
                return
            }
            default {
                Write-Host "Invalid option. Please try again." -ForegroundColor Red
                Start-Sleep -Seconds 1
            }
        }
    } while ($choice -ne "0")
}

# Export all functions
Export-ModuleMember -Function Set-CertificateSyncConfig, Install-CertificateSyncCronJob, Remove-CertificateSyncCronJob, Show-CertificateSyncCronStatus, Test-CertificateSyncCronJob, Deploy-CertificateSyncScript, Show-CertificateSyncCronMenu
