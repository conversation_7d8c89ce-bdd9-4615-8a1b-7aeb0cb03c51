# Test-ServerReadiness-AuthService.psm1 - Auth-Service specific server readiness testing

<#
.SYNOPSIS
    Provides functionality for testing server readiness specifically for auth-service deployment.

.DESCRIPTION
    This module tests SSH connectivity, dependency availability, and server configuration
    specifically for the auth-service C++23 application. It does NOT check database-service
    requirements and focuses only on auth-service needs.

.NOTES
    File Name      : Test-ServerReadiness-AuthService.psm1
    Author         : Auth Service Deployment Team
    Prerequisite   : PowerShell 5.1 or later
    Copyright      : (c) 2025 Augment
#>
# Helper function to get environment from hostname
function Get-EnvironmentFromHostname {
    param([string]$Hostname)

    switch -Regex ($Hostname) {
        "auth-dev" { return "auth-dev" }
        "authbe" { return "authbe" }
        "authfe" { return "authfe" }
        default { return $null }
    }
}

# Helper function to load requirements for environment
function Get-RequirementsForEnvironment {
    param([string]$Environment)

    if (-not $Environment) { return $null }

    $requirementsFile = Join-Path $PSScriptRoot "..\requirements\$Environment-requirements.json"
    if (Test-Path $requirementsFile) {
        try {
            return Get-Content $requirementsFile -Raw | ConvertFrom-Json
        } catch {
            Write-Warning "Failed to load requirements from $requirementsFile`: $_"
            return $null
        }
    }
    return $null
}

function Test-ServerReadiness {
    [CmdletBinding()]
    param()

    Write-Host "=== Testing Auth-Service Server Readiness ===" -ForegroundColor Cyan
    Write-Host ""

    # Get configuration from multiple sources (prioritize Global variables set by menu)
    $config = $null
    if ($Global:Config) {
        $config = $Global:Config
        Write-Host "✅ Using Global configuration" -ForegroundColor Green
    } elseif ($Global:CurrentConfig) {
        $config = $Global:CurrentConfig
        Write-Host "✅ Using Global current configuration" -ForegroundColor Green
    } elseif ($script:Config) {
        $config = $script:Config
        Write-Host "✅ Using script configuration" -ForegroundColor Green
    } elseif (Get-Command -Name Get-Configuration -ErrorAction SilentlyContinue) {
        $config = Get-Configuration
        Write-Host "✅ Using loaded configuration" -ForegroundColor Green
    }

    # Load auth-service configuration
    if ($null -eq $config) {
        Write-Host "❌ Configuration is not loaded. Please set environment first (Menu Option 1)." -ForegroundColor Red
        Write-Host "💡 No configuration available from any source." -ForegroundColor Yellow
        return $false
    }

    # Use the found configuration
    $script:Config = $config
    Write-Host "✅ Using loaded configuration" -ForegroundColor Green

    # Determine environment-specific requirements file (prioritize global variables)
    $environment = if ($env:DeploymentEnvironment) {
        $env:DeploymentEnvironment
        Write-Host "✅ Environment from env:DeploymentEnvironment: $env:DeploymentEnvironment" -ForegroundColor Green
    } elseif ($Global:Environment) {
        $Global:Environment
        Write-Host "✅ Environment from Global:Environment: $Global:Environment" -ForegroundColor Green
    } elseif ($config -and $config.PSObject.Properties['environment']) {
        $config.environment
        Write-Host "✅ Environment from config: $($config.environment)" -ForegroundColor Green
    } elseif ($config -and $config.ssh -and $config.ssh.host) {
        # Derive environment from hostname if not explicitly set
        $derivedEnv = if ($config.ssh.host -match "auth-dev|dev\.chcit\.org") { "development" }
        elseif ($config.ssh.host -match "authbe") { "backend-production" }
        elseif ($config.ssh.host -match "authfe") { "frontend-production" }
        else { "development" }  # Default fallback
        Write-Host "✅ Environment derived from hostname ($($config.ssh.host)): $derivedEnv" -ForegroundColor Green
        $derivedEnv
    } else {
        Write-Host "⚠️ Using default environment: development" -ForegroundColor Yellow
        "development"  # Default fallback
    }

    # Map environment to requirements file
    $requirementsFileName = switch ($environment) {
        "development" { "auth-dev-requirements.json" }
        "backend-production" { "authbe-requirements.json" }
        "frontend-production" { "authfe-requirements.json" }
        default { "auth-dev-requirements.json" }  # Default fallback
    }

    # Load environment-specific requirements
    $requirementsPath = Join-Path $PSScriptRoot "..\requirements\$requirementsFileName"
    if (Test-Path $requirementsPath) {
        try {
            $requirements = Get-Content $requirementsPath | ConvertFrom-Json
            Write-Host "✅ Requirements file loaded: $requirementsFileName (Environment: $environment)" -ForegroundColor Green

            # Display target requirements
            Write-Host "Target OS: $($requirements.operating_system.distribution) $($requirements.operating_system.version)" -ForegroundColor Yellow
            Write-Host "Minimum Specs: $($requirements.system_requirements.minimum_specs.cpu_cores) cores, $($requirements.system_requirements.minimum_specs.memory_gb)GB RAM" -ForegroundColor Yellow
        } catch {
            Write-Host "⚠️ Could not load requirements file: $_" -ForegroundColor Yellow
        }
    } else {
        Write-Host "⚠️ Requirements file not found: $requirementsPath" -ForegroundColor Yellow
        Write-Host "💡 Expected environment: $environment, file: $requirementsFileName" -ForegroundColor Gray
    }


    # Determine current environment (use same logic as requirements detection)
    $currentEnvironment = if ($env:DeploymentEnvironment) {
        $env:DeploymentEnvironment
    } elseif ($Global:Environment) {
        $Global:Environment
    } elseif ($config -and $config.PSObject.Properties['environment']) {
        $config.environment
    } elseif ($config -and $config.ssh -and $config.ssh.host) {
        # Derive environment from hostname if not explicitly set
        if ($config.ssh.host -match "auth-dev|dev\.chcit\.org") { "development" }
        elseif ($config.ssh.host -match "authbe") { "backend-production" }
        elseif ($config.ssh.host -match "authfe") { "frontend-production" }
        else { "unknown" }
    } else {
        "not-set"
    }

    # Auth-service specific settings
    $serviceName = "auth-service"
    $targetHost = $script:Config.ssh.host
    $installDir = "/opt/auth-service"
    $servicePort = 8082

    Write-Host "Current Environment: $currentEnvironment" -ForegroundColor Cyan
    Write-Host "Service: $serviceName" -ForegroundColor Yellow
    Write-Host "Target Host: $targetHost" -ForegroundColor Yellow
    Write-Host "Install Directory: $installDir" -ForegroundColor Yellow
    Write-Host "Service Port: $servicePort" -ForegroundColor Yellow
    Write-Host "SSH Key Path: $($script:Config.ssh.key_path)" -ForegroundColor Yellow
    Write-Host "SSH Local Key Path: $($script:Config.ssh.local_key_path)" -ForegroundColor Yellow
    Write-Host ""

    # Test 1: SSH Connectivity
    Write-Host "1. Testing SSH connectivity to $targetHost..." -ForegroundColor Yellow

    try {
        $sshCommand = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no -o ConnectTimeout=10 $($script:Config.ssh.username)@$targetHost `"echo 'SSH connection successful'`""
        $result = Invoke-Expression $sshCommand

        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✅ SSH connection successful" -ForegroundColor Green
        } else {
            Write-Host "  ❌ SSH connection failed" -ForegroundColor Red
            Write-Host "  💡 Suggestions:" -ForegroundColor Yellow
            Write-Host "    - Check if hostname '$targetHost' is correct" -ForegroundColor Yellow
            Write-Host "    - Verify SSH key path: $($script:Config.ssh.local_key_path)" -ForegroundColor Yellow
            Write-Host "    - Try using database host instead: $($script:Config.database.host)" -ForegroundColor Yellow
            return $false
        }
    } catch {
        Write-Host "  ❌ SSH connection error: $_" -ForegroundColor Red
        Write-Host "  💡 Suggestions:" -ForegroundColor Yellow
        Write-Host "    - Check if hostname '$targetHost' resolves" -ForegroundColor Yellow
        Write-Host "    - Verify network connectivity" -ForegroundColor Yellow
        Write-Host "    - Check SSH key permissions" -ForegroundColor Yellow
        return $false
    }

    # Test 2: System Information
    Write-Host ""
    Write-Host "2. Checking system information..." -ForegroundColor Yellow

    try {
        $sysInfoCmd = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"uname -a && lsb_release -a 2>/dev/null || cat /etc/os-release`""
        $sysInfo = Invoke-Expression $sysInfoCmd

        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✅ System information retrieved" -ForegroundColor Green
            Write-Host "  $($sysInfo.Split("`n")[0])" -ForegroundColor Gray
        } else {
            Write-Host "  ❌ Could not retrieve system information" -ForegroundColor Red
        }
    } catch {
        Write-Host "  ❌ System information error: $_" -ForegroundColor Red
    }

    # Test 3: Auth-Service Required Directories
    Write-Host ""
    Write-Host "3. Checking auth-service directories..." -ForegroundColor Yellow

    $authDirs = @(
        "/opt/auth-service",
        "/opt/auth-service/bin",
        "/opt/auth-service/config",
        "/opt/auth-service/logs"
    )

    foreach ($dir in $authDirs) {
        try {
            $dirCheckCmd = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"test -d $dir && echo 'EXISTS' || echo 'NOT_EXISTS'`""
            $dirResult = Invoke-Expression $dirCheckCmd

            if ($LASTEXITCODE -eq 0 -and $dirResult -like "*EXISTS*") {
                Write-Host "  ✅ Directory exists: $dir" -ForegroundColor Green
            } else {
                Write-Host "  ❌ Directory missing: $dir" -ForegroundColor Red
            }
        } catch {
            Write-Host "  ❌ Error checking directory $dir`: $_" -ForegroundColor Red
        }
    }

    # Test 4: C++23 Development Tools
    Write-Host ""
    Write-Host "4. Checking C++23 development tools..." -ForegroundColor Yellow

    $devTools = @(
        @{ Name = "GCC 14+"; Command = "g++-14 --version 2>/dev/null | head -1 || g++ --version 2>/dev/null | head -1 || gcc --version 2>/dev/null | head -1 || echo 'NOT_INSTALLED'" },
        @{ Name = "CMake"; Command = "cmake --version 2>/dev/null | head -1 || echo 'NOT_INSTALLED'" },
        @{ Name = "Make"; Command = "make --version 2>/dev/null | head -1 || echo 'NOT_INSTALLED'" }
    )

    foreach ($tool in $devTools) {
        try {
            $toolCmd = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"$($tool.Command)`""
            $toolResult = Invoke-Expression $toolCmd

            # Debug output
            Write-Host "    Debug: $($tool.Name) result: '$toolResult'" -ForegroundColor Gray

            if ($LASTEXITCODE -eq 0 -and $toolResult -notlike "*NOT_INSTALLED*" -and $toolResult.Trim() -ne "") {
                # Special handling for GCC version checking
                if ($tool.Name -eq "GCC 14+") {
                    if ($toolResult -match "gcc.*?(\d+\.\d+)" -or $toolResult -match "g\+\+.*?(\d+\.\d+)") {
                        $version = [decimal]$matches[1]
                        if ($version -ge 14.0) {
                            Write-Host "  ✅ $($tool.Name): $($toolResult.Split("`n")[0])" -ForegroundColor Green
                        } elseif ($version -ge 11.0) {
                            Write-Host "  ⚠️ $($tool.Name): $($toolResult.Split("`n")[0]) (version $version, 14+ recommended)" -ForegroundColor Yellow
                        } else {
                            Write-Host "  ❌ $($tool.Name): $($toolResult.Split("`n")[0]) (version $version too old, need 14+)" -ForegroundColor Red
                        }
                    } else {
                        Write-Host "  ✅ $($tool.Name): $($toolResult.Split("`n")[0])" -ForegroundColor Green
                    }
                } else {
                    Write-Host "  ✅ $($tool.Name): $($toolResult.Split("`n")[0])" -ForegroundColor Green
                }
            } else {
                Write-Host "  ❌ $($tool.Name): Not installed" -ForegroundColor Red
            }
        } catch {
            Write-Host "  ❌ $($tool.Name): Error checking - $_" -ForegroundColor Red
        }
    }

    # Test 5: Auth-Service Specific Libraries
    Write-Host ""
    Write-Host "5. Checking auth-service specific libraries..." -ForegroundColor Yellow

    $authLibs = @(
        @{ Name = "Boost Libraries"; Command = "dpkg -s libboost-all-dev 2>/dev/null | grep 'Status.*install ok installed' || echo 'NOT_INSTALLED'" },
        @{ Name = "OpenSSL Development"; Command = "dpkg -s libssl-dev 2>/dev/null | grep 'Status.*install ok installed' || echo 'NOT_INSTALLED'" },
        @{ Name = "JSON Library"; Command = "dpkg -s nlohmann-json3-dev 2>/dev/null | grep 'Status.*install ok installed' || echo 'NOT_INSTALLED'" },
        @{ Name = "PostgreSQL Client Dev"; Command = "dpkg -s libpq-dev 2>/dev/null | grep 'Status.*install ok installed' || echo 'NOT_INSTALLED'" },
        @{ Name = "PostgreSQL C++ (pqxx)"; Command = "dpkg -s libpqxx-dev 2>/dev/null | grep 'Status.*install ok installed' || echo 'NOT_INSTALLED'" },
        @{ Name = "Argon2 Password Hashing"; Command = "dpkg -s libargon2-dev 2>/dev/null | grep 'Status.*install ok installed' || echo 'NOT_INSTALLED'" },
        @{ Name = "JWT Token Library"; Command = "dpkg -s libjwt-dev 2>/dev/null | grep 'Status.*install ok installed' || echo 'NOT_INSTALLED'" },
        @{ Name = "HTTP Client Library"; Command = "dpkg -s libcurl4-openssl-dev 2>/dev/null | grep 'Status.*install ok installed' || echo 'NOT_INSTALLED'" }
    )

    foreach ($lib in $authLibs) {
        try {
            $libCmd = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"$($lib.Command)`""
            $libResult = Invoke-Expression $libCmd

            # Debug output
            Write-Host "    Debug: $($lib.Name) result: '$libResult'" -ForegroundColor Gray

            if ($LASTEXITCODE -eq 0 -and $libResult -like "*install ok installed*") {
                Write-Host "  ✅ $($lib.Name): Installed" -ForegroundColor Green
            } else {
                Write-Host "  ❌ $($lib.Name): Not installed" -ForegroundColor Red
            }
        } catch {
            Write-Host "  ❌ $($lib.Name): Error checking - $_" -ForegroundColor Red
        }
    }

    # Test 6: PostgreSQL Server
    Write-Host ""
    Write-Host "6. Checking PostgreSQL server..." -ForegroundColor Yellow

    try {
        # First check if PostgreSQL is installed (check both generic and version-specific packages)
        $pgInstalledCmd = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"dpkg -s postgresql-17 2>/dev/null | grep Status || dpkg -s postgresql 2>/dev/null | grep Status || echo 'NOT_INSTALLED'`""
        $pgInstalled = Invoke-Expression $pgInstalledCmd

        if ($LASTEXITCODE -eq 0 -and $pgInstalled -like "*install ok installed*") {
            # Determine which version is installed
            $pgVersionCheck = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"dpkg -s postgresql-17 2>/dev/null && echo 'PG17_INSTALLED' || echo 'PG_GENERIC_INSTALLED'`""
            $versionResult = Invoke-Expression $pgVersionCheck

            if ($versionResult -like "*PG17_INSTALLED*") {
                Write-Host "  ✅ PostgreSQL 17 server is installed" -ForegroundColor Green
            } else {
                Write-Host "  ✅ PostgreSQL server is installed (generic package)" -ForegroundColor Green
            }

            # Check if it's running
            $pgStatusCmd = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"systemctl is-active postgresql 2>/dev/null || echo 'NOT_RUNNING'`""
            $pgStatus = Invoke-Expression $pgStatusCmd

            if ($LASTEXITCODE -eq 0 -and $pgStatus -like "*active*") {
                Write-Host "  ✅ PostgreSQL server is running" -ForegroundColor Green
            } else {
                Write-Host "  ❌ PostgreSQL server is not running (Status: $pgStatus)" -ForegroundColor Red
            }
        } else {
            Write-Host "  ❌ PostgreSQL server is not installed" -ForegroundColor Red
        }

        # Check PostgreSQL client tools and version
        $pgVersionCmd = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"psql --version 2>/dev/null || echo 'NOT_INSTALLED'`""
        $pgVersion = Invoke-Expression $pgVersionCmd

        if ($LASTEXITCODE -eq 0 -and $pgVersion -notlike "*NOT_INSTALLED*") {
            $versionLine = $pgVersion.Split("`n")[0]
            Write-Host "  ✅ PostgreSQL client: $versionLine" -ForegroundColor Green

            # Check if it meets version requirements
            if ($versionLine -match "17\.[0-9]+") {
                Write-Host "  ✅ Version meets requirements (17.x)" -ForegroundColor Green
            } elseif ($versionLine -match "1[8-9]\.[0-9]+|[2-9][0-9]\.[0-9]+") {
                Write-Host "  ✅ Version exceeds requirements ($($matches[0]))" -ForegroundColor Green
            } elseif ($versionLine -match "16\.[0-9]+") {
                Write-Host "  ⚠️ Version 16.x detected - meets minimum but 17+ recommended" -ForegroundColor Yellow
            } else {
                Write-Host "  ⚠️ Version may be below requirements" -ForegroundColor Yellow
            }
        } else {
            Write-Host "  ❌ PostgreSQL client not installed" -ForegroundColor Red
        }
    } catch {
        Write-Host "  ❌ Error checking PostgreSQL: $_" -ForegroundColor Red
    }

    # Test 7: Port Availability
    Write-Host ""
    Write-Host "7. Checking port availability..." -ForegroundColor Yellow

    try {
        $portCheckCmd = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"netstat -tuln | grep -w $servicePort || echo 'PORT_AVAILABLE'`""
        $portResult = Invoke-Expression $portCheckCmd

        if ($LASTEXITCODE -eq 0 -and $portResult -like "*PORT_AVAILABLE*") {
            Write-Host "  ✅ Port $servicePort is available" -ForegroundColor Green
        } else {
            Write-Host "  ❌ Port $servicePort is in use" -ForegroundColor Red
        }
    } catch {
        Write-Host "  ❌ Error checking port availability: $_" -ForegroundColor Red
    }

    # Test 8: Certificate Management Setup
    Write-Host ""
    Write-Host "8. Checking certificate management setup..." -ForegroundColor Yellow

    # Check if ssl-sync user exists
    try {
        $sslSyncUserCmd = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"id ssl-sync 2>/dev/null || echo 'SSL_SYNC_USER_NOT_FOUND'`""
        $sslSyncUserResult = Invoke-Expression $sslSyncUserCmd

        if ($sslSyncUserResult -like "*SSL_SYNC_USER_NOT_FOUND*") {
            Write-Host "  ⚠️ SSL-Sync user not found" -ForegroundColor Yellow
            Write-Host "    Consider setting up SSL-Sync user for secure certificate management" -ForegroundColor Gray
        } else {
            Write-Host "  ✅ SSL-Sync user exists" -ForegroundColor Green

            # Check SSL-Sync SSH directory and keys
            $sslSyncDirCmd = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"sudo test -d /home/<USER>/.ssh && echo 'SSH_DIR_EXISTS' || echo 'SSH_DIR_NOT_FOUND'`""
            $sslSyncDirResult = Invoke-Expression $sslSyncDirCmd

            if ($sslSyncDirResult -like "*SSH_DIR_EXISTS*") {
                # List SSH keys to find what actually exists
                $sslSyncKeysCmd = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"sudo ls -la /home/<USER>/.ssh/ 2>/dev/null | grep -E '(id_rsa|id_ed25519|id_ecdsa)' | grep -v '.pub' | wc -l`""
                $sslSyncKeysResult = Invoke-Expression $sslSyncKeysCmd

                if ([int]$sslSyncKeysResult -gt 0) {
                    # Get the actual key names
                    $keyNamesCmd = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"sudo ls -1 /home/<USER>/.ssh/ 2>/dev/null | grep -E '(id_rsa|id_ed25519|id_ecdsa)' | grep -v '.pub' | head -3`""
                    $keyNamesResult = Invoke-Expression $keyNamesCmd
                    Write-Host "  ✅ SSL-Sync SSH key(s) found: $($keyNamesResult -replace "`n", ", ")" -ForegroundColor Green
                } else {
                    Write-Host "  ❌ SSL-Sync SSH keys not found" -ForegroundColor Red
                    Write-Host "    💡 Run SSL-Sync setup to generate SSH keys" -ForegroundColor Yellow
                }
            } else {
                Write-Host "  ❌ SSL-Sync SSH directory not found" -ForegroundColor Red
                Write-Host "    💡 Run SSL-Sync setup to create SSH configuration" -ForegroundColor Yellow
            }

            # Check certificate backup directory
            $certBackupCmd = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"test -d /home/<USER>/letsencrypt_backup && echo 'CERT_BACKUP_DIR_EXISTS' || echo 'CERT_BACKUP_DIR_NOT_FOUND'`""
            $certBackupResult = Invoke-Expression $certBackupCmd

            if ($certBackupResult -like "*CERT_BACKUP_DIR_EXISTS*") {
                Write-Host "  ✅ Certificate backup directory exists" -ForegroundColor Green

                # Check for actual certificates
                $certFilesCmd = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"find /home/<USER>/letsencrypt_backup -name '*.pem' | wc -l`""
                $certFilesResult = Invoke-Expression $certFilesCmd

                if ([int]$certFilesResult -gt 0) {
                    Write-Host "  ✅ Certificate files found ($certFilesResult files)" -ForegroundColor Green
                } else {
                    Write-Host "  ⚠️ No certificate files found" -ForegroundColor Yellow
                    Write-Host "    Run certificate sync to populate certificates" -ForegroundColor Gray
                }
            } else {
                Write-Host "  ❌ Certificate backup directory not found" -ForegroundColor Red
            }

            # Check sudoers configuration
            $sudoersCmd = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"test -f /etc/sudoers.d/ssl-sync && echo 'SUDOERS_EXISTS' || echo 'SUDOERS_NOT_FOUND'`""
            $sudoersResult = Invoke-Expression $sudoersCmd

            if ($sudoersResult -like "*SUDOERS_EXISTS*") {
                Write-Host "  ✅ SSL-Sync sudoers configuration exists" -ForegroundColor Green
            } else {
                Write-Host "  ❌ SSL-Sync sudoers configuration not found" -ForegroundColor Red
            }
        }
    } catch {
        Write-Host "  ❌ Error checking certificate management setup: $_" -ForegroundColor Red
    }

    # Test 9: System Resources
    Write-Host ""
    Write-Host "9. Checking system resources..." -ForegroundColor Yellow

    try {
        $resourceCmd = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"df -h / && free -h`""
        $resourceResult = Invoke-Expression $resourceCmd

        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✅ System resources checked" -ForegroundColor Green
            $lines = $resourceResult.Split("`n")
            foreach ($line in $lines) {
                if ($line -like "*/*" -or $line -like "*Mem:*") {
                    Write-Host "  $line" -ForegroundColor Gray
                }
            }
        } else {
            Write-Host "  ❌ Could not check system resources" -ForegroundColor Red
        }
    } catch {
        Write-Host "  ❌ System resources error: $_" -ForegroundColor Red
    }

    # Test 9: Certificate Setup Verification
    Write-Host ""
    Write-Host "9. Checking SSL certificate setup..." -ForegroundColor Yellow

    # Check if this is a certificate-enabled environment using requirements
    $environment = Get-EnvironmentFromHostname -Hostname $targetHost
    $requirements = Get-RequirementsForEnvironment -Environment $environment
    $isCertEnvironment = $false

    if ($requirements -and $requirements.ssl_certificates -and $requirements.ssl_certificates.required) {
        $isCertEnvironment = $true
        Write-Host "  Environment: $environment" -ForegroundColor Cyan
        Write-Host "  SSL Domain: $($requirements.ssl_certificates.full_domain)" -ForegroundColor Cyan
    } else {
        # Fallback to hostname matching if requirements not found
        $isCertEnvironment = $targetHost -match "auth-dev|authbe|authfe"
        if ($isCertEnvironment) {
            Write-Host "  Using fallback certificate detection for: $targetHost" -ForegroundColor Yellow
        }
    }

    if ($isCertEnvironment) {
        # Check auth-service user exists and is in ssl-cert group
        try {
            $userCheckCmd = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"id auth-service 2>/dev/null && groups auth-service | grep ssl-cert && echo 'USER_OK' || echo 'USER_MISSING'`""
            $userResult = Invoke-Expression $userCheckCmd

            if ($userResult -like "*USER_OK*") {
                Write-Host "  ✅ Auth-service user exists and is in ssl-cert group" -ForegroundColor Green
            } else {
                Write-Host "  ❌ Auth-service user missing or not in ssl-cert group" -ForegroundColor Red
                Write-Host "    💡 Run certificate setup from dependency installation" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "  ❌ Error checking auth-service user: $_" -ForegroundColor Red
        }

        # Use wildcard certificate domain for all environments
        $certDomain = if ($requirements -and $requirements.ssl_certificates -and $requirements.ssl_certificates.wildcard_cert) {
            $requirements.ssl_certificates.wildcard_cert
        } elseif ($requirements -and $requirements.ssl_certificates -and $requirements.ssl_certificates.domain) {
            $requirements.ssl_certificates.domain
        } else {
            "chcit.org"  # fallback
        }

        $certDirs = @(
            "/home/<USER>/letsencrypt_backup",
            "/home/<USER>/letsencrypt_backup/live/$certDomain",
            "/etc/letsencrypt/live/$certDomain"
        )

        foreach ($certDir in $certDirs) {
            try {
                $dirCheckCmd = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"test -d '$certDir' && echo 'EXISTS' || echo 'MISSING'`""
                $dirResult = Invoke-Expression $dirCheckCmd

                if ($dirResult -like "*EXISTS*") {
                    Write-Host "  ✅ Certificate directory exists: $certDir" -ForegroundColor Green
                } else {
                    Write-Host "  ❌ Certificate directory missing: $certDir" -ForegroundColor Red
                }
            } catch {
                Write-Host "  ❌ Error checking certificate directory $certDir`: $_" -ForegroundColor Red
            }
        }

        # Check certificate files using environment-specific domain
        try {
            $certFilesCmd = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"ls -la /home/<USER>/letsencrypt_backup/live/$certDomain/*.pem 2>/dev/null | wc -l`""
            $certCount = Invoke-Expression $certFilesCmd

            if ([int]$certCount -ge 4) {
                Write-Host "  ✅ Certificate files found ($certCount files) for domain: $certDomain" -ForegroundColor Green

                # Check certificate expiry
                $expiryCmd = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"openssl x509 -in /home/<USER>/letsencrypt_backup/live/$certDomain/cert.pem -noout -enddate 2>/dev/null || echo 'CERT_ERROR'`""
                $expiryResult = Invoke-Expression $expiryCmd

                if ($expiryResult -notlike "*CERT_ERROR*") {
                    Write-Host "  ✅ Certificate expiry: $expiryResult" -ForegroundColor Green
                } else {
                    Write-Host "  ⚠️ Could not check certificate expiry" -ForegroundColor Yellow
                }
            } else {
                Write-Host "  ❌ Certificate files missing or incomplete" -ForegroundColor Red
                Write-Host "    💡 Run certificate sync from project-tracker.chcit.org" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "  ❌ Error checking certificate files: $_" -ForegroundColor Red
        }

        # Check cron job for certificate sync (check ssl-sync user's crontab)
        try {
            $cronCheckCmd = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"sudo -u ssl-sync crontab -l 2>/dev/null | grep sync-auth-certificates && echo 'CRON_OK' || echo 'CRON_MISSING'`""
            $cronResult = Invoke-Expression $cronCheckCmd

            if ($cronResult -like "*CRON_OK*") {
                Write-Host "  ✅ Certificate sync cron job is scheduled" -ForegroundColor Green
                # Show the cron job details
                $cronDetails = $cronResult -replace "CRON_OK", "" | Where-Object { $_ -match "sync-auth-certificates" }
                if ($cronDetails) {
                    Write-Host "    Schedule: $($cronDetails.Trim())" -ForegroundColor Gray
                }
            } else {
                Write-Host "  ❌ Certificate sync cron job not found" -ForegroundColor Red
                Write-Host "    💡 Cron job should be set up during dependency installation" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "  ❌ Error checking cron job: $_" -ForegroundColor Red
        }

        # Check certificate script exists
        try {
            $scriptCheckCmd = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"test -x /opt/auth-service/scripts/setup-certificate-access.sh && echo 'SCRIPT_OK' || echo 'SCRIPT_MISSING'`""
            $scriptResult = Invoke-Expression $scriptCheckCmd

            if ($scriptResult -like "*SCRIPT_OK*") {
                Write-Host "  ✅ Certificate setup script is installed" -ForegroundColor Green
            } else {
                Write-Host "  ❌ Certificate setup script missing" -ForegroundColor Red
                Write-Host "    💡 Script should be installed during dependency installation" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "  ❌ Error checking certificate script: $_" -ForegroundColor Red
        }

        # Test HTTPS endpoints for certificate validation
        try {
            $httpsTestCmd = ""
            $endpointDescription = ""

            if ($targetHost -match "auth-dev") {
                $httpsTestCmd = "curl -I https://auth-dev.chcit.org:8082 2>/dev/null | head -1"
                $endpointDescription = "auth-dev.chcit.org:8082"
            } elseif ($targetHost -match "authbe") {
                $httpsTestCmd = "curl -I https://authbe.chcit.org:8082 2>/dev/null | head -1"
                $endpointDescription = "authbe.chcit.org:8082"
            } elseif ($targetHost -match "authfe") {
                $httpsTestCmd = "curl -I https://authfe.chcit.org:3000 2>/dev/null | head -1"
                $endpointDescription = "authfe.chcit.org:3000"
            }

            if ($httpsTestCmd) {
                Write-Host "  Testing HTTPS endpoint: $endpointDescription" -ForegroundColor Yellow
                $httpsCmd = "ssh -i `"$($script:Config.ssh.local_key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"$httpsTestCmd`""
                $httpsResult = Invoke-Expression $httpsCmd

                if ($httpsResult -like "*200*" -or $httpsResult -like "*404*" -or $httpsResult -like "*302*") {
                    Write-Host "  ✅ HTTPS endpoint responding: $httpsResult" -ForegroundColor Green
                } elseif ($httpsResult -like "*Connection refused*" -or $httpsResult -like "*Could not resolve*") {
                    Write-Host "  ⚠️ HTTPS endpoint not accessible (service may not be running)" -ForegroundColor Yellow
                    Write-Host "    💡 This is normal if the auth-service hasn't been deployed yet" -ForegroundColor Cyan
                } else {
                    Write-Host "  ⚠️ HTTPS endpoint test result: $httpsResult" -ForegroundColor Yellow
                }
            } else {
                Write-Host "  ⚠️ Could not determine HTTPS endpoint for this environment" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "  ❌ Error testing HTTPS endpoint: $_" -ForegroundColor Red
        }
    } else {
        Write-Host "  ℹ️ Certificate setup not required for this environment" -ForegroundColor Cyan
    }

    Write-Host ""
    Write-Host "✅ Auth-service server readiness test completed!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Cyan
    Write-Host "- If dependencies are missing, run menu option 8 (Install Dependencies)" -ForegroundColor White
    Write-Host "- If all dependencies are available, run menu option 9 (Build Auth-Service)" -ForegroundColor White
    Write-Host ""
    Write-Host "Press Enter to continue..." -ForegroundColor Yellow
    Read-Host | Out-Null

    return $true
}

# Export the function
Export-ModuleMember -Function Test-ServerReadiness
