{"metadata": {"name": "auth-dev-requirements", "version": "1.0.0", "description": "Development environment requirements for auth-dev.chcit.org", "environment": "development", "target_os": "Ubuntu 24.04 LTS", "target_server": "auth-dev.chcit.org", "created": "2025-01-07", "updated": "2025-01-07"}, "system_requirements": {"minimum_specs": {"cpu_cores": 2, "memory_gb": 4, "disk_space_gb": 20, "network": "Stable internet connection for package installation"}, "recommended_specs": {"cpu_cores": 4, "memory_gb": 8, "disk_space_gb": 50, "network": "High-speed internet connection"}}, "operating_system": {"distribution": "Ubuntu", "version": "24.04", "codename": "<PERSON>", "kernel_minimum": "6.8.0", "architecture": "x86_64"}, "system_dependencies": {"essential_tools": [{"name": "build-essential", "package": "build-essential", "version_minimum": "12.10", "command": "sudo apt-get update && sudo apt-get install -y build-essential", "description": "Essential build tools including GCC 14.2 for C++23 compilation", "verification": "gcc --version | grep -E 'gcc.*14\\.[0-9]+'"}, {"name": "cmake", "package": "cmake", "version_minimum": "3.28", "command": "sudo apt-get install -y cmake", "description": "CMake build system for C++23 projects", "verification": "cmake --version | grep -E 'cmake version 3\\.[2-9][0-9]'"}, {"name": "pkg-config", "package": "pkg-config", "version_minimum": "1.8", "command": "sudo apt-get install -y pkg-config", "description": "Package configuration tool for library detection", "verification": "pkg-config --version"}], "cpp_libraries": [{"name": "boost", "package": "libboost-all-dev", "version_minimum": "1.83", "command": "sudo apt-get install -y libboost-all-dev", "description": "Boost C++ libraries (system, program_options, filesystem)", "verification": "dpkg -l | grep libboost-dev", "components": ["system", "program_options", "filesystem", "thread"]}, {"name": "postgresql-client", "package": "libpqxx-dev", "version_minimum": "7.7", "command": "sudo apt-get install -y libpqxx-dev libpq-dev", "description": "PostgreSQL C++ library for database connectivity", "verification": "pkg-config --modversion libpqxx"}, {"name": "openssl", "package": "libssl-dev", "version_minimum": "3.0", "command": "sudo apt-get install -y libssl-dev", "description": "OpenSSL development libraries for cryptography and TLS", "verification": "openssl version"}, {"name": "<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "package": "nlohmann-json3-dev", "version_minimum": "3.11", "command": "sudo apt-get install -y nlohmann-json3-dev", "description": "JSON for Modern C++ library", "verification": "pkg-config --modversion nlohmann_json"}], "security_libraries": [{"name": "argon2", "package": "libargon2-dev", "version_minimum": "0.0", "command": "sudo apt-get install -y libargon2-dev", "description": "Argon2 password hashing library for secure authentication", "verification": "pkg-config --modversion libargon2"}, {"name": "jwt-cpp", "package": "libjwt-dev", "version_minimum": "1.15", "command": "sudo apt-get install -y libjwt-dev", "description": "JWT (JSON Web Token) library for token management", "verification": "pkg-config --modversion libjwt"}], "http_libraries": [{"name": "curl", "package": "libcurl4-openssl-dev", "version_minimum": "8.5", "command": "sudo apt-get install -y libcurl4-openssl-dev", "description": "HTTP client library for API communications", "verification": "curl-config --version"}], "frontend_dependencies": [{"name": "nodejs", "package": "nodejs", "version_minimum": "18.0", "command": "curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash - && sudo apt-get install -y nodejs", "description": "Node.js JavaScript runtime for frontend development", "verification": "node --version"}, {"name": "npm", "package": "npm", "version_minimum": "9.0", "command": "sudo apt-get install -y npm", "description": "Node.js package manager", "verification": "npm --version"}, {"name": "nginx", "package": "nginx", "version_minimum": "1.18", "command": "sudo apt-get install -y nginx", "description": "Web server for frontend development and testing", "verification": "nginx -v"}], "frontend_build_tools": [{"name": "typescript", "package": "typescript", "install_method": "npm", "command": "sudo npm install -g typescript", "description": "TypeScript compiler for frontend development", "verification": "tsc --version"}, {"name": "vite", "package": "@vitejs/create-app", "install_method": "npm", "command": "sudo npm install -g @vitejs/create-app vite", "description": "Frontend build tool and development server", "verification": "npx vite --version"}]}, "database_requirements": {"postgresql": {"version_minimum": "16.0", "version_recommended": "17.0", "client_package": "postgresql-client", "dev_package": "libpq-dev", "install_command": "sudo apt-get install -y postgresql-client libpq-dev", "verification": "psql --version", "connection_test": "psql -h *********** -U auth_service_dev -d auth_service_dev -c 'SELECT version();'"}}, "cache_requirements": {"valkey": {"version_minimum": "7.0", "client_package": "redis-tools", "install_command": "sudo apt-get install -y redis-tools", "verification": "redis-cli --version", "connection_test": "redis-cli -h *********** ping", "description": "Valkey-compatible client tools for cache operations and development"}}, "runtime_requirements": {"system_user": {"username": "auth-service", "group": "auth-service", "home_directory": "/opt/auth-service", "shell": "/bin/bash", "create_command": "sudo useradd -r -s /bin/bash -d /opt/auth-service -m auth-service"}, "directories": [{"path": "/opt/auth-service", "owner": "auth-service", "group": "auth-service", "permissions": "755", "description": "Main application directory"}, {"path": "/opt/auth-service/bin", "owner": "auth-service", "group": "auth-service", "permissions": "755", "description": "Executable binaries"}, {"path": "/opt/auth-service/config", "owner": "auth-service", "group": "auth-service", "permissions": "750", "description": "Configuration files"}, {"path": "/opt/auth-service/logs", "owner": "auth-service", "group": "auth-service", "permissions": "755", "description": "Application logs"}, {"path": "/var/log/auth-service", "owner": "auth-service", "group": "auth-service", "permissions": "755", "description": "System logs"}], "systemd_service": {"service_name": "auth-service", "service_file": "/etc/systemd/system/auth-service.service", "user": "auth-service", "group": "auth-service", "working_directory": "/opt/auth-service", "executable": "/opt/auth-service/bin/auth-service"}}, "service_configuration": {"auth_service": {"name": "auth-service", "user": "auth-service", "group": "auth-service", "port": 8082, "install_dir": "/opt/auth-service", "log_dir": "/opt/auth-service/logs", "config_dir": "/opt/auth-service/config"}, "database": {"name": "auth_service_dev", "user": "auth_service_dev", "password": "secure_dev_password", "host": "***********", "port": 5432}}, "ssl_certificates": {"required": true, "domain": "chcit.org", "subdomain": "auth-dev", "full_domain": "auth-dev.chcit.org", "wildcard_cert": "chcit.org", "backup_location": "/home/<USER>/letsencrypt_backup/live/chcit.org", "standard_location": "/etc/letsencrypt/live/chcit.org", "sync_script": "/opt/auth-service/scripts/sync-auth-certificates.sh", "setup_script": "/opt/auth-service/scripts/setup-certificate-access.sh", "cron_schedule": "0 */6 * * *"}, "network_requirements": {"ports": [{"port": 8082, "protocol": "TCP", "description": "Auth-Service HTTP API", "required": true}, {"port": 22, "protocol": "TCP", "description": "SSH for deployment", "required": true}, {"port": 5432, "protocol": "TCP", "description": "PostgreSQL Database", "required": true}]}, "security_requirements": {"ssl_certificates": {"required": true, "location": "/etc/ssl/certs/auth-service", "nginx_integration": true}, "file_permissions": {"config_files": "640", "log_files": "644", "executable": "755", "ssl_certificates": "600"}, "firewall": {"ufw_rules": ["sudo ufw allow 8082/tcp comment 'Auth-Service API'", "sudo ufw allow 22/tcp comment 'SSH Access'", "sudo ufw allow from *********/24 to any port 5432 comment 'PostgreSQL Internal'"]}, "fail2ban": {"enabled": true, "jails": ["sshd", "nginx-http-auth"]}}, "performance_requirements": {"max_connections": 500, "memory_limit": "2GB", "log_rotation": {"enabled": true, "max_size": "50MB", "retention_days": 14}}, "verification_commands": {"system_check": ["lsb_release -a", "uname -r", "free -h", "df -h"], "dependency_check": ["gcc --version", "cmake --version", "pkg-config --version"], "library_check": ["pkg-config --list-all | grep boost", "pkg-config --modversion libpqxx", "openssl version", "pkg-config --modversion nlohmann_json"], "database_check": ["psql --version", "psql -h *********** -U auth_service_dev -d auth_service_dev -c 'SELECT version();'"], "cache_check": ["redis-cli --version", "redis-cli -h *********** ping"], "frontend_check": ["node --version", "npm --version", "nginx -v", "tsc --version", "npx vite --version"], "service_check": ["systemctl status auth-service", "curl -f http://localhost:8082/health || echo 'Service not responding'"], "certificate_check": ["id auth-service", "groups auth-service | grep ssl-cert", "ls -la /home/<USER>/letsencrypt_backup/live/auth-dev.chcit.org/", "openssl x509 -in /home/<USER>/letsencrypt_backup/live/auth-dev.chcit.org/cert.pem -noout -enddate", "crontab -l | grep sync-auth-certificates"]}, "troubleshooting": {"common_issues": [{"issue": "GCC version too old", "solution": "sudo apt-get update && sudo apt-get install gcc-14 g++-14", "verification": "gcc-14 --version"}, {"issue": "CMake version too old", "solution": "Install CMake from official repository or snap", "command": "sudo snap install cmake --classic"}, {"issue": "PostgreSQL connection failed", "solution": "Check PostgreSQL service and network connectivity", "commands": ["sudo systemctl status postgresql", "telnet *********** 5432"]}, {"issue": "SSL certificate access denied", "solution": "Run certificate setup script", "commands": ["sudo /opt/auth-service/scripts/setup-certificate-access.sh", "sudo /opt/auth-service/scripts/sync-auth-certificates.sh"]}]}}