{"metadata": {"name": "authbe-requirements", "version": "1.0.0", "description": "Backend production environment requirements for authbe.chcit.org", "environment": "backend-production", "target_os": "Ubuntu 24.04 LTS", "target_server": "authbe.chcit.org", "created": "2025-01-07", "updated": "2025-01-07"}, "system_requirements": {"minimum_specs": {"cpu_cores": 4, "memory_gb": 8, "disk_space_gb": 50, "network": "Stable internet connection for package installation"}, "recommended_specs": {"cpu_cores": 8, "memory_gb": 16, "disk_space_gb": 100, "network": "High-speed internet connection"}}, "operating_system": {"distribution": "Ubuntu", "version": "24.04", "codename": "<PERSON>", "kernel_minimum": "6.8.0", "architecture": "x86_64"}, "system_dependencies": {"essential_runtime": [{"name": "curl", "package": "curl", "version_minimum": "7.81", "command": "sudo apt-get install -y curl", "description": "HTTP client for health checks and API calls", "verification": "curl --version"}], "web_server": [{"name": "nginx", "package": "nginx", "version_minimum": "1.18", "command": "sudo apt-get install -y nginx", "description": "Web server for SSL termination and reverse proxy", "verification": "nginx -v"}]}, "database_requirements": {"postgresql": {"version_minimum": "16.0", "version_recommended": "17.0", "server_package": "postgresql-17", "client_package": "postgresql-client-17", "contrib_package": "postgresql-contrib-17", "install_command": "sudo apt-get install -y postgresql-17 postgresql-client-17 postgresql-contrib-17", "verification": "psql --version && sudo systemctl status postgresql", "connection_test": "sudo -u postgres psql -c 'SELECT version();'", "configuration": {"listen_addresses": "localhost", "port": "5432", "max_connections": "100", "shared_buffers": "128MB", "description": "PostgreSQL server co-located with auth-service for optimal performance"}}}, "cache_requirements": {"valkey": {"version_minimum": "7.0", "server_package": "valkey-server", "client_package": "redis-tools", "install_command": "sudo apt-get install -y valkey-server redis-tools", "fallback_install": "sudo apt-get install -y redis-server redis-tools", "verification": "redis-cli --version && sudo systemctl status valkey-server", "connection_test": "redis-cli ping", "service_name": "valkey-server", "configuration": {"bind": "127.0.0.1", "port": "6379", "maxmemory": "256mb", "maxmemory_policy": "allkeys-lru", "description": "Valkey server co-located with auth-service for caching"}, "note": "Valkey server co-located with backend for optimal performance"}}, "runtime_requirements": {"system_user": {"username": "auth-service", "group": "auth-service", "home_directory": "/opt/auth-service", "shell": "/bin/bash", "create_command": "sudo useradd -r -s /bin/bash -d /opt/auth-service -m auth-service"}, "directories": [{"path": "/opt/auth-service", "owner": "auth-service", "group": "auth-service", "permissions": "755", "description": "Main application directory"}, {"path": "/opt/auth-service/bin", "owner": "auth-service", "group": "auth-service", "permissions": "755", "description": "Executable binaries"}, {"path": "/opt/auth-service/config", "owner": "auth-service", "group": "auth-service", "permissions": "750", "description": "Configuration files"}, {"path": "/opt/auth-service/logs", "owner": "auth-service", "group": "auth-service", "permissions": "755", "description": "Application logs"}, {"path": "/var/log/auth-service", "owner": "auth-service", "group": "auth-service", "permissions": "755", "description": "System logs"}], "systemd_service": {"service_name": "auth-service", "service_file": "/etc/systemd/system/auth-service.service", "user": "auth-service", "group": "auth-service", "working_directory": "/opt/auth-service", "executable": "/opt/auth-service/bin/auth-service"}}, "service_configuration": {"auth_service": {"name": "auth-service", "user": "auth-service", "group": "auth-service", "port": 8082, "install_dir": "/opt/auth-service", "log_dir": "/opt/auth-service/logs", "config_dir": "/opt/auth-service/config"}, "database": {"name": "auth_service_prod", "user": "auth_service_prod", "password": "secure_prod_password", "host": "***********", "port": 5432}}, "ssl_certificates": {"required": true, "domain": "chcit.org", "subdomain": "auth", "full_domain": "auth.chcit.org", "wildcard_cert": "chcit.org", "backup_location": "/home/<USER>/letsencrypt_backup/live/chcit.org", "standard_location": "/etc/letsencrypt/live/chcit.org", "sync_script": "/opt/auth-service/scripts/sync-auth-certificates.sh", "setup_script": "/opt/auth-service/scripts/setup-certificate-access.sh", "cron_schedule": "0 */6 * * *"}, "network_requirements": {"ports": [{"port": 8082, "protocol": "TCP", "description": "Auth-Service HTTP API", "required": true}, {"port": 22, "protocol": "TCP", "description": "SSH for deployment", "required": true}, {"port": 5432, "protocol": "TCP", "description": "PostgreSQL Database", "required": true}]}, "security_requirements": {"ssl_certificates": {"required": true, "location": "/etc/ssl/certs/auth-service", "nginx_integration": true}, "file_permissions": {"config_files": "640", "log_files": "644", "executable": "755", "ssl_certificates": "600"}, "firewall": {"ufw_rules": ["sudo ufw allow 8082/tcp comment 'Auth-Service API'", "sudo ufw allow 22/tcp comment 'SSH Access'", "sudo ufw allow from *********/24 to any port 5432 comment 'PostgreSQL Internal'"]}, "fail2ban": {"enabled": true, "jails": ["sshd", "nginx-http-auth"]}}, "performance_requirements": {"max_connections": 1000, "memory_limit": "4GB", "log_rotation": {"enabled": true, "max_size": "100MB", "retention_days": 30}}, "verification_commands": {"system_check": ["lsb_release -a", "uname -r", "free -h", "df -h"], "runtime_check": ["curl --version", "nginx -v", "systemctl --version"], "database_check": ["systemctl status postgresql", "psql --version", "sudo -u postgres psql -c 'SELECT version();'"], "cache_check": ["redis-cli --version", "sudo systemctl status valkey || sudo systemctl status redis", "redis-cli ping"], "service_check": ["systemctl status auth-service", "curl -f http://localhost:8082/health || echo 'Service not responding'"], "certificate_check": ["id auth-service", "groups auth-service | grep ssl-cert", "ls -la /home/<USER>/letsencrypt_backup/live/auth.chcit.org/", "openssl x509 -in /home/<USER>/letsencrypt_backup/live/auth.chcit.org/cert.pem -noout -enddate", "crontab -l | grep sync-auth-certificates"]}, "troubleshooting": {"common_issues": [{"issue": "Auth-service not starting", "solution": "Check service status and logs", "commands": ["sudo systemctl status auth-service", "sudo journalctl -u auth-service -f", "sudo tail -f /opt/auth-service/logs/auth-service.log"]}, {"issue": "PostgreSQL connection failed", "solution": "Check PostgreSQL service and network connectivity", "commands": ["sudo systemctl status postgresql", "telnet *********** 5432", "sudo -u postgres psql -c 'SELECT version();'"]}, {"issue": "SSL certificate access denied", "solution": "Run certificate setup script", "commands": ["sudo /opt/auth-service/scripts/setup-certificate-access.sh", "sudo /opt/auth-service/scripts/sync-auth-certificates.sh"]}, {"issue": "High memory usage in production", "solution": "Monitor and optimize memory allocation", "commands": ["free -h", "ps aux --sort=-%mem | head -10", "systemctl status auth-service"]}, {"issue": "Port 8082 already in use", "solution": "Check what's using the port and stop conflicting service", "commands": ["sudo netstat -tulpn | grep :8082", "sudo lsof -i :8082", "sudo systemctl stop conflicting-service"]}]}}