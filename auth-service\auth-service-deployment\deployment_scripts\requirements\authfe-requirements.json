{"metadata": {"name": "authfe-requirements", "version": "1.0.0", "description": "Frontend production environment requirements for authfe.chcit.org", "environment": "frontend-production", "target_os": "Ubuntu 24.04 LTS", "target_server": "authfe.chcit.org", "created": "2025-01-07", "updated": "2025-01-07"}, "system_requirements": {"minimum_specs": {"cpu_cores": 2, "memory_gb": 4, "disk_space_gb": 30}, "recommended_specs": {"cpu_cores": 4, "memory_gb": 8, "disk_space_gb": 60}}, "system_dependencies": {"web_server": [{"name": "nginx", "package": "nginx", "version_minimum": "1.18", "version_command": "nginx -v", "install_command": "sudo apt-get install -y nginx"}], "runtime_tools": [{"name": "curl", "package": "curl", "version_minimum": "7.81", "version_command": "curl --version", "install_command": "sudo apt-get install -y curl", "description": "HTTP client for health checks and API calls"}]}, "service_configuration": {"auth_ui": {"name": "auth-ui", "user": "auth-service", "group": "auth-service", "port": 3000, "install_dir": "/opt/auth-service-ui", "log_dir": "/opt/auth-service-ui/logs", "config_dir": "/opt/auth-service-ui/config", "web_root": "/var/www/auth-service"}, "nginx": {"sites_available": "/etc/nginx/sites-available", "sites_enabled": "/etc/nginx/sites-enabled", "web_root": "/var/www/auth-service", "log_directory": "/var/log/nginx"}}, "ssl_certificates": {"required": true, "domain": "chcit.org", "subdomain": "auth", "full_domain": "auth.chcit.org", "wildcard_cert": "chcit.org", "backup_location": "/home/<USER>/letsencrypt_backup/live/chcit.org", "standard_location": "/etc/letsencrypt/live/chcit.org", "sync_script": "/opt/auth-service/scripts/sync-auth-certificates.sh", "setup_script": "/opt/auth-service/scripts/setup-certificate-access.sh", "cron_schedule": "0 */6 * * *"}, "network_requirements": {"ports": [{"port": 3000, "protocol": "TCP", "description": "Auth-UI Application", "required": true}, {"port": 80, "protocol": "TCP", "description": "HTTP (redirect to HTTPS)", "required": true}, {"port": 443, "protocol": "TCP", "description": "HTTPS", "required": true}, {"port": 22, "protocol": "TCP", "description": "SSH for deployment", "required": true}]}, "nginx_configuration": {"server_name": "auth.chcit.org", "ssl_protocols": ["TLSv1.2", "TLSv1.3"], "security_headers": {"hsts": true, "content_security_policy": true, "x_frame_options": "DENY", "x_content_type_options": "nosniff"}, "proxy_settings": {"backend_url": "https://authbe.chcit.org:8082", "timeout": 30, "buffer_size": "4k"}}, "security_requirements": {"firewall": {"ufw_rules": ["sudo ufw allow 80/tcp comment 'HTTP'", "sudo ufw allow 443/tcp comment 'HTTPS'", "sudo ufw allow 3000/tcp comment 'Auth-UI'", "sudo ufw allow 22/tcp comment 'SSH Access'"]}, "fail2ban": {"enabled": true, "jails": ["sshd", "nginx-http-auth", "nginx-limit-req"]}}, "performance_requirements": {"static_file_caching": true, "gzip_compression": true, "max_file_size": "10MB", "log_rotation": {"enabled": true, "max_size": "50MB", "retention_days": 14}}, "verification_commands": {"system_check": ["lsb_release -a", "uname -r", "free -h", "df -h"], "dependency_check": ["nginx -v", "curl --version"], "runtime_check": ["systemctl --version", "curl --version"], "service_check": ["systemctl status nginx", "systemctl status auth-ui", "curl -f http://localhost:3000 || echo 'UI not responding'"], "certificate_check": ["id auth-service", "groups auth-service | grep ssl-cert", "ls -la /home/<USER>/letsencrypt_backup/live/auth.chcit.org/", "openssl x509 -in /home/<USER>/letsencrypt_backup/live/auth.chcit.org/cert.pem -noout -enddate", "crontab -l | grep sync-auth-certificates"], "network_check": ["ss -tlnp | grep :3000", "ss -tlnp | grep :80", "ss -tlnp | grep :443", "curl -I https://auth.chcit.org"], "nginx_check": ["nginx -t", "systemctl status nginx", "curl -I http://localhost"]}}