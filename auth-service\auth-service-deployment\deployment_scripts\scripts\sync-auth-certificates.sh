#!/bin/bash

# SSL Certificate Sync Script for Auth-Service
# Uses shared LetsEncrypt backup structure (same as git.chcit.org and db.chcit.org)
# Based on the proven sync-certificates.sh system
#
# COPY TO: D:\Coding_Projects\auth-service\cert_sync_helper_app\
# DEPLOY TO: /opt/auth-service/scripts/sync-auth-certificates.sh

# Configuration
MAIN_SERVER="project-tracker.chcit.org"
MAIN_SERVER_USER="ssl-sync"
CERT_DIR="/etc/letsencrypt"
LOG_DIR="/home/<USER>/logs"
SYNC_LOG="$LOG_DIR/auth-cert-sync.log"
SSH_KEY="/home/<USER>/.ssh/id_ed25519_cert_sync"

# Target servers for each environment
declare -A AUTH_SERVERS=(
    ["development"]="auth-dev.chcit.org"
    ["backend-production"]="authbe.chcit.org"
    ["frontend-production"]="authfe.chcit.org"
)

# Use wildcard certificate for all environments - simplifies certificate management
# The wildcard *.chcit.org certificate works for all auth-service subdomains
CERT_DOMAIN="chcit.org"

# Service ports for each environment
declare -A AUTH_PORTS=(
    ["development"]="8082"
    ["backend-production"]="8082"
    ["frontend-production"]="3000"
)

# Service names for each environment
declare -A SERVICE_NAMES=(
    ["development"]="auth-service"
    ["backend-production"]="auth-service"
    ["frontend-production"]="auth-ui"
)

# Shared certificate backup directory (consistent with SSL-Sync infrastructure)
BACKUP_DIR="/home/<USER>/letsencrypt_backup"

# Function to log messages
log() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] $1"
    echo "[$timestamp] $1" >> "$SYNC_LOG" 2>/dev/null || true
}

# Function to set environment from parameter or default to development
set_environment() {
    local env_param="${1:-development}"

    # Map environment parameter to our internal environment names
    case "$env_param" in
        "auth-dev"|"development"|"dev")
            CURRENT_ENV="development"
            ;;
        "authbe"|"backend-production"|"backend"|"production-backend")
            CURRENT_ENV="backend-production"
            ;;
        "authfe"|"frontend-production"|"frontend"|"production-frontend")
            CURRENT_ENV="frontend-production"
            ;;
        *)
            # Default to development if unknown
            CURRENT_ENV="development"
            log "Unknown environment '$env_param', defaulting to development"
            ;;
    esac

    # Using wildcard certificate for all environments - no domain mapping needed
    log "Environment: $CURRENT_ENV, Certificate Domain: $CERT_DOMAIN"
    return 0
}

# Function to verify certificate files exist on remote server
verify_remote_certificates() {
    log "Verifying remote certificates on $MAIN_SERVER for domain: $CERT_DOMAIN"

    local cert_path="$CERT_DIR/live/$CERT_DOMAIN"
    local required_files=("cert.pem" "privkey.pem" "chain.pem" "fullchain.pem")

    for file in "${required_files[@]}"; do
        if ! ssh -i "$SSH_KEY" -o StrictHostKeyChecking=no "$MAIN_SERVER_USER@$MAIN_SERVER" "test -f '$cert_path/$file'" 2>/dev/null; then
            log "ERROR: Required certificate file not found on remote server: $cert_path/$file"
            return 1
        fi
    done
    
    # Check certificate expiry
    local expiry_date=$(openssl x509 -in "$cert_path/cert.pem" -noout -enddate | cut -d= -f2)
    local expiry_epoch=$(date -d "$expiry_date" +%s)
    local current_epoch=$(date +%s)
    local days_until_expiry=$(( (expiry_epoch - current_epoch) / 86400 ))
    
    log "Certificate expires in $days_until_expiry days ($expiry_date)"
    
    if [[ $days_until_expiry -lt 30 ]]; then
        log "WARNING: Certificate expires in less than 30 days!"
    fi
    
    return 0
}

# Function to pull certificates from project-tracker to this auth server
pull_certificates_from_source() {
    log "Pulling certificates from $MAIN_SERVER for domain: $CERT_DOMAIN"
    log "Target environment: $CURRENT_ENV"

    # Test SSH connectivity to source server
    if ! ssh -i "$SSH_KEY" -o ConnectTimeout=10 -o BatchMode=yes "$MAIN_SERVER_USER@$MAIN_SERVER" "echo 'SSH connection successful'" >/dev/null 2>&1; then
        log "ERROR: SSH connection failed to $MAIN_SERVER"
        return 1
    fi

    # Create local backup directory structure
    log "Creating local backup directory structure..."
    mkdir -p "$BACKUP_DIR/live" "$BACKUP_DIR/archive" "$LOG_DIR"

    if [[ $? -ne 0 ]]; then
        log "ERROR: Failed to create local backup directories"
        return 1
    fi

    # Step 1: Pull certificates using rsync (pull from source)
    log "Pulling certificates from $MAIN_SERVER using rsync"

    # Pull archive directory for the wildcard certificate domain
    log "Pulling archive directory for domain: $CERT_DOMAIN"
    if [ "$(id -un)" = "ssl-sync" ]; then
        rsync -avz --timeout=30 -e "ssh -i $SSH_KEY" "${MAIN_SERVER_USER}@${MAIN_SERVER}:$CERT_DIR/archive/$CERT_DOMAIN/" "$BACKUP_DIR/archive/$CERT_DOMAIN/"
    else
        sudo -u ssl-sync rsync -avz --timeout=30 -e "ssh -i $SSH_KEY" "${MAIN_SERVER_USER}@${MAIN_SERVER}:$CERT_DIR/archive/$CERT_DOMAIN/" "$BACKUP_DIR/archive/$CERT_DOMAIN/"
    fi

    # Check if rsync was successful
    if [ $? -ne 0 ]; then
        log "ERROR: Failed to pull archive directory from $MAIN_SERVER"
        return 1
    fi

    # Pull live directory for the wildcard certificate domain - we need the files, not the symlinks
    log "Pulling live directory for domain: $CERT_DOMAIN"
    if [ "$(id -un)" = "ssl-sync" ]; then
        rsync -avzL --timeout=30 -e "ssh -i $SSH_KEY" "${MAIN_SERVER_USER}@${MAIN_SERVER}:$CERT_DIR/live/$CERT_DOMAIN/" "$BACKUP_DIR/live/$CERT_DOMAIN/"
    else
        sudo -u ssl-sync rsync -avzL --timeout=30 -e "ssh -i $SSH_KEY" "${MAIN_SERVER_USER}@${MAIN_SERVER}:$CERT_DIR/live/$CERT_DOMAIN/" "$BACKUP_DIR/live/$CERT_DOMAIN/"
    fi

    # Check if rsync was successful
    if [ $? -ne 0 ]; then
        log "ERROR: Failed to pull live directory from $MAIN_SERVER"
        return 1
    fi

    # Step 2: Verify local certificate installation
    log "Verifying local certificate installation for domain $CERT_DOMAIN..."
    local cert_files=("cert.pem" "privkey.pem" "chain.pem" "fullchain.pem")
    local cert_ok=true

    for file in "${cert_files[@]}"; do
        if [[ ! -f "$BACKUP_DIR/live/$CERT_DOMAIN/$file" ]]; then
            log "ERROR: Certificate file missing: $BACKUP_DIR/live/$CERT_DOMAIN/$file"
            cert_ok=false
        fi
    done

    if [[ "$cert_ok" != "true" ]]; then
        log "ERROR: Local certificate verification failed"
        return 1
    fi

    # Step 3: Set proper permissions on backup directory (using ssl-sync user and sslcert group)
    log "Setting backup certificate permissions for domain $CERT_DOMAIN..."
    chown -R ssl-sync "$BACKUP_DIR" 2>/dev/null || sudo chown -R ssl-sync "$BACKUP_DIR"
    chgrp -R sslcert "$BACKUP_DIR" 2>/dev/null || sudo chgrp -R sslcert "$BACKUP_DIR"
    chmod -R 755 "$BACKUP_DIR"
    chmod 600 "$BACKUP_DIR/live/$CERT_DOMAIN/privkey.pem" 2>/dev/null || sudo chmod 600 "$BACKUP_DIR/live/$CERT_DOMAIN/privkey.pem"

    # Step 4: Copy certificates to /etc/letsencrypt (where nginx expects them)
    log "Copying certificates to /etc/letsencrypt for nginx access..."

    # Create /etc/letsencrypt directory structure with proper permissions
    # Check if we can write to /etc/letsencrypt or need sudo
    if [ -w "/etc" ] || [ "$(id -u)" = "0" ]; then
        # We have permission or are root
        mkdir -p "/etc/letsencrypt/live/$CERT_DOMAIN" "/etc/letsencrypt/archive/$CERT_DOMAIN"
        cp -r "$BACKUP_DIR/archive/$CERT_DOMAIN/"* "/etc/letsencrypt/archive/$CERT_DOMAIN/"

        # Set permissions to match project-tracker server
        chown -R root:ssl-cert "/etc/letsencrypt" 2>/dev/null || true
        chmod 755 "/etc/letsencrypt"
        chmod 750 "/etc/letsencrypt/live"
        chmod 755 "/etc/letsencrypt/live/$CERT_DOMAIN"
        chmod 750 "/etc/letsencrypt/archive"
        chmod 755 "/etc/letsencrypt/archive/$CERT_DOMAIN"

        # Set permissions on certificate files
        chmod 644 "/etc/letsencrypt/archive/$CERT_DOMAIN/"*.pem
        chmod 600 "/etc/letsencrypt/archive/$CERT_DOMAIN/privkey"*.pem
    else
        # Need sudo - check if ssl-sync user has sudo permissions
        if sudo -n true 2>/dev/null; then
            log "Using sudo for /etc/letsencrypt operations..."
            sudo mkdir -p "/etc/letsencrypt/live/$CERT_DOMAIN" "/etc/letsencrypt/archive/$CERT_DOMAIN"
            sudo cp -r "$BACKUP_DIR/archive/$CERT_DOMAIN/"* "/etc/letsencrypt/archive/$CERT_DOMAIN/"

            # Set permissions to match project-tracker server
            sudo chown -R root:ssl-cert "/etc/letsencrypt"
            sudo chmod 755 "/etc/letsencrypt"
            sudo chmod 750 "/etc/letsencrypt/live"
            sudo chmod 755 "/etc/letsencrypt/live/$CERT_DOMAIN"
            sudo chmod 750 "/etc/letsencrypt/archive"
            sudo chmod 755 "/etc/letsencrypt/archive/$CERT_DOMAIN"

            # Set permissions on certificate files
            sudo chmod 644 "/etc/letsencrypt/archive/$CERT_DOMAIN/"*.pem
            sudo chmod 600 "/etc/letsencrypt/archive/$CERT_DOMAIN/privkey"*.pem
        else
            log "ERROR: Cannot write to /etc/letsencrypt and sudo not available"
            log "Certificates are available in backup location: $BACKUP_DIR"
            return 1
        fi
    fi

    # Create symlinks in live directory (like certbot does)
    log "Creating certificate symlinks in /etc/letsencrypt/live/$CERT_DOMAIN/"

    # Find the latest certificate files (highest number) - check if files exist first
    if [ ! -d "/etc/letsencrypt/archive/$CERT_DOMAIN" ]; then
        log "ERROR: Archive directory not found: /etc/letsencrypt/archive/$CERT_DOMAIN"
        return 1
    fi

    # Find latest certificate number
    local latest_cert=$(ls "/etc/letsencrypt/archive/$CERT_DOMAIN/cert"*.pem 2>/dev/null | sort -V | tail -1)
    if [ -z "$latest_cert" ]; then
        log "ERROR: No certificate files found in archive directory"
        return 1
    fi

    local latest_num=$(basename "$latest_cert" | sed 's/cert\([0-9]*\)\.pem/\1/')
    log "Using certificate version: $latest_num"

    # Change to live directory and create symlinks
    cd "/etc/letsencrypt/live/$CERT_DOMAIN" || {
        log "ERROR: Cannot access live directory: /etc/letsencrypt/live/$CERT_DOMAIN"
        return 1
    }

    # Remove old symlinks and create new ones
    rm -f cert.pem chain.pem fullchain.pem privkey.pem 2>/dev/null || true

    # Create symlinks with error checking
    ln -sf "../../archive/$CERT_DOMAIN/cert${latest_num}.pem" cert.pem || {
        log "ERROR: Failed to create cert.pem symlink"
        return 1
    }
    ln -sf "../../archive/$CERT_DOMAIN/chain${latest_num}.pem" chain.pem || {
        log "ERROR: Failed to create chain.pem symlink"
        return 1
    }
    ln -sf "../../archive/$CERT_DOMAIN/fullchain${latest_num}.pem" fullchain.pem || {
        log "ERROR: Failed to create fullchain.pem symlink"
        return 1
    }
    ln -sf "../../archive/$CERT_DOMAIN/privkey${latest_num}.pem" privkey.pem || {
        log "ERROR: Failed to create privkey.pem symlink"
        return 1
    }

    # Set proper ownership on symlinks (only if we have permission)
    chown -h root:ssl-cert "/etc/letsencrypt/live/$CERT_DOMAIN/"*.pem 2>/dev/null || {
        log "WARNING: Could not set ownership on symlinks (may need sudo)"
    }

    log "Certificates successfully copied to /etc/letsencrypt/"

    # Verify /etc/letsencrypt certificate installation
    log "Verifying /etc/letsencrypt certificate installation..."
    local etc_cert_files=("cert.pem" "privkey.pem" "chain.pem" "fullchain.pem")
    local etc_cert_ok=true

    for file in "${etc_cert_files[@]}"; do
        if [[ ! -f "/etc/letsencrypt/live/$CERT_DOMAIN/$file" ]]; then
            log "ERROR: Certificate file missing in /etc/letsencrypt: /etc/letsencrypt/live/$CERT_DOMAIN/$file"
            etc_cert_ok=false
        fi
    done

    if [[ "$etc_cert_ok" != "true" ]]; then
        log "ERROR: /etc/letsencrypt certificate verification failed"
        return 1
    fi

    log "✅ All certificate files verified in /etc/letsencrypt/"

    # Step 5: Reload appropriate service if it's running
    local service_name=${SERVICE_NAMES[$CURRENT_ENV]}
    log "Reloading $service_name locally..."
    if sudo systemctl is-active --quiet "$service_name" 2>/dev/null; then
        sudo systemctl reload "$service_name"
        log "Service reloaded successfully: $service_name"
    else
        log "Service is not running, skipping reload: $service_name"
    fi

    # Step 6: Test HTTPS endpoint
    local subdomain=""
    local port=${AUTH_PORTS[$CURRENT_ENV]}

    case "$CURRENT_ENV" in
        "development")
            subdomain="auth-dev"
            ;;
        "backend-production")
            subdomain="authbe"
            ;;
        "frontend-production")
            subdomain="authfe"
            ;;
        *)
            log "WARNING: Unknown environment $CURRENT_ENV, skipping endpoint test"
            return 0
            ;;
    esac

    local test_url="https://$subdomain.chcit.org:$port"
    log "Testing HTTPS endpoint: $test_url"
    
    # Give the service a moment to reload
    sleep 2
    
    if curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 "$test_url" >/dev/null 2>&1; then
        log "HTTPS endpoint test successful for $test_url"
    else
        log "WARNING: HTTPS endpoint test failed for $test_url (service may not be running)"
    fi
    
    log "Certificate sync to $env environment completed successfully"
    return 0
}

# Function to sync certificates to all auth-service environments
sync_all_environments() {
    log "Starting auth-service certificate synchronization to all environments"
    
    local overall_success=true
    
    for env in "${!AUTH_SERVERS[@]}"; do
        if ! sync_to_auth_server "$env"; then
            overall_success=false
            log "ERROR: Failed to sync certificates to $env environment"
        fi
    done
    
    if $overall_success; then
        log "All auth-service certificate synchronizations completed successfully"
        return 0
    else
        log "Some auth-service certificate synchronizations failed"
        return 1
    fi
}

# Function to display usage information
usage() {
    echo "Usage: $0 [environment]"
    echo ""
    echo "Environments:"
    echo "  development          - Sync to development server (auth-dev.chcit.org)"
    echo "  backend-production   - Sync to backend production server (authbe.chcit.org)"
    echo "  frontend-production  - Sync to frontend production server (authfe.chcit.org)"
    echo "  all                  - Sync to all environments (default)"
    echo ""
    echo "Examples:"
    echo "  $0                           # Sync to all environments"
    echo "  $0 all                       # Sync to all environments"
    echo "  $0 development               # Sync to development only"
    echo "  $0 backend-production        # Sync to backend production only"
    echo "  $0 frontend-production       # Sync to frontend production only"
    echo ""
    echo "Certificate Location:"
    echo "  Certificates will be available at: $BACKUP_DIR/live/$CERT_DOMAIN/"
    echo "  Same location as git.chcit.org and db.chcit.org certificates"
    echo ""
    echo "Service Details:"
    echo "  Development: auth-service on port 8082"
    echo "  Backend Production: auth-service on port 8082"
    echo "  Frontend Production: auth-ui on port 3000"
}

# Main execution
main() {
    # Create log directory if it doesn't exist
    mkdir -p "$LOG_DIR" 2>/dev/null || true

    log "Auth-service certificate sync script started"
    log "Using shared LetsEncrypt backup structure: $BACKUP_DIR"

    # Set environment (default to development)
    if ! set_environment "${1:-development}"; then
        exit 1
    fi

    # Verify remote certificates exist
    if ! verify_remote_certificates; then
        log "ERROR: Remote certificate verification failed"
        exit 1
    fi

    # Pull certificates for this server (no arguments needed - auto-detected)
    log "Pulling certificates for current environment: $CURRENT_ENV"

    if ! pull_certificates_from_source; then
        log "ERROR: Certificate pull failed for $CURRENT_ENV environment"
        exit 1
    fi
    
    log "Auth-service certificate sync script completed successfully"
    log "Certificates available at: $BACKUP_DIR/live/$CERT_DOMAIN/"
}

# Run main function with all arguments
main "$@"
