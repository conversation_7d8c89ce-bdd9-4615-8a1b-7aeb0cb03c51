#!/bin/bash

# Simple React UI Build Script
# Builds the React/TypeScript UI for the auth-service

echo "🔨 Building React/TypeScript Auth Service UI..."

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
REACT_UI_DIR="$PROJECT_ROOT"
BUILD_DIR="$REACT_UI_DIR/build"
DEPLOY_DIR="/opt/auth-service-ui/react"

echo "📁 Project root: $PROJECT_ROOT"
echo "📁 React UI directory: $REACT_UI_DIR"

# Check if we're in the right directory
if [ ! -f "$REACT_UI_DIR/package.json" ]; then
    echo "❌ Error: package.json not found in $REACT_UI_DIR"
    echo "Please run this script from the auth-service-ui directory"
    exit 1
fi

# Check for Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Error: Node.js not found"
    echo "Please install Node.js 18+ from https://nodejs.org/"
    exit 1
fi

# Check for npm
if ! command -v npm &> /dev/null; then
    echo "❌ Error: npm not found"
    echo "npm should be installed with Node.js"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo "❌ Error: Node.js version 16+ required (found: $(node --version))"
    exit 1
fi

echo "✅ Node.js version: $(node --version)"
echo "✅ npm version: $(npm --version)"

# Navigate to React UI directory
cd "$REACT_UI_DIR"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

# Run type checking
echo "🔍 Running TypeScript type checking..."
npm run type-check

if [ $? -ne 0 ]; then
    echo "⚠️  TypeScript type checking failed, but continuing with build..."
fi

# Build the React application
echo "🔨 Building React application..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build failed!"
    exit 1
fi

# Check if build directory exists
if [ ! -d "$BUILD_DIR" ]; then
    echo "❌ Build directory not found: $BUILD_DIR"
    exit 1
fi

echo "✅ React application built successfully!"
echo "📁 Build output: $BUILD_DIR"

# Optional: Deploy to server directory
read -p "🚀 Deploy to $DEPLOY_DIR? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🚀 Deploying to server directory..."
    
    # Create deploy directory if it doesn't exist
    sudo mkdir -p "$DEPLOY_DIR"
    
    # Copy build files
    sudo cp -r "$BUILD_DIR"/* "$DEPLOY_DIR/"
    
    # Set proper permissions
    sudo chown -R www-data:www-data "$DEPLOY_DIR"
    sudo chmod -R 755 "$DEPLOY_DIR"
    
    echo "✅ Deployed to $DEPLOY_DIR"
    echo "🌐 Configure nginx to serve from this directory"
else
    echo "📋 Build completed. Manual deployment required."
    echo "📁 Copy contents of $BUILD_DIR to your web server directory"
fi

echo ""
echo "🎉 Build process completed!"
echo "📋 Next steps:"
echo "   1. Configure nginx to serve the React app"
echo "   2. Set up API proxy to auth-service backend"
echo "   3. Test the application"
