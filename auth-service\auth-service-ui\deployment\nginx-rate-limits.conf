# Rate Limiting Configuration for Auth Service
# Include this in the main nginx.conf or in the server block

# Define rate limiting zones
# These should be placed in the http block of nginx.conf

# General API rate limiting - 10 requests per second per IP
limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;

# OAuth token endpoint - stricter limits (5 requests per minute per IP)
limit_req_zone $binary_remote_addr zone=oauth_token:10m rate=5r/m;

# Login attempts - very strict (3 attempts per minute per IP)
limit_req_zone $binary_remote_addr zone=login_limit:10m rate=3r/m;

# Admin interface - moderate limits (20 requests per minute per IP)
limit_req_zone $binary_remote_addr zone=admin_limit:10m rate=20r/m;

# Health check - more lenient (1 request per second per IP)
limit_req_zone $binary_remote_addr zone=health_limit:10m rate=1r/s;

# Rate limiting rules for server block
# Add these location blocks to your auth-dev.chcit.org server configuration

# OAuth token endpoint - strictest limits
location /oauth/token {
    limit_req zone=oauth_token burst=2 nodelay;
    limit_req_status 429;
    
    # Custom error page for rate limiting
    error_page 429 @rate_limit_error;
    
    proxy_pass http://localhost:8082/oauth/token;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

# Other OAuth endpoints - moderate limits
location /oauth/ {
    limit_req zone=api_limit burst=5 nodelay;
    limit_req_status 429;
    
    error_page 429 @rate_limit_error;
    
    proxy_pass http://localhost:8082/oauth/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

# Admin interface rate limiting
location /admin.html {
    limit_req zone=admin_limit burst=10 nodelay;
    limit_req_status 429;
    
    error_page 429 @admin_rate_limit_error;
    
    try_files $uri =404;
}

# Health check endpoint
location /health {
    limit_req zone=health_limit burst=3 nodelay;
    limit_req_status 429;
    
    proxy_pass http://localhost:8082/health;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

# Rate limit error pages
location @rate_limit_error {
    add_header Content-Type application/json always;
    add_header Retry-After 60 always;
    return 429 '{"error":"rate_limit_exceeded","error_description":"Too many requests. Please try again later.","retry_after":60}';
}

location @admin_rate_limit_error {
    add_header Content-Type text/html always;
    add_header Retry-After 60 always;
    return 429 '<!DOCTYPE html><html><head><title>Rate Limited</title></head><body><h1>Too Many Requests</h1><p>Please wait before accessing the admin interface again.</p></body></html>';
}

# Additional security measures
# Block common attack patterns
location ~ /\.(env|git|svn) {
    deny all;
    access_log off;
    log_not_found off;
}

# Block SQL injection attempts
location ~ (union|select|insert|delete|update|drop|create|alter|exec|script) {
    deny all;
    access_log off;
    log_not_found off;
}

# Logging for rate limiting
# Add to your main server block
access_log /var/log/nginx/auth-dev.access.log combined;
error_log /var/log/nginx/auth-dev.error.log warn;

# Log rate limiting events specifically
log_format rate_limit '$remote_addr - $remote_user [$time_local] '
                     '"$request" $status $body_bytes_sent '
                     '"$http_referer" "$http_user_agent" '
                     'rt=$request_time uct="$upstream_connect_time" '
                     'uht="$upstream_header_time" urt="$upstream_response_time"';

# Use this log format for rate-limited endpoints
access_log /var/log/nginx/auth-dev.rate-limit.log rate_limit;
