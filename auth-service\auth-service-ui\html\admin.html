<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth Service - Admin Dashboard</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0f1419 0%, #1a202c 50%, #2d3748 100%);
            min-height: 100vh;
            color: #e2e8f0;
        }
        
        .header {
            background: linear-gradient(135deg, #1a365d 0%, #2c5282 50%, #3182ce 100%);
            color: white;
            padding: 1.5rem 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .header .btn {
            width: auto;
            padding: 0.75rem 1.5rem;
            font-size: 0.9rem;
            margin: 0;
        }
        
        .header h1 {
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            opacity: 0.9;
            font-size: 0.95rem;
            color: #bee3f8;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .nav-tabs {
            display: flex;
            background: rgba(26, 54, 93, 0.6);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            margin-bottom: 2rem;
            overflow: hidden;
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .nav-tab {
            flex: 1;
            padding: 1.2rem;
            text-align: center;
            background: transparent;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            color: #cbd5e0;
            position: relative;
        }
        
        .nav-tab.active {
            background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(49, 130, 206, 0.4);
        }
        
        .nav-tab:hover:not(.active) {
            background: rgba(255,255,255,0.1);
            color: #e2e8f0;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .card {
            background: rgba(26, 54, 93, 0.4);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            padding: 2rem;
            margin-bottom: 1.5rem;
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .card h3 {
            margin-bottom: 1.5rem;
            color: #e2e8f0;
            font-weight: 600;
            font-size: 1.25rem;
        }
        
        .card h4 {
            color: #cbd5e0;
            margin-bottom: 1rem;
            font-weight: 500;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: rgba(26, 54, 93, 0.6);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            text-align: center;
            border: 1px solid rgba(255,255,255,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.4);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #63b3ed;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .stat-label {
            color: #a0aec0;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .btn {
            padding: 0.875rem 1.75rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0.25rem;
            font-size: 0.9rem;
            position: relative;
            overflow: hidden;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(49, 130, 206, 0.4);
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #2c5282 0%, #2a4365 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(49, 130, 206, 0.6);
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: #cbd5e0;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }
        
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            color: #e2e8f0;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
        }
        
        .btn-logout {
            background: rgba(255, 255, 255, 0.1);
            color: #a0aec0;
            border: 1px solid rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
        }
        
        .btn-logout:hover {
            background: rgba(255, 255, 255, 0.2);
            color: #cbd5e0;
            border-color: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
        }
        
        .token-display {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 1.25rem;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            word-break: break-all;
            margin: 1rem 0;
            color: #e2e8f0;
            backdrop-filter: blur(10px);
        }
        
        .alert {
            padding: 1rem 1.25rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .alert-success {
            background: rgba(72, 187, 120, 0.2);
            color: #9ae6b4;
            border-color: rgba(72, 187, 120, 0.3);
        }
        
        .alert-error {
            background: rgba(229, 62, 62, 0.2);
            color: #feb2b2;
            border-color: rgba(229, 62, 62, 0.3);
        }
        
        .alert-info {
            background: rgba(99, 179, 237, 0.2);
            color: #bee3f8;
            border-color: rgba(99, 179, 237, 0.3);
        }
        
        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        @media (max-width: 768px) {
            .nav-tabs {
                flex-direction: column;
            }
            
            .grid-2 {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h1>🔐 Auth Service Admin Dashboard</h1>
            </div>
            <div>
                <button class="btn btn-secondary" onclick="performHealthCheck()" style="margin-right: 10px;">📊 Health Check</button>
                <button class="btn btn-logout" onclick="logout()">🚪 Logout</button>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Navigation Tabs -->
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('dashboard')">Dashboard</button>
            <button class="nav-tab" onclick="showTab('tokens')">Token Management</button>
            <button class="nav-tab" onclick="showTab('users')">User Management</button>
            <button class="nav-tab" onclick="showTab('security')">Security</button>
            <button class="nav-tab" onclick="showTab('system')">System</button>
        </div>

        <!-- Dashboard Tab -->
        <div id="dashboard" class="tab-content active">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="total-users">-</div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="active-tokens">-</div>
                    <div class="stat-label">Active Tokens</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="failed-logins">-</div>
                    <div class="stat-label">Failed Logins (24h)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="system-uptime">-</div>
                    <div class="stat-label">System Status</div>
                </div>
            </div>

            <div class="card">
                <h3>🚀 Quick Actions</h3>
                <button class="btn btn-primary" onclick="testOAuthFlow()">Test OAuth Flow</button>
                <button class="btn btn-secondary" onclick="refreshSystemStats()">Refresh Stats</button>
                <button class="btn btn-secondary" onclick="viewSystemLogs()">View Logs</button>
                <button class="btn btn-secondary" onclick="exportAuditLog()">Export Audit Log</button>
            </div>

            <div class="card">
                <h3>📊 System Health</h3>
                <div id="health-status">Loading system health...</div>
            </div>
        </div>

        <!-- Token Management Tab -->
        <div id="tokens" class="tab-content">
            <div class="card">
                <h3>🔑 Token Management</h3>
                <div class="alert alert-info">
                    <strong>Current Session:</strong> Manage your OAuth 2.0 tokens and test authentication flows.
                </div>
                
                <div class="grid-2">
                    <div>
                        <h4>Token Operations</h4>
                        <button class="btn btn-primary" onclick="generateTestToken()">Generate Test Token</button>
                        <button class="btn btn-secondary" onclick="validateCurrentToken()">Validate Token</button>
                        <button class="btn btn-secondary" onclick="refreshCurrentToken()">Refresh Token</button>
                        <button class="btn btn-secondary" onclick="revokeCurrentToken()">Revoke Token</button>
                    </div>
                    <div>
                        <h4>Token Analytics</h4>
                        <button class="btn btn-secondary" onclick="viewTokenStats()">Token Statistics</button>
                        <button class="btn btn-secondary" onclick="viewActiveTokens()">Active Tokens</button>
                        <button class="btn btn-secondary" onclick="viewExpiredTokens()">Expired Tokens</button>
                    </div>
                </div>

                <div id="current-token-display">
                    <h4>Current Access Token</h4>
                    <div class="token-display" id="token-content">No token generated yet</div>
                    <div id="token-info"></div>
                </div>
            </div>
        </div>

        <!-- Placeholder tabs for other sections -->
        <div id="users" class="tab-content">
            <div class="card">
                <h3>👥 User Management</h3>
                <p>User management functionality will be implemented here.</p>
            </div>
        </div>

        <div id="security" class="tab-content">
            <div class="card">
                <h3>🛡️ Security Management</h3>
                <p>Security management functionality will be implemented here.</p>
            </div>
        </div>

        <div id="system" class="tab-content">
            <div class="card">
                <h3>⚙️ System Administration</h3>
                <p>System administration functionality will be implemented here.</p>
            </div>
        </div>
    </div>

    <!-- Message Display -->
    <div id="message-container" style="position: fixed; top: 20px; right: 20px; z-index: 1000;"></div>

    <script>
        // Global variables
        let currentToken = null;
        let refreshToken = null;

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            checkAdminAccess();
            loadSystemHealth();
            loadSystemStats();
            loadSystemInfo();
        });

        // Check if user has admin access
        function checkAdminAccess() {
            const userInfo = JSON.parse(localStorage.getItem('user_info') || '{}');
            const token = localStorage.getItem('auth_token');

            if (!token) {
                alert('No authentication token found. Redirecting to login...');
                window.location.href = '/';
                return;
            }

            if (!userInfo.is_admin && userInfo.username !== 'btaylor-admin') {
                alert('Access denied. Admin privileges required.');
                window.location.href = '/dashboard.html';
                return;
            }

            // Update header with user info
            if (userInfo.username) {
                const headerTitle = document.querySelector('.header h1');
                headerTitle.innerHTML = `🔐 Auth Service Admin Dashboard - ${userInfo.username}`;
            }
        }

        // Tab management
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // Remove active class from all nav tabs
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked nav tab
            event.target.classList.add('active');
        }

        // Message display
        function showMessage(message, type = 'info') {
            const container = document.getElementById('message-container');
            const messageDiv = document.createElement('div');
            messageDiv.className = `alert alert-${type}`;
            messageDiv.style.marginBottom = '10px';
            messageDiv.textContent = message;

            container.appendChild(messageDiv);

            setTimeout(() => {
                container.removeChild(messageDiv);
            }, 5000);
        }

        // System health and stats
        async function loadSystemHealth() {
            try {
                const response = await fetch('/health');
                const data = await response.json();

                document.getElementById('health-status').innerHTML = `
                    <p><strong>Status:</strong> <span style="color: #68d391;">${data.status}</span></p>
                    <p><strong>Service:</strong> ${data.service}</p>
                    <p><strong>Version:</strong> ${data.version}</p>
                    <p><strong>Timestamp:</strong> ${new Date(data.timestamp * 1000).toLocaleString()}</p>
                    <p><strong>OAuth Endpoints:</strong> ${data.oauth2_endpoints.join(', ')}</p>
                `;

                document.getElementById('system-uptime').textContent = 'Healthy';

            } catch (error) {
                document.getElementById('health-status').innerHTML = `
                    <p><strong>Status:</strong> <span style="color: #fc8181;">Error loading health data</span></p>
                `;
                console.error('Health check failed:', error);
            }
        }

        function loadSystemStats() {
            // Mock stats - in real implementation, these would come from API
            document.getElementById('total-users').textContent = '2';
            document.getElementById('active-tokens').textContent = currentToken ? '1' : '0';
            document.getElementById('failed-logins').textContent = '0';
        }

        function loadSystemInfo() {
            // Mock system info
            console.log('System info loaded');
        }

        // Token management functions
        async function generateTestToken() {
            try {
                // Use current user's token or generate new one
                const userInfo = JSON.parse(localStorage.getItem('user_info') || '{}');
                const storedToken = localStorage.getItem('auth_token');

                if (storedToken) {
                    // Use existing token
                    currentToken = storedToken;
                    refreshToken = localStorage.getItem('refresh_token') || '';

                    document.getElementById('token-content').textContent = currentToken;
                    document.getElementById('token-info').innerHTML = `
                        <p><strong>Token Type:</strong> Bearer</p>
                        <p><strong>User:</strong> ${userInfo.username}</p>
                        <p><strong>Login Time:</strong> ${new Date(userInfo.login_time).toLocaleString()}</p>
                        <p><strong>Admin:</strong> ${userInfo.is_admin || userInfo.username === 'btaylor-admin' ? 'Yes' : 'No'}</p>
                    `;

                    loadSystemStats();
                    showMessage('Current token loaded successfully!', 'success');
                } else {
                    showMessage('No token available. Please login first.', 'error');
                }
            } catch (error) {
                showMessage('Error during token operation: ' + error.message, 'error');
            }
        }

        async function validateCurrentToken() {
            if (!currentToken) {
                showMessage('No token to validate. Generate a token first.', 'error');
                return;
            }

            try {
                const response = await fetch('/oauth/validate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ token: currentToken })
                });

                if (response.ok) {
                    const data = await response.json();
                    showMessage(`Token is valid! User: ${data.user_id || 'Unknown'}`, 'success');
                } else {
                    showMessage('Token validation failed', 'error');
                }
            } catch (error) {
                showMessage('Error validating token', 'error');
            }
        }

        async function refreshCurrentToken() {
            if (!refreshToken) {
                showMessage('No refresh token available.', 'error');
                return;
            }

            try {
                const response = await fetch('/oauth/refresh', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ refresh_token: refreshToken })
                });

                if (response.ok) {
                    const data = await response.json();
                    currentToken = data.access_token;

                    document.getElementById('token-content').textContent = data.access_token;
                    showMessage('Token refreshed successfully!', 'success');
                } else {
                    showMessage('Token refresh failed', 'error');
                }
            } catch (error) {
                showMessage('Error refreshing token', 'error');
            }
        }

        async function revokeCurrentToken() {
            if (!currentToken) {
                showMessage('No token to revoke.', 'error');
                return;
            }

            try {
                const response = await fetch('/oauth/revoke', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ token: currentToken })
                });

                if (response.ok) {
                    currentToken = null;
                    refreshToken = null;
                    document.getElementById('token-content').textContent = 'No token generated yet';
                    document.getElementById('token-info').innerHTML = '';
                    loadSystemStats();
                    showMessage('Token revoked successfully!', 'success');
                } else {
                    showMessage('Token revocation failed', 'error');
                }
            } catch (error) {
                showMessage('Error revoking token', 'error');
            }
        }

        // Dashboard functions
        function testOAuthFlow() {
            showMessage('Testing complete OAuth 2.0 flow...', 'info');
            generateTestToken();
        }

        function refreshSystemStats() {
            loadSystemHealth();
            loadSystemStats();
            loadSystemInfo();
            showMessage('System statistics refreshed!', 'success');
        }

        // Header functions
        async function performHealthCheck() {
            try {
                const response = await fetch('/health');
                const data = await response.json();
                showMessage(`Service Status: ${data.status} (v${data.version}) - ${data.oauth2_endpoints.length} endpoints available`, 'success');
                console.log('Health Check:', data);
            } catch (error) {
                showMessage('Health check failed: Service unavailable', 'error');
                console.error('Health check error:', error);
            }
        }

        function logout() {
            // Clear any stored tokens
            currentToken = null;
            refreshToken = null;
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_info');

            // Redirect to main page immediately
            window.location.href = '/';
        }

        // Mock functions for features not yet implemented
        function viewSystemLogs() { showMessage('System logs viewer coming soon!', 'info'); }
        function exportAuditLog() { showMessage('Audit log export feature coming soon!', 'info'); }
        function viewTokenStats() { showMessage('Token statistics feature coming soon!', 'info'); }
        function viewActiveTokens() { showMessage('Active tokens view coming soon!', 'info'); }
        function viewExpiredTokens() { showMessage('Expired tokens view coming soon!', 'info'); }
    </script>
</body>
</html>
