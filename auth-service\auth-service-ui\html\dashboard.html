<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sample Application Dashboard</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0f1419 0%, #1a202c 50%, #2d3748 100%);
            min-height: 100vh;
            color: #e2e8f0;
        }
        
        .header {
            background: rgba(26, 54, 93, 0.6);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            border-bottom: 1px solid rgba(255,255,255,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            color: #e2e8f0;
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .welcome-card {
            background: rgba(26, 54, 93, 0.4);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }
        
        .welcome-card h2 {
            color: #63b3ed;
            margin-bottom: 1rem;
            font-size: 2rem;
        }
        
        .welcome-card p {
            color: #cbd5e0;
            font-size: 1.1rem;
            margin-bottom: 1.5rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: rgba(26, 54, 93, 0.6);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid rgba(255,255,255,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.4);
        }
        
        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #63b3ed;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #a0aec0;
            font-size: 0.9rem;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            font-size: 0.9rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(49, 130, 206, 0.4);
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #2c5282 0%, #2a4365 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(49, 130, 206, 0.6);
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: #cbd5e0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            color: #e2e8f0;
            transform: translateY(-2px);
        }
        
        .btn-logout {
            background: rgba(255, 255, 255, 0.1);
            color: #a0aec0;
            border: 1px solid rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
        }
        
        .btn-logout:hover {
            background: rgba(255, 255, 255, 0.2);
            color: #cbd5e0;
            border-color: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
        }
        
        .footer {
            text-align: center;
            padding: 2rem;
            color: #a0aec0;
            border-top: 1px solid rgba(255,255,255,0.1);
            margin-top: 2rem;
        }
        
        .session-info {
            background: rgba(26, 54, 93, 0.3);
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
            font-size: 0.9rem;
            color: #a0aec0;
        }
        
        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 Sample Application Dashboard</h1>
        <div class="user-info">
            <div class="user-avatar" id="user-avatar">U</div>
            <div>
                <div style="font-weight: 500;" id="username-display">Loading...</div>
                <div style="font-size: 0.8rem; color: #a0aec0;">Authenticated User</div>
            </div>
            <button class="btn btn-secondary" onclick="openAdminPanel()" id="admin-btn" style="display: none;">⚙️ Admin</button>
            <button class="btn btn-logout" onclick="logout()">🚪 Logout</button>
        </div>
    </div>

    <div class="container">
        <div class="welcome-card">
            <h2>🎉 Welcome to Your Dashboard!</h2>
            <p>You have successfully authenticated using OAuth 2.0. This is a sample application dashboard that demonstrates the authentication flow.</p>
            <div class="session-info" id="session-info">
                Loading session information...
            </div>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">📈</div>
                <div class="stat-number">1,247</div>
                <div class="stat-label">Total Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🔐</div>
                <div class="stat-number">98.5%</div>
                <div class="stat-label">Security Score</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">⚡</div>
                <div class="stat-number">99.9%</div>
                <div class="stat-label">Uptime</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🚀</div>
                <div class="stat-number">156</div>
                <div class="stat-label">API Calls Today</div>
            </div>
        </div>
    </div>

    <div class="footer">
        <p>Sample Application Dashboard - Powered by Auth Service OAuth 2.0</p>
        <p style="margin-top: 0.5rem; font-size: 0.8rem;">This is a demonstration of successful authentication flow</p>
    </div>

    <script>
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadUserInfo();
            checkAuthentication();
        });

        function loadUserInfo() {
            const userInfo = JSON.parse(localStorage.getItem('user_info') || '{}');
            const token = localStorage.getItem('auth_token');
            
            if (userInfo.username) {
                document.getElementById('username-display').textContent = userInfo.username;
                document.getElementById('user-avatar').textContent = userInfo.username.charAt(0).toUpperCase();
                
                const loginTime = new Date(userInfo.login_time);
                const sessionDuration = Math.floor((new Date() - loginTime) / 1000 / 60);
                
                document.getElementById('session-info').innerHTML = `
                    <strong>Session Information:</strong><br>
                    Login Time: ${loginTime.toLocaleString()}<br>
                    Session Duration: ${sessionDuration} minutes<br>
                    Token Status: ${token ? 'Active' : 'Missing'}<br>
                    Token Expires: ${userInfo.token_expires ? userInfo.token_expires + ' seconds' : 'Unknown'}<br>
                    User Type: ${userInfo.is_admin || userInfo.username === 'btaylor-admin' ? 'Administrator' : 'Standard User'}
                `;
                
                // Show admin button only for admin users
                if (userInfo.is_admin || userInfo.username === 'btaylor-admin') {
                    document.getElementById('admin-btn').style.display = 'inline-block';
                }
            }
        }

        function checkAuthentication() {
            const token = localStorage.getItem('auth_token');
            if (!token) {
                alert('No authentication token found. Redirecting to login...');
                window.location.href = '/';
            }
        }

        function openAdminPanel() {
            window.open('/admin.html', '_blank');
        }

        function logout() {
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_info');
            window.location.href = '/';
        }
    </script>
</body>
</html>
