{"name": "auth-service-ui", "version": "1.0.0", "description": "React/TypeScript UI for OAuth 2.0 Authentication Service", "private": true, "dependencies": {"@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^4.9.5", "axios": "^1.6.0", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/material": "^5.15.0", "@mui/icons-material": "^5.15.0", "react-router-dom": "^6.20.0", "react-hook-form": "^7.48.0", "@hookform/resolvers": "^3.3.0", "yup": "^1.4.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/jest": "^29.5.0", "web-vitals": "^3.5.0"}, "proxy": "http://auth-dev.chcit.org:8083"}