import axios, { AxiosResponse } from 'axios';
import {
  LoginRequest,
  TokenResponse,
  RefreshRequest,
  ValidateRequest,
  ValidationResponse,
  RevokeRequest,
  HealthResponse,
  ErrorResponse
} from '../types/auth';

// Configure axios defaults
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://auth-dev.chcit.org:8083';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000,
});

// Add request interceptor for auth token
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('access_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export class AuthService {
  // Health check
  static async healthCheck(): Promise<HealthResponse> {
    try {
      const response: AxiosResponse<HealthResponse> = await api.get('/health');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error_description || 'Health check failed');
    }
  }

  // Login with username/password
  static async login(credentials: LoginRequest): Promise<TokenResponse> {
    try {
      const response: AxiosResponse<TokenResponse> = await api.post('/oauth/token', credentials);
      
      // Store tokens in localStorage
      localStorage.setItem('access_token', response.data.access_token);
      localStorage.setItem('refresh_token', response.data.refresh_token);
      
      return response.data;
    } catch (error: any) {
      const errorData: ErrorResponse = error.response?.data;
      throw new Error(errorData?.error_description || errorData?.error || 'Login failed');
    }
  }

  // Refresh access token
  static async refreshToken(refreshRequest: RefreshRequest): Promise<TokenResponse> {
    try {
      const response: AxiosResponse<TokenResponse> = await api.post('/oauth/refresh', refreshRequest);
      
      // Update stored tokens
      localStorage.setItem('access_token', response.data.access_token);
      if (response.data.refresh_token) {
        localStorage.setItem('refresh_token', response.data.refresh_token);
      }
      
      return response.data;
    } catch (error: any) {
      const errorData: ErrorResponse = error.response?.data;
      throw new Error(errorData?.error_description || errorData?.error || 'Token refresh failed');
    }
  }

  // Validate token
  static async validateToken(validateRequest: ValidateRequest): Promise<ValidationResponse> {
    try {
      const response: AxiosResponse<ValidationResponse> = await api.post('/oauth/validate', validateRequest, {
        headers: {
          Authorization: `Bearer ${validateRequest.token}`
        }
      });
      return response.data;
    } catch (error: any) {
      const errorData: ErrorResponse = error.response?.data;
      throw new Error(errorData?.error_description || errorData?.error || 'Token validation failed');
    }
  }

  // Revoke token (logout)
  static async revokeToken(revokeRequest: RevokeRequest): Promise<void> {
    try {
      await api.post('/oauth/revoke', revokeRequest);
      
      // Clear stored tokens
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
    } catch (error: any) {
      const errorData: ErrorResponse = error.response?.data;
      throw new Error(errorData?.error_description || errorData?.error || 'Token revocation failed');
    }
  }

  // Logout (revoke current token)
  static async logout(): Promise<void> {
    const accessToken = localStorage.getItem('access_token');
    if (accessToken) {
      try {
        await this.revokeToken({ token: accessToken });
      } catch (error) {
        console.warn('Token revocation failed during logout:', error);
      }
    }
    
    // Clear tokens regardless of revocation success
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
  }

  // Check if user is authenticated
  static isAuthenticated(): boolean {
    return !!localStorage.getItem('access_token');
  }

  // Get stored access token
  static getAccessToken(): string | null {
    return localStorage.getItem('access_token');
  }

  // Get stored refresh token
  static getRefreshToken(): string | null {
    return localStorage.getItem('refresh_token');
  }
}
