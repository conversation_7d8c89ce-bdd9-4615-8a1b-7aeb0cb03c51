// OAuth 2.0 Authentication Types

export interface LoginRequest {
  username: string;
  password: string;
  grant_type: 'password';
}

export interface TokenResponse {
  access_token: string;
  refresh_token: string;
  token_type: 'Bearer';
  expires_in: number;
  scope?: string;
}

export interface RefreshRequest {
  refresh_token: string;
  grant_type: 'refresh_token';
}

export interface ValidateRequest {
  token: string;
}

export interface ValidationResponse {
  valid: boolean;
  user_id?: string;
  username?: string;
  scopes?: string[];
  expires_at?: number;
}

export interface RevokeRequest {
  token: string;
}

export interface HealthResponse {
  status: string;
  service: string;
  version: string;
  oauth2_endpoints: string[];
  timestamp: number;
}

export interface ErrorResponse {
  error: string;
  error_description?: string;
}

export interface User {
  id: string;
  username: string;
  email?: string;
  roles?: string[];
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  accessToken: string | null;
  refreshToken: string | null;
  loading: boolean;
  error: string | null;
}
