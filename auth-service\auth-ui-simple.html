<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth Service - OAuth 2.0 Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #4a90e2 0%, #7bb3f0 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 1.5rem;
            width: 420px;
            height: 520px;
            margin: 1rem;
            overflow: hidden;
            position: relative;
            flex-shrink: 0;
        }
        
        .content-area {
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .header {
            text-align: center;
            margin-bottom: 0.75rem;
            flex-shrink: 0;
        }

        .header h1 {
            color: #333;
            margin-bottom: 0.25rem;
            font-size: 1.6rem;
        }

        .header p {
            color: #666;
            font-size: 0.85rem;
        }
        
        .form-group {
            margin-bottom: 0.5rem;
        }
        
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        input::placeholder {
            color: #999;
            opacity: 1;
        }
        
        input[type="text"]:focus, input[type="password"]:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            width: 100%;
            padding: 0.6rem;
            background: linear-gradient(135deg, #2980b9 0%, #3498db 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 0.75rem;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
            background: linear-gradient(135deg, #3498db 0%, #5dade2 100%);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.2);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #5499c7 0%, #85c1e9 100%);
            box-shadow: 0 4px 15px rgba(84, 153, 199, 0.3);
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #85c1e9 0%, #aed6f1 100%);
            box-shadow: 0 6px 20px rgba(84, 153, 199, 0.4);
        }
        
        .alert {
            padding: 0.5rem;
            border-radius: 8px;
            margin-bottom: 0.75rem;
            font-weight: 500;
            font-size: 0.75rem;
            line-height: 1.2;
            word-wrap: break-word;
            height: 45px;
            overflow: hidden;
            display: flex;
            align-items: center;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        

        
        .hidden {
            display: none;
        }
        

        
        .test-credentials {
            background: linear-gradient(135deg, #ebf3fd 0%, #d6eaff 100%);
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 0.5rem;
            margin-top: 0.5rem;
            font-size: 0.75rem;
            line-height: 1.2;
        }

        .test-credentials strong {
            color: #2980b9;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            position: relative;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e1e5e9;
        }

        .modal-header h3 {
            margin: 0;
            color: #2980b9;
            font-size: 1.5rem;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
            padding: 0.5rem;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-btn:hover {
            background: #f0f0f0;
            color: #333;
        }

        .modal-body {
            line-height: 1.6;
        }

        .info-section {
            background: linear-gradient(135deg, #ebf3fd 0%, #d6eaff 100%);
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .info-section h4 {
            margin: 0 0 0.5rem 0;
            color: #2980b9;
            font-size: 1.1rem;
        }

        .info-section pre {
            background: white;
            padding: 0.75rem;
            border-radius: 6px;
            border: 1px solid #ddd;
            font-size: 0.85rem;
            overflow-x: auto;
            margin: 0.5rem 0 0 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="content-area">
            <div class="header">
                <h1>🔐 Auth Service</h1>
                <p>OAuth 2.0 Authentication Dashboard</p>
            </div>

        <div id="alert-container" style="min-height: 45px; flex-shrink: 0;"></div>

        <!-- Login Form -->
        <div id="login-form" style="flex: 1; display: flex; flex-direction: column;">
            <div class="form-group">
                <input type="text" id="username" placeholder="Username" value="testuser" required>
            </div>

            <div class="form-group">
                <input type="password" id="password" placeholder="Password" value="testpass123" required>
            </div>

            <button class="btn" onclick="login()">Sign In</button>

            <div style="text-align: center; margin: 1rem 0;">
                <a href="#" onclick="showForgotPassword()" style="color: #2980b9; text-decoration: none; font-size: 0.85rem;">Forgot Password?</a>
            </div>

            <div class="test-credentials">
                <strong>Test Credentials:</strong><br>
                Username: testuser<br>
                Password: testpass123
            </div>
        </div>

        <!-- Dashboard -->
        <div id="dashboard" class="hidden" style="flex: 1; display: flex; flex-direction: column; justify-content: center;">
            <button class="btn btn-secondary" onclick="logout()">Logout</button>
        </div>


        </div>
    </div>

    <!-- Modal Window -->
    <div id="infoModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Information</h3>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Content will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '';
        let accessToken = localStorage.getItem('access_token');
        let refreshTokenValue = localStorage.getItem('refresh_token');

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            if (accessToken) {
                showDashboard();
            }

            // Clear default values when user starts typing
            const usernameInput = document.getElementById('username');
            const passwordInput = document.getElementById('password');

            usernameInput.addEventListener('focus', function() {
                if (this.value === 'testuser') {
                    this.value = '';
                }
            });

            passwordInput.addEventListener('focus', function() {
                if (this.value === 'testpass123') {
                    this.value = '';
                }
            });
        });

        function showAlert(message, type = 'error') {
            const container = document.getElementById('alert-container');
            container.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
            setTimeout(() => container.innerHTML = '', 5000);
        }

        function showModal(title, content) {
            document.getElementById('modal-title').textContent = title;
            document.getElementById('modal-body').innerHTML = content;
            document.getElementById('infoModal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('infoModal').style.display = 'none';
        }

        // Close modal when clicking outside of it
        window.onclick = function(event) {
            const modal = document.getElementById('infoModal');
            if (event.target === modal) {
                closeModal();
            }
        }

        function showLogin() {
            document.getElementById('login-form').classList.remove('hidden');
            document.getElementById('dashboard').classList.add('hidden');
        }

        function showDashboard(message = null) {
            document.getElementById('login-form').classList.add('hidden');
            document.getElementById('dashboard').classList.remove('hidden');

            // Show login message if provided
            if (message) {
                const dashboardElement = document.getElementById('dashboard');
                const messageDiv = document.createElement('div');
                messageDiv.style.cssText = 'text-align: center; margin-bottom: 2rem; padding: 1rem; background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); border: 1px solid #b3d9ff; border-radius: 8px; color: #155724;';
                messageDiv.innerHTML = `<strong>✅ ${message}</strong>`;
                dashboardElement.insertBefore(messageDiv, dashboardElement.firstChild);
            }
        }

        function showForgotPassword() {
            const forgotContent = `
                <div class="info-section">
                    <h4>🔑 Password Reset</h4>
                    <p>To reset your password, please contact your system administrator.</p>
                    <p><strong>For this demo:</strong> Use the test credentials provided in the login form.</p>
                </div>
                <div class="info-section">
                    <h4>📧 Contact Information</h4>
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Support:</strong> Available during business hours</p>
                </div>
            `;
            showModal('Forgot Password', forgotContent);
        }

        async function checkHealth() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();

                // Show detailed health info in modal
                const healthContent = `
                    <div class="info-section">
                        <h4>🟢 Service Status</h4>
                        <p><strong>Status:</strong> ${data.status}</p>
                        <p><strong>Version:</strong> ${data.version}</p>
                        <p><strong>Timestamp:</strong> ${new Date().toLocaleString()}</p>
                    </div>
                    <div class="info-section">
                        <h4>📊 Health Check Response</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
                showModal('Service Health Check', healthContent);
            } catch (error) {
                const errorContent = `
                    <div class="info-section">
                        <h4>❌ Health Check Failed</h4>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <p><strong>Timestamp:</strong> ${new Date().toLocaleString()}</p>
                    </div>
                `;
                showModal('Health Check Error', errorContent);
            }
        }

        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                showAlert('Please enter both username and password');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/oauth/token`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        grant_type: 'password'
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    accessToken = data.access_token;
                    refreshTokenValue = data.refresh_token;
                    
                    localStorage.setItem('access_token', accessToken);
                    localStorage.setItem('refresh_token', refreshTokenValue);
                    
                    showDashboard('Login successful!');
                } else {
                    showAlert(data.error_description || data.error || 'Login failed');
                }
            } catch (error) {
                showAlert('Network error: ' + error.message);
            }
        }

        async function validateToken() {
            if (!accessToken) {
                showAlert('No access token available');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/oauth/validate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${accessToken}`
                    },
                    body: JSON.stringify({
                        token: accessToken
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    const userInfo = data.user_id ? `${data.user_id.substring(0, 8)}...` : 'Unknown';
                    const scopeInfo = data.scopes ? data.scopes.join(', ') : 'None';
                    const expiryInfo = data.expires_at ? new Date(data.expires_at * 1000).toLocaleString() : 'Unknown';

                    const validationContent = `
                        <div class="info-section">
                            <h4>✅ Token Validation Successful</h4>
                            <p><strong>User ID:</strong> ${data.user_id || 'Unknown'}</p>
                            <p><strong>Scopes:</strong> ${scopeInfo}</p>
                            <p><strong>Expires:</strong> ${expiryInfo}</p>
                            <p><strong>Valid:</strong> ${data.valid ? 'Yes' : 'No'}</p>
                        </div>
                        <div class="info-section">
                            <h4>🔍 Full Validation Response</h4>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                        <div class="info-section">
                            <h4>🎫 Access Token (First 100 chars)</h4>
                            <pre>${accessToken.substring(0, 100)}...</pre>
                        </div>
                    `;
                    showModal('Token Validation Results', validationContent);
                } else {
                    const errorContent = `
                        <div class="info-section">
                            <h4>❌ Token Validation Failed</h4>
                            <p><strong>Error:</strong> ${data.error_description || 'Token validation failed'}</p>
                            <p><strong>Timestamp:</strong> ${new Date().toLocaleString()}</p>
                        </div>
                    `;
                    showModal('Token Validation Error', errorContent);
                }
            } catch (error) {
                showAlert('Validation error: ' + error.message);
            }
        }

        async function refreshToken() {
            if (!refreshTokenValue) {
                showAlert('No refresh token available');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/oauth/refresh`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        refresh_token: refreshTokenValue,
                        grant_type: 'refresh_token'
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    accessToken = data.access_token;
                    if (data.refresh_token) {
                        refreshTokenValue = data.refresh_token;
                    }

                    localStorage.setItem('access_token', accessToken);
                    localStorage.setItem('refresh_token', refreshTokenValue);

                    const refreshContent = `
                        <div class="info-section">
                            <h4>🔄 Token Refresh Successful</h4>
                            <p><strong>New Access Token:</strong> Generated</p>
                            <p><strong>New Refresh Token:</strong> ${data.refresh_token ? 'Generated' : 'Reused existing'}</p>
                            <p><strong>Expires In:</strong> ${data.expires_in ? data.expires_in + ' seconds' : 'Unknown'}</p>
                            <p><strong>Token Type:</strong> ${data.token_type || 'Bearer'}</p>
                            <p><strong>Scopes:</strong> ${data.scope || 'read write'}</p>
                        </div>
                        <div class="info-section">
                            <h4>🎫 New Access Token (First 100 chars)</h4>
                            <pre>${accessToken.substring(0, 100)}...</pre>
                        </div>
                        <div class="info-section">
                            <h4>📊 Full Refresh Response</h4>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                    showModal('Token Refresh Results', refreshContent);
                } else {
                    const errorContent = `
                        <div class="info-section">
                            <h4>❌ Token Refresh Failed</h4>
                            <p><strong>Error:</strong> ${data.error_description || 'Token refresh failed'}</p>
                            <p><strong>Timestamp:</strong> ${new Date().toLocaleString()}</p>
                        </div>
                    `;
                    showModal('Token Refresh Error', errorContent);
                }
            } catch (error) {
                showAlert('Refresh error: ' + error.message);
            }
        }

        async function logout() {
            if (accessToken) {
                try {
                    await fetch(`${API_BASE}/oauth/revoke`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            token: accessToken
                        })
                    });
                } catch (error) {
                    console.warn('Token revocation failed:', error);
                }
            }

            accessToken = null;
            refreshTokenValue = null;
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            
            showAlert('Logged out successfully', 'success');
            showLogin();
        }
    </script>
</body>
</html>
