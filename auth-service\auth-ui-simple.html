<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth Service - OAuth 2.0 Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #4a90e2 0%, #7bb3f0 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 1.5rem;
            width: 420px;
            height: 520px;
            margin: 1rem;
            overflow: hidden;
            position: relative;
            flex-shrink: 0;
        }
        
        .content-area {
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .header {
            text-align: center;
            margin-bottom: 0.75rem;
            flex-shrink: 0;
        }

        .header h1 {
            color: #333;
            margin-bottom: 0.25rem;
            font-size: 1.6rem;
        }

        .header p {
            color: #666;
            font-size: 0.85rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }
        
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        input[type="text"]:focus, input[type="password"]:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            width: 100%;
            padding: 0.6rem;
            background: linear-gradient(135deg, #2980b9 0%, #3498db 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 0.75rem;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
            background: linear-gradient(135deg, #3498db 0%, #5dade2 100%);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.2);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #5499c7 0%, #85c1e9 100%);
            box-shadow: 0 4px 15px rgba(84, 153, 199, 0.3);
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #85c1e9 0%, #aed6f1 100%);
            box-shadow: 0 6px 20px rgba(84, 153, 199, 0.4);
        }
        
        .alert {
            padding: 0.5rem;
            border-radius: 8px;
            margin-bottom: 0.75rem;
            font-weight: 500;
            font-size: 0.75rem;
            line-height: 1.2;
            word-wrap: break-word;
            height: 45px;
            overflow: hidden;
            display: flex;
            align-items: center;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .token-display {
            background: linear-gradient(135deg, #ebf3fd 0%, #d6eaff 100%);
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 0.5rem;
            margin-bottom: 0.75rem;
            word-break: break-all;
            font-family: monospace;
            font-size: 0.7rem;
            color: #2c3e50;
            height: 60px;
            overflow: hidden;
            line-height: 1.2;
        }
        
        .hidden {
            display: none;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-healthy {
            background: #28a745;
        }
        
        .status-error {
            background: #dc3545;
        }
        
        .test-credentials {
            background: linear-gradient(135deg, #ebf3fd 0%, #d6eaff 100%);
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 0.5rem;
            margin-top: 0.5rem;
            font-size: 0.75rem;
            line-height: 1.2;
        }

        .test-credentials strong {
            color: #2980b9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="content-area">
            <div class="header">
                <h1>🔐 Auth Service</h1>
                <p>OAuth 2.0 Authentication Dashboard</p>
            </div>

        <div id="alert-container" style="min-height: 45px; flex-shrink: 0;"></div>

        <!-- Login Form -->
        <div id="login-form" style="flex: 1; display: flex; flex-direction: column;">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" value="testuser" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" value="testpass123" required>
            </div>
            
            <button class="btn" onclick="login()">Sign In</button>
            
            <button class="btn btn-secondary" onclick="checkHealth()">Check Service Health</button>
            
            <div class="test-credentials">
                <strong>Test Credentials:</strong><br>
                Username: testuser<br>
                Password: testpass123
            </div>
        </div>

        <!-- Dashboard -->
        <div id="dashboard" class="hidden" style="flex: 1; display: flex; flex-direction: column;">
            <div class="token-display">
                <strong>Access Token:</strong><br>
                <span id="access-token"></span>
            </div>

            <button class="btn" onclick="validateToken()">Validate Token</button>
            <button class="btn" onclick="refreshToken()">Refresh Token</button>
            <button class="btn btn-secondary" onclick="logout()">Logout</button>
        </div>

        <!-- Service Status -->
        <div id="service-status" style="margin-top: 0.75rem; padding: 0.75rem; background: linear-gradient(135deg, #ebf3fd 0%, #d6eaff 100%); border: 1px solid #b3d9ff; border-radius: 8px; font-size: 0.85rem;">
            <strong style="color: #2980b9;">Service Status:</strong>
            <span id="status-indicator" class="status-indicator"></span>
            <span id="status-text" style="color: #34495e;">Checking...</span>
        </div>
        </div>
    </div>

    <script>
        const API_BASE = '';
        let accessToken = localStorage.getItem('access_token');
        let refreshTokenValue = localStorage.getItem('refresh_token');

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            if (accessToken) {
                showDashboard();
            }
            checkHealth();
        });

        function showAlert(message, type = 'error') {
            const container = document.getElementById('alert-container');
            container.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
            setTimeout(() => container.innerHTML = '', 5000);
        }

        function showLogin() {
            document.getElementById('login-form').classList.remove('hidden');
            document.getElementById('dashboard').classList.add('hidden');
        }

        function showDashboard() {
            document.getElementById('login-form').classList.add('hidden');
            document.getElementById('dashboard').classList.remove('hidden');
            document.getElementById('access-token').textContent = accessToken ? 
                accessToken.substring(0, 50) + '...' : 'No token';
        }

        async function checkHealth() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                document.getElementById('status-indicator').className = 'status-indicator status-healthy';
                document.getElementById('status-text').textContent = `${data.status} (v${data.version})`;
                
                showAlert('Service is healthy and running!', 'success');
            } catch (error) {
                document.getElementById('status-indicator').className = 'status-indicator status-error';
                document.getElementById('status-text').textContent = 'Service unavailable';
                
                showAlert('Failed to connect to auth service: ' + error.message);
            }
        }

        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                showAlert('Please enter both username and password');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/oauth/token`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        grant_type: 'password'
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    accessToken = data.access_token;
                    refreshTokenValue = data.refresh_token;
                    
                    localStorage.setItem('access_token', accessToken);
                    localStorage.setItem('refresh_token', refreshTokenValue);
                    
                    showAlert('Login successful!', 'success');
                    showDashboard();
                } else {
                    showAlert(data.error_description || data.error || 'Login failed');
                }
            } catch (error) {
                showAlert('Network error: ' + error.message);
            }
        }

        async function validateToken() {
            if (!accessToken) {
                showAlert('No access token available');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/oauth/validate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${accessToken}`
                    },
                    body: JSON.stringify({
                        token: accessToken
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    const userInfo = data.user_id ? `User ID: ${data.user_id.substring(0, 8)}...` : 'Unknown';
                    const scopeInfo = data.scopes ? ` | Scopes: ${data.scopes.join(', ')}` : '';
                    const expiryInfo = data.expires_at ? ` | Expires: ${new Date(data.expires_at * 1000).toLocaleString()}` : '';
                    showAlert(`Token is valid! ${userInfo}${scopeInfo}${expiryInfo}`, 'success');
                } else {
                    showAlert(data.error_description || 'Token validation failed');
                }
            } catch (error) {
                showAlert('Validation error: ' + error.message);
            }
        }

        async function refreshToken() {
            if (!refreshTokenValue) {
                showAlert('No refresh token available');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/oauth/refresh`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        refresh_token: refreshTokenValue,
                        grant_type: 'refresh_token'
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    accessToken = data.access_token;
                    if (data.refresh_token) {
                        refreshTokenValue = data.refresh_token;
                    }
                    
                    localStorage.setItem('access_token', accessToken);
                    localStorage.setItem('refresh_token', refreshTokenValue);
                    
                    document.getElementById('access-token').textContent = 
                        accessToken.substring(0, 50) + '...';
                    
                    showAlert('Token refreshed successfully!', 'success');
                } else {
                    showAlert(data.error_description || 'Token refresh failed');
                }
            } catch (error) {
                showAlert('Refresh error: ' + error.message);
            }
        }

        async function logout() {
            if (accessToken) {
                try {
                    await fetch(`${API_BASE}/oauth/revoke`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            token: accessToken
                        })
                    });
                } catch (error) {
                    console.warn('Token revocation failed:', error);
                }
            }

            accessToken = null;
            refreshTokenValue = null;
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            
            showAlert('Logged out successfully', 'success');
            showLogin();
        }
    </script>
</body>
</html>
