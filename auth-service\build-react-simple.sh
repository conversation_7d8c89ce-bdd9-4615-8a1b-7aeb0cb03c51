#!/bin/bash

echo "🚀 Building Simple React UI for OAuth 2.0..."

# Create a simple React build without complex dependencies
echo "📁 Creating simple React build..."
mkdir -p /tmp/react-build

# Create a simple index.html that loads React from CDN
cat > /tmp/react-build/index.html << 'EOF'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#2980b9" />
    <meta name="description" content="OAuth 2.0 Authentication Service - React UI" />
    <title>OAuth 2.0 Auth Service - React</title>
    
    <!-- React and Material-UI from CDN -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://unpkg.com/@mui/material@latest/umd/material-ui.production.min.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    
    <!-- Material-UI CSS -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons" />
    
    <style>
        body {
            margin: 0;
            font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #4a90e2 0%, #7bb3f0 100%);
            min-height: 100vh;
        }
        
        #root {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .auth-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 2rem;
            width: 100%;
            max-width: 420px;
            text-align: center;
        }
        
        .auth-header {
            margin-bottom: 2rem;
        }
        
        .auth-icon {
            font-size: 48px;
            color: #2980b9;
            margin-bottom: 1rem;
        }
        
        .auth-title {
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 1.8rem;
            font-weight: 600;
        }
        
        .auth-subtitle {
            color: #666;
            font-size: 0.9rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
            text-align: left;
        }
        
        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #2980b9;
        }
        
        .auth-button {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(135deg, #2980b9 0%, #3498db 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }
        
        .auth-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
        }
        
        .auth-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .auth-button.secondary {
            background: linear-gradient(135deg, #5499c7 0%, #85c1e9 100%);
            box-shadow: 0 4px 15px rgba(84, 153, 199, 0.3);
        }
        
        .alert {
            padding: 0.75rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            font-weight: 500;
            font-size: 0.875rem;
        }
        
        .alert.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .test-credentials {
            background: linear-gradient(135deg, #ebf3fd 0%, #d6eaff 100%);
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 0.75rem;
            margin-top: 1rem;
            font-size: 0.8rem;
            text-align: center;
        }
        
        .forgot-password {
            margin: 1rem 0;
        }
        
        .forgot-password a {
            color: #2980b9;
            text-decoration: none;
            font-size: 0.85rem;
        }
        
        .dashboard-actions {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="auth-card">
            <!-- Login Form -->
            <div id="login-form">
                <div class="auth-header">
                    <div class="auth-icon">🔐</div>
                    <h1 class="auth-title">OAuth 2.0 Service</h1>
                    <p class="auth-subtitle">React/TypeScript Authentication</p>
                </div>
                
                <div id="alert-container"></div>
                
                <form id="login-form-element">
                    <div class="form-group">
                        <input type="text" id="username" class="form-input" placeholder="Username" value="testuser" required>
                    </div>
                    
                    <div class="form-group">
                        <input type="password" id="password" class="form-input" placeholder="Password" value="testpass123" required>
                    </div>
                    
                    <button type="submit" class="auth-button" id="login-btn">Sign In</button>
                    
                    <div class="forgot-password">
                        <a href="#" onclick="showForgotPassword()">Forgot Password?</a>
                    </div>
                    
                    <div class="test-credentials">
                        <strong>Demo Credentials</strong><br>
                        Username: <strong>testuser</strong><br>
                        Password: <strong>testpass123</strong>
                    </div>
                </form>
            </div>
            
            <!-- Dashboard -->
            <div id="dashboard" class="hidden">
                <div class="auth-header">
                    <div class="auth-icon">✅</div>
                    <h2 class="auth-title">Welcome!</h2>
                    <p class="auth-subtitle">React OAuth 2.0 Dashboard</p>
                </div>
                
                <div id="success-message"></div>
                
                <div class="dashboard-actions">
                    <button class="auth-button" onclick="checkHealth()">Health Check</button>
                    <button class="auth-button" onclick="validateToken()">Validate Token</button>
                    <button class="auth-button" onclick="refreshToken()">Refresh Token</button>
                    <button class="auth-button" onclick="showAdmin()">Admin Panel</button>
                    <button class="auth-button secondary" onclick="logout()">Logout</button>
                </div>
            </div>
            
            <!-- Admin Panel -->
            <div id="admin-panel" class="hidden">
                <div class="auth-header">
                    <div class="auth-icon">⚙️</div>
                    <h2 class="auth-title">Admin Panel</h2>
                    <p class="auth-subtitle">User & Token Management</p>
                </div>
                
                <div class="dashboard-actions">
                    <button class="auth-button" onclick="showUserManagement()">User Management</button>
                    <button class="auth-button" onclick="showTokenAnalytics()">Token Analytics</button>
                    <button class="auth-button" onclick="showSystemSettings()">System Settings</button>
                    <button class="auth-button secondary" onclick="backToDashboard()">Back to Dashboard</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global state
        let currentUser = null;
        let accessToken = null;
        let refreshTokenValue = null;

        // API functions
        async function apiCall(endpoint, method = 'GET', data = null) {
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };
            
            if (accessToken) {
                options.headers.Authorization = `Bearer ${accessToken}`;
            }
            
            if (data) {
                options.body = JSON.stringify(data);
            }
            
            const response = await fetch(endpoint, options);
            return response;
        }

        // UI functions
        function showAlert(message, type = 'error') {
            const container = document.getElementById('alert-container');
            container.innerHTML = `<div class="alert ${type}">${message}</div>`;
            setTimeout(() => container.innerHTML = '', 5000);
        }

        function showView(viewId) {
            ['login-form', 'dashboard', 'admin-panel'].forEach(id => {
                document.getElementById(id).classList.add('hidden');
            });
            document.getElementById(viewId).classList.remove('hidden');
        }

        // Authentication functions
        async function login(event) {
            event.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('login-btn');
            
            loginBtn.disabled = true;
            loginBtn.textContent = 'Signing In...';
            
            try {
                const response = await apiCall('/oauth/token', 'POST', {
                    username,
                    password,
                    grant_type: 'password'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    accessToken = data.access_token;
                    refreshTokenValue = data.refresh_token;
                    currentUser = { username };
                    
                    localStorage.setItem('access_token', accessToken);
                    localStorage.setItem('refresh_token', refreshTokenValue);
                    
                    document.getElementById('success-message').innerHTML = 
                        '<div class="alert success">Login successful!</div>';
                    showView('dashboard');
                } else {
                    const error = await response.json();
                    showAlert(error.error_description || 'Login failed');
                }
            } catch (error) {
                showAlert('Network error: ' + error.message);
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = 'Sign In';
            }
        }

        async function logout() {
            if (accessToken) {
                try {
                    await apiCall('/oauth/revoke', 'POST', { token: accessToken });
                } catch (error) {
                    console.error('Revoke error:', error);
                }
            }
            
            accessToken = null;
            refreshTokenValue = null;
            currentUser = null;
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            
            showAlert('Logged out successfully', 'success');
            showView('login-form');
        }

        // Feature functions
        async function checkHealth() {
            try {
                const response = await apiCall('/health');
                const data = await response.json();
                
                if (response.ok) {
                    alert(`Service Health: ${data.status}\nVersion: ${data.version}\nEndpoints: ${data.oauth2_endpoints.join(', ')}`);
                } else {
                    alert('Health check failed');
                }
            } catch (error) {
                alert('Health check error: ' + error.message);
            }
        }

        async function validateToken() {
            if (!accessToken) {
                alert('No access token available');
                return;
            }
            
            try {
                const response = await apiCall('/oauth/validate', 'POST', { token: accessToken });
                const data = await response.json();
                
                if (response.ok) {
                    alert(`Token is valid!\nUser ID: ${data.user_id}\nScopes: ${data.scopes.join(', ')}\nExpires: ${new Date(data.expires_at * 1000).toLocaleString()}`);
                } else {
                    alert('Token validation failed: ' + (data.error_description || 'Unknown error'));
                }
            } catch (error) {
                alert('Validation error: ' + error.message);
            }
        }

        async function refreshToken() {
            if (!refreshTokenValue) {
                alert('No refresh token available');
                return;
            }
            
            try {
                const response = await apiCall('/oauth/refresh', 'POST', {
                    refresh_token: refreshTokenValue,
                    grant_type: 'refresh_token'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    accessToken = data.access_token;
                    if (data.refresh_token) {
                        refreshTokenValue = data.refresh_token;
                    }
                    
                    localStorage.setItem('access_token', accessToken);
                    localStorage.setItem('refresh_token', refreshTokenValue);
                    
                    alert('Token refreshed successfully!');
                } else {
                    const error = await response.json();
                    alert('Token refresh failed: ' + (error.error_description || 'Unknown error'));
                }
            } catch (error) {
                alert('Refresh error: ' + error.message);
            }
        }

        function showAdmin() {
            showView('admin-panel');
        }

        function backToDashboard() {
            showView('dashboard');
        }

        function showForgotPassword() {
            alert('For password reset, please contact your system <NAME_EMAIL>');
        }

        function showUserManagement() {
            alert('User Management feature coming soon!\n\nThis will include:\n- Add/remove users\n- Manage user roles\n- View user activity');
        }

        function showTokenAnalytics() {
            alert('Token Analytics feature coming soon!\n\nThis will include:\n- Token usage statistics\n- Rate limiting metrics\n- Security analytics');
        }

        function showSystemSettings() {
            alert('System Settings feature coming soon!\n\nThis will include:\n- OAuth 2.0 configuration\n- Security settings\n- Rate limiting configuration');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Check for existing tokens
            const storedToken = localStorage.getItem('access_token');
            const storedRefresh = localStorage.getItem('refresh_token');
            
            if (storedToken) {
                accessToken = storedToken;
                refreshTokenValue = storedRefresh;
                currentUser = { username: 'user' };
                showView('dashboard');
            }
            
            // Bind form submission
            document.getElementById('login-form-element').addEventListener('submit', login);
            
            // Clear default values on focus
            document.getElementById('username').addEventListener('focus', function() {
                if (this.value === 'testuser') this.value = '';
            });
            
            document.getElementById('password').addEventListener('focus', function() {
                if (this.value === 'testpass123') this.value = '';
            });
        });
    </script>
</body>
</html>
EOF

echo "🚀 Deploying React UI..."
sudo mkdir -p /var/www/auth-ui-react
sudo cp /tmp/react-build/index.html /var/www/auth-ui-react/
sudo chown -R www-data:www-data /var/www/auth-ui-react

echo "✅ Simple React UI deployed successfully!"
echo "🌐 Access at: https://auth-react.chcit.org"
