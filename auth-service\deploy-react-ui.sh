#!/bin/bash

echo "🚀 Deploying React/TypeScript OAuth 2.0 UI..."

# Set up directories
echo "📁 Setting up directories..."
sudo mkdir -p /var/www/auth-ui-react
sudo chown -R www-data:www-data /var/www/auth-ui-react

# Install Node.js and npm if not present
echo "📦 Checking Node.js installation..."
if ! command -v node &> /dev/null; then
    echo "Installing Node.js..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
fi

# Copy React project files
echo "📋 Copying React project files..."
sudo cp -r /tmp/react-ui /opt/auth-service/
sudo chown -R btaylor-admin:btaylor-admin /opt/auth-service/react-ui

# Install dependencies and build
echo "📦 Installing dependencies..."
cd /opt/auth-service/react-ui
npm install

echo "🔨 Building React application..."
npm run build

# Deploy to web directory
echo "🚀 Deploying to web directory..."
sudo cp -r build/* /var/www/auth-ui-react/
sudo chown -R www-data:www-data /var/www/auth-ui-react

# Create nginx configuration for React UI
echo "⚙️ Configuring nginx for React UI..."
sudo tee /etc/nginx/sites-available/auth-ui-react > /dev/null << 'EOF'
# React UI server configuration
server {
    listen 443 ssl http2;
    server_name auth-react.chcit.org;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/chcit.org/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/chcit.org/privkey.pem;
    
    # Modern SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # React app
    location / {
        root /var/www/auth-ui-react;
        index index.html;
        try_files $uri $uri/ /index.html;
        
        # Cache static assets
        location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API proxy to auth-service
    location /oauth/ {
        proxy_pass http://localhost:8083/oauth/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /health {
        proxy_pass http://localhost:8083/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# HTTP to HTTPS redirect
server {
    listen 80;
    server_name auth-react.chcit.org;
    return 301 https://$server_name$request_uri;
}
EOF

# Enable the site
sudo ln -sf /etc/nginx/sites-available/auth-ui-react /etc/nginx/sites-enabled/
sudo nginx -t && sudo systemctl reload nginx

echo "✅ React UI deployed successfully!"
echo "🌐 Access at: https://auth-react.chcit.org"
echo "📊 Admin panel: https://auth-react.chcit.org/admin"
