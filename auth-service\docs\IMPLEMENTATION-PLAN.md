# C++23 Backend RBAC Implementation Plan

**Date**: July 13, 2025  
**Phase**: 1.2 - C++23 Backend RBAC Implementation  
**Status**: 🚀 **ACTIVE DEVELOPMENT**  
**Estimated Duration**: 1-2 weeks  

## 🎯 **Implementation Overview**

This document provides a detailed implementation plan for adding multi-tenant Role-Based Access Control (RBAC) to the C++23 auth-service backend.

### **✅ Prerequisites Completed**
- ✅ Enhanced database schema deployed (auth_organizations, auth_projects, auth_roles, etc.)
- ✅ Existing C++ codebase analyzed and understood
- ✅ RBAC manager class structure designed
- ✅ Enhanced token manager architecture planned

## 📊 **Implementation Architecture**

### **New Components Being Added**

#### **1. RBACManager Class** (`rbac_manager.hpp/cpp`)
**Purpose**: Core RBAC functionality for organizations, projects, roles, and permissions

**Key Features**:
- Organization management (create, read, update, delete)
- Project management within organizations
- Role and permission management
- User-role assignments
- Permission validation

#### **2. EnhancedTokenManager Class** (`enhanced_token_manager.hpp/cpp`)
**Purpose**: Project-scoped token management with RBAC integration

**Key Features**:
- Project-specific token scoping
- Role-based token permissions
- Token analytics and reporting
- Enhanced security features

#### **3. Database Integration Enhancements**
**Purpose**: Extend DatabaseManager to support RBAC operations

**Key Features**:
- RBAC-specific query methods
- Transaction support for complex operations
- Performance optimization for permission checks

## 🗂️ **Implementation Phases**

### **Phase 1A: Core RBAC Infrastructure** (Days 1-3)

#### **Task 1A.1: Database Manager Extensions**
**Priority**: 🔥 **CRITICAL**  
**Estimated Time**: 1 day  

**Subtasks**:
- [ ] Add RBAC query methods to DatabaseManager
- [ ] Implement transaction support
- [ ] Add connection pooling optimization
- [ ] Create database helper methods for UUID operations
- [ ] Add error handling for RBAC operations

**Files to Modify**:
- `include/database_manager.hpp`
- `src/database_manager.cpp`

**New Methods to Add**:
```cpp
// RBAC-specific database operations
std::optional<pqxx::result> executeRBACQuery(const std::string& query, const std::vector<std::string>& params);
bool executeRBACTransaction(const std::vector<std::pair<std::string, std::vector<std::string>>>& queries);
std::string generateUUID();
bool isValidUUID(const std::string& uuid);
```

#### **Task 1A.2: RBACManager Implementation**
**Priority**: 🔥 **CRITICAL**  
**Estimated Time**: 2 days  

**Subtasks**:
- [ ] Implement organization management methods
- [ ] Implement project management methods
- [ ] Implement role and permission management
- [ ] Implement user-role assignment methods
- [ ] Implement permission validation logic
- [ ] Add comprehensive error handling
- [ ] Add logging and debugging support

**Files to Complete**:
- `src/rbac_manager.cpp` (complete implementation)

**Key Methods to Implement**:
```cpp
// Organization Management
std::string createOrganization(const std::string& org_name, const std::string& org_domain);
std::optional<Organization> getOrganization(const std::string& org_id);
bool updateOrganization(const std::string& org_id, const std::string& org_name, const std::string& org_domain);

// Project Management  
std::string createProject(const std::string& org_id, const std::string& project_name, const std::string& project_description);
std::optional<Project> getProject(const std::string& project_id);

// Permission Validation
PermissionResult validatePermission(const std::string& user_id, const std::string& project_id, const std::string& permission_name);
```

#### **Task 1A.3: Enhanced Token Manager Foundation**
**Priority**: 🔥 **HIGH**  
**Estimated Time**: 1 day  

**Subtasks**:
- [ ] Create enhanced token manager class structure
- [ ] Implement basic project-scoped token generation
- [ ] Add token validation with project context
- [ ] Integrate with existing JWT manager
- [ ] Add token storage enhancements

**Files to Create**:
- `src/enhanced_token_manager.cpp`

### **Phase 1B: API Integration** (Days 4-6)

#### **Task 1B.1: OAuth Endpoint Updates**
**Priority**: 🔥 **HIGH**  
**Estimated Time**: 2 days  

**Subtasks**:
- [ ] Update `/oauth/token` endpoint for project scoping
- [ ] Add project_id parameter to token requests
- [ ] Implement role-based token generation
- [ ] Update token validation in all endpoints
- [ ] Add permission checks to existing endpoints

**Files to Modify**:
- `src/auth_service.cpp`
- `include/auth_service.hpp`

**New Endpoint Parameters**:
```json
{
  "username": "<EMAIL>",
  "password": "password123",
  "grant_type": "password",
  "project_id": "uuid-of-project",
  "scope": "read write admin"
}
```

#### **Task 1B.2: New RBAC API Endpoints**
**Priority**: 🟡 **MEDIUM**  
**Estimated Time**: 2 days  

**Subtasks**:
- [ ] Add organization management endpoints
- [ ] Add project management endpoints
- [ ] Add user-role management endpoints
- [ ] Add permission validation endpoints
- [ ] Implement proper HTTP status codes and error responses

**New Endpoints to Add**:
```
POST   /api/organizations          - Create organization
GET    /api/organizations          - List organizations
GET    /api/organizations/{id}     - Get organization
PUT    /api/organizations/{id}     - Update organization
DELETE /api/organizations/{id}     - Delete organization

POST   /api/projects               - Create project
GET    /api/projects               - List projects
GET    /api/projects/{id}          - Get project
PUT    /api/projects/{id}          - Update project
DELETE /api/projects/{id}          - Delete project

POST   /api/users/{id}/roles       - Assign role to user
DELETE /api/users/{id}/roles       - Remove role from user
GET    /api/users/{id}/permissions - Get user permissions
```

### **Phase 1C: Enhanced Features** (Days 7-10)

#### **Task 1C.1: Token Analytics and Reporting**
**Priority**: 🟡 **MEDIUM**  
**Estimated Time**: 2 days  

**Subtasks**:
- [ ] Implement token analytics collection
- [ ] Add token usage tracking
- [ ] Create reporting endpoints
- [ ] Add token lifecycle management
- [ ] Implement token cleanup procedures

#### **Task 1C.2: Security Enhancements**
**Priority**: 🟡 **MEDIUM**  
**Estimated Time**: 2 days  

**Subtasks**:
- [ ] Add suspicious activity detection
- [ ] Implement enhanced rate limiting
- [ ] Add security event logging
- [ ] Implement token rotation policies
- [ ] Add IP-based access controls

### **Phase 1D: Testing and Integration** (Days 11-14)

#### **Task 1D.1: Unit Testing**
**Priority**: 🔥 **HIGH**  
**Estimated Time**: 2 days  

**Subtasks**:
- [ ] Create unit tests for RBACManager
- [ ] Create unit tests for EnhancedTokenManager
- [ ] Test database integration
- [ ] Test error handling scenarios
- [ ] Test performance with large datasets

#### **Task 1D.2: Integration Testing**
**Priority**: 🔥 **HIGH**  
**Estimated Time**: 2 days  

**Subtasks**:
- [ ] Test complete OAuth flow with RBAC
- [ ] Test API endpoints with various permission levels
- [ ] Test multi-tenant scenarios
- [ ] Test token scoping and validation
- [ ] Performance testing and optimization

## 🔧 **Technical Implementation Details**

### **Database Integration Strategy**

#### **Connection Management**
```cpp
class DatabaseManager {
    // Add connection pooling for RBAC operations
    std::unique_ptr<pqxx::connection_pool> rbac_pool_;
    
    // RBAC-specific query execution
    std::optional<pqxx::result> executeRBACQuery(
        const std::string& query, 
        const std::vector<std::string>& params
    );
};
```

#### **Transaction Support**
```cpp
// Complex RBAC operations require transactions
bool RBACManager::assignUserProjectRole(
    const std::string& user_id,
    const std::string& project_id, 
    const std::string& role_id
) {
    return db_manager_->executeRBACTransaction({
        {"INSERT INTO auth_user_project_roles ...", {user_id, project_id, role_id}},
        {"UPDATE auth_users SET updated_at = CURRENT_TIMESTAMP ...", {user_id}}
    });
}
```

### **Permission Validation Strategy**

#### **Caching Layer**
```cpp
class RBACManager {
private:
    // Cache frequently accessed permissions
    std::unordered_map<std::string, std::vector<std::string>> permission_cache_;
    std::chrono::steady_clock::time_point cache_expiry_;
    
    // Cache management
    void refreshPermissionCache(const std::string& user_id);
    bool isCacheValid();
};
```

#### **Optimized Queries**
```sql
-- Single query to get all user permissions for a project
SELECT DISTINCT p.permission_name
FROM auth_user_project_roles upr
JOIN auth_roles r ON upr.role_id = r.role_id  
JOIN auth_role_permissions rp ON r.role_id = rp.role_id
JOIN auth_permissions p ON rp.permission_id = p.permission_id
WHERE upr.user_id = $1 AND upr.project_id = $2 
  AND upr.is_active = true
  AND (upr.expires_at IS NULL OR upr.expires_at > CURRENT_TIMESTAMP);
```

### **Token Scoping Implementation**

#### **JWT Payload Enhancement**
```json
{
  "sub": "user-uuid",
  "project_id": "project-uuid", 
  "org_id": "org-uuid",
  "scope": ["read", "write", "admin"],
  "permissions": ["user.read", "project.write", "admin.all"],
  "iat": 1234567890,
  "exp": 1234571490,
  "jti": "token-uuid"
}
```

#### **Token Validation Flow**
```cpp
TokenValidationResult EnhancedTokenManager::validateProjectToken(
    const std::string& token,
    const std::string& project_id,
    const std::string& required_permission
) {
    // 1. Validate JWT signature and expiration
    // 2. Extract project_id from token
    // 3. Verify project_id matches request
    // 4. Check required permission if specified
    // 5. Update last_used_at timestamp
    // 6. Return validation result
}
```

## 📋 **Configuration Updates**

### **Enhanced Configuration File**
```json
{
  "rbac": {
    "enable_caching": true,
    "cache_ttl_seconds": 300,
    "max_roles_per_user": 10,
    "max_projects_per_org": 100,
    "permission_check_timeout_ms": 100
  },
  "tokens": {
    "project_scoping_required": true,
    "max_tokens_per_user_project": 5,
    "token_analytics_enabled": true,
    "suspicious_activity_detection": true
  }
}
```

## 🚀 **Deployment Strategy**

### **Incremental Deployment**
1. **Phase 1A**: Deploy core RBAC infrastructure (backward compatible)
2. **Phase 1B**: Deploy API updates (with feature flags)
3. **Phase 1C**: Enable enhanced features gradually
4. **Phase 1D**: Full testing and performance optimization

### **Rollback Plan**
- Feature flags for RBAC functionality
- Database schema versioning
- Backward compatibility for existing tokens
- Gradual migration of existing users to RBAC system

## 📊 **Success Metrics**

### **Functional Metrics**
- ✅ All RBAC operations working correctly
- ✅ Project-scoped tokens functioning
- ✅ Permission validation < 100ms response time
- ✅ Zero data loss during migration
- ✅ 100% backward compatibility

### **Performance Metrics**
- ✅ API response times < 200ms (95th percentile)
- ✅ Database query optimization (< 50ms for permission checks)
- ✅ Memory usage within acceptable limits
- ✅ Concurrent user support (500+ users)

### **Security Metrics**
- ✅ All security tests passing
- ✅ No privilege escalation vulnerabilities
- ✅ Proper token scoping enforcement
- ✅ Audit logging for all RBAC operations

This implementation plan provides a comprehensive roadmap for adding enterprise-grade RBAC functionality to the auth-service while maintaining performance, security, and reliability standards.
