#include <iostream>
#include <string>
#include <argon2.h>
#include <random>
#include <iomanip>
#include <sstream>

std::string generateSalt(size_t length = 16) {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);
    
    std::string salt;
    salt.reserve(length);
    for (size_t i = 0; i < length; ++i) {
        salt.push_back(static_cast<char>(dis(gen)));
    }
    return salt;
}

std::string bytesToHex(const uint8_t* bytes, size_t length) {
    std::stringstream ss;
    ss << std::hex << std::setfill('0');
    for (size_t i = 0; i < length; ++i) {
        ss << std::setw(2) << static_cast<unsigned>(bytes[i]);
    }
    return ss.str();
}

std::string hashPassword(const std::string& password) {
    // Argon2id parameters (matching auth-service configuration)
    const uint32_t t_cost = 3;      // time cost
    const uint32_t m_cost = 65536;  // memory cost (64 MB)
    const uint32_t parallelism = 4; // parallelism
    const uint32_t hash_len = 32;   // hash length
    const uint32_t salt_len = 16;   // salt length
    
    // Generate random salt
    std::string salt = generateSalt(salt_len);
    
    // Allocate memory for hash
    uint8_t hash[hash_len];
    
    // Hash the password
    int result = argon2id_hash_raw(
        t_cost, m_cost, parallelism,
        password.c_str(), password.length(),
        salt.c_str(), salt_len,
        hash, hash_len
    );
    
    if (result != ARGON2_OK) {
        throw std::runtime_error("Argon2id hashing failed: " + std::string(argon2_error_message(result)));
    }
    
    // Convert to hex strings
    std::string salt_hex = bytesToHex(reinterpret_cast<const uint8_t*>(salt.c_str()), salt_len);
    std::string hash_hex = bytesToHex(hash, hash_len);
    
    // Format as Argon2id string
    std::stringstream ss;
    ss << "$argon2id$v=19$m=" << m_cost << ",t=" << t_cost << ",p=" << parallelism 
       << "$" << salt_hex << "$" << hash_hex;
    
    return ss.str();
}

int main(int argc, char* argv[]) {
    if (argc != 2) {
        std::cerr << "Usage: " << argv[0] << " <password>" << std::endl;
        return 1;
    }
    
    try {
        std::string password = argv[1];
        std::string hash = hashPassword(password);
        std::cout << hash << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}
