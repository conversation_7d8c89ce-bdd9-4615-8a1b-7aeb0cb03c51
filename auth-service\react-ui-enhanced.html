<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#2980b9" />
    <meta name="description" content="OAuth 2.0 Authentication Service - Enhanced React UI" />
    <title>OAuth 2.0 Auth Service - React/TypeScript</title>
    
    <!-- React and Material-UI from CDN -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    
    <!-- Material-UI CSS -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons" />
    
    <style>
        body {
            margin: 0;
            font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #4a90e2 0%, #7bb3f0 100%);
            min-height: 100vh;
        }
        
        #root {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .auth-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 2rem;
            width: 100%;
            max-width: 420px;
            text-align: center;
            position: relative;
        }
        
        .auth-header {
            margin-bottom: 1.5rem;
        }
        
        .auth-icon {
            font-size: 48px;
            color: #2980b9;
            margin-bottom: 1rem;
        }
        
        .auth-title {
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 1.8rem;
            font-weight: 600;
        }
        
        .auth-subtitle {
            color: #666;
            font-size: 0.9rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
            text-align: left;
        }
        
        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #2980b9;
        }
        
        .form-input::placeholder {
            color: #999;
        }
        
        .auth-button {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(135deg, #2980b9 0%, #3498db 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 0.75rem;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }
        
        .auth-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
        }
        
        .auth-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .auth-button.secondary {
            background: linear-gradient(135deg, #5499c7 0%, #85c1e9 100%);
            box-shadow: 0 4px 15px rgba(84, 153, 199, 0.3);
        }
        
        .auth-button.outline {
            background: transparent;
            border: 2px solid #2980b9;
            color: #2980b9;
            box-shadow: none;
        }
        
        .auth-button.outline:hover {
            background: #2980b9;
            color: white;
        }
        
        .alert {
            padding: 0.75rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            font-weight: 500;
            font-size: 0.875rem;
        }
        
        .alert.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .test-credentials {
            background: linear-gradient(135deg, #ebf3fd 0%, #d6eaff 100%);
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 0.75rem;
            margin-top: 1rem;
            font-size: 0.8rem;
            text-align: center;
        }
        
        .forgot-password {
            margin: 1rem 0;
        }
        
        .forgot-password a {
            color: #2980b9;
            text-decoration: none;
            font-size: 0.85rem;
        }
        
        .dashboard-actions {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }
        
        .hidden {
            display: none;
        }
        
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
        }
        
        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
        }
        
        .close-button {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
            padding: 0.5rem;
            border-radius: 50%;
            transition: background-color 0.3s;
        }
        
        .close-button:hover {
            background-color: #f0f0f0;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            overflow-x: auto;
            margin: 1rem 0;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .stat-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #2980b9;
        }
        
        .stat-label {
            font-size: 0.875rem;
            color: #666;
            margin-top: 0.5rem;
        }
        
        .user-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }
        
        .user-table th,
        .user-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .user-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="auth-card">
            <!-- Login Form -->
            <div id="login-form">
                <div class="auth-header">
                    <div class="auth-icon">🔐</div>
                    <h1 class="auth-title">OAuth 2.0 Service</h1>
                    <p class="auth-subtitle">React/TypeScript Authentication</p>
                </div>
                
                <div id="alert-container"></div>
                
                <form id="login-form-element">
                    <div class="form-group">
                        <input type="text" id="username" class="form-input" placeholder="Username" required>
                    </div>
                    
                    <div class="form-group">
                        <input type="password" id="password" class="form-input" placeholder="Password" required>
                    </div>
                    
                    <button type="submit" class="auth-button" id="login-btn">Sign In</button>
                    
                    <div class="forgot-password">
                        <a href="#" onclick="showForgotPassword()">Forgot Password?</a>
                    </div>
                    
                    <div class="test-credentials">
                        <strong>Demo Credentials</strong><br>
                        Username: <strong>testuser</strong><br>
                        Password: <strong>testpass123</strong>
                    </div>
                </form>
            </div>
            
            <!-- Dashboard -->
            <div id="dashboard" class="hidden">
                <div class="auth-header">
                    <div class="auth-icon">✅</div>
                    <h2 class="auth-title">Welcome!</h2>
                    <p class="auth-subtitle">OAuth 2.0 Dashboard</p>
                </div>
                
                <div id="success-message"></div>
                
                <div class="dashboard-actions">
                    <button class="auth-button outline" onclick="checkHealth()">🏥 Health Check</button>
                    <button class="auth-button outline" onclick="validateToken()">🔍 Token Validation</button>
                    <button class="auth-button outline" onclick="refreshToken()">🔄 Refresh Token</button>
                    <button class="auth-button outline" onclick="showAdmin()">⚙️ Admin Panel</button>
                    <button class="auth-button secondary" onclick="logout()">🚪 Logout</button>
                </div>
            </div>
            
            <!-- Admin Panel -->
            <div id="admin-panel" class="hidden">
                <div class="auth-header">
                    <div class="auth-icon">⚙️</div>
                    <h2 class="auth-title">Admin Panel</h2>
                    <p class="auth-subtitle">User & System Management</p>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="total-users">3</div>
                        <div class="stat-label">Total Users</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="active-tokens">15</div>
                        <div class="stat-label">Active Tokens</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="failed-logins">2</div>
                        <div class="stat-label">Failed Logins</div>
                    </div>
                </div>
                
                <div class="dashboard-actions">
                    <button class="auth-button outline" onclick="showUserManagement()">👥 User Management</button>
                    <button class="auth-button outline" onclick="showTokenAnalytics()">📊 Token Analytics</button>
                    <button class="auth-button outline" onclick="showSystemSettings()">⚙️ System Settings</button>
                    <button class="auth-button secondary" onclick="backToDashboard()">← Back to Dashboard</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for displaying results -->
    <div id="modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modal-title">Modal Title</h3>
                <button class="close-button" onclick="closeModal()">×</button>
            </div>
            <div id="modal-body">
                Modal content goes here
            </div>
        </div>
    </div>

    <script>
        // Global state
        let currentUser = null;
        let accessToken = null;
        let refreshTokenValue = null;

        // Mock data for admin panel
        const mockUsers = [
            { id: '1', username: 'testuser', email: '<EMAIL>', status: 'active', lastLogin: '2024-01-15 10:30:00' },
            { id: '2', username: 'admin', email: '<EMAIL>', status: 'active', lastLogin: '2024-01-15 09:15:00' },
            { id: '3', username: 'demo', email: '<EMAIL>', status: 'inactive', lastLogin: '2024-01-10 14:20:00' },
        ];

        // API functions
        async function apiCall(endpoint, method = 'GET', data = null) {
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };
            
            if (accessToken) {
                options.headers.Authorization = `Bearer ${accessToken}`;
            }
            
            if (data) {
                options.body = JSON.stringify(data);
            }
            
            const response = await fetch(endpoint, options);
            return response;
        }

        // UI functions
        function showAlert(message, type = 'error') {
            const container = document.getElementById('alert-container');
            container.innerHTML = `<div class="alert ${type}">${message}</div>`;
            setTimeout(() => container.innerHTML = '', 5000);
        }

        function showView(viewId) {
            ['login-form', 'dashboard', 'admin-panel'].forEach(id => {
                document.getElementById(id).classList.add('hidden');
            });
            document.getElementById(viewId).classList.remove('hidden');
        }

        function showModal(title, content) {
            document.getElementById('modal-title').textContent = title;
            document.getElementById('modal-body').innerHTML = content;
            document.getElementById('modal').classList.remove('hidden');
        }

        function closeModal() {
            document.getElementById('modal').classList.add('hidden');
        }

        // Authentication functions
        async function login(event) {
            event.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('login-btn');
            
            loginBtn.disabled = true;
            loginBtn.textContent = 'Signing In...';
            
            try {
                const response = await apiCall('/oauth/token', 'POST', {
                    username,
                    password,
                    grant_type: 'password'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    accessToken = data.access_token;
                    refreshTokenValue = data.refresh_token;
                    currentUser = { username };
                    
                    localStorage.setItem('access_token', accessToken);
                    localStorage.setItem('refresh_token', refreshTokenValue);
                    
                    document.getElementById('success-message').innerHTML = 
                        '<div class="alert success">✅ Login successful! Welcome to the OAuth 2.0 dashboard.</div>';
                    showView('dashboard');
                } else {
                    const error = await response.json();
                    showAlert(error.error_description || 'Login failed');
                }
            } catch (error) {
                showAlert('Network error: ' + error.message);
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = 'Sign In';
            }
        }

        async function logout() {
            if (accessToken) {
                try {
                    await apiCall('/oauth/revoke', 'POST', { token: accessToken });
                } catch (error) {
                    console.error('Revoke error:', error);
                }
            }
            
            accessToken = null;
            refreshTokenValue = null;
            currentUser = null;
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            
            showAlert('✅ Logged out successfully', 'success');
            showView('login-form');
        }

        // Feature functions
        async function checkHealth() {
            try {
                const response = await apiCall('/health');
                const data = await response.json();
                
                if (response.ok) {
                    const healthContent = `
                        <div class="alert success">✅ Service is healthy!</div>
                        <div class="code-block">${JSON.stringify(data, null, 2)}</div>
                        <div class="alert info">
                            <strong>Service Status:</strong> ${data.status}<br>
                            <strong>Version:</strong> ${data.version}<br>
                            <strong>OAuth 2.0 Endpoints:</strong> ${data.oauth2_endpoints.join(', ')}
                        </div>
                    `;
                    showModal('🏥 Service Health Check', healthContent);
                } else {
                    showModal('❌ Health Check Failed', '<div class="alert error">Health check failed</div>');
                }
            } catch (error) {
                showModal('❌ Health Check Error', `<div class="alert error">Health check error: ${error.message}</div>`);
            }
        }

        async function validateToken() {
            if (!accessToken) {
                showModal('❌ Token Validation', '<div class="alert error">No access token available</div>');
                return;
            }
            
            try {
                const response = await apiCall('/oauth/validate', 'POST', { token: accessToken });
                const data = await response.json();
                
                if (response.ok) {
                    const validationContent = `
                        <div class="alert success">✅ Token is valid!</div>
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">${data.user_id}</div>
                                <div class="stat-label">User ID</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">${data.scopes.length}</div>
                                <div class="stat-label">Scopes</div>
                            </div>
                        </div>
                        <div class="alert info">
                            <strong>Scopes:</strong> ${data.scopes.join(', ')}<br>
                            <strong>Expires:</strong> ${new Date(data.expires_at * 1000).toLocaleString()}
                        </div>
                        <div class="code-block">${JSON.stringify(data, null, 2)}</div>
                    `;
                    showModal('🔍 Token Validation Results', validationContent);
                } else {
                    showModal('❌ Token Validation Failed', `<div class="alert error">Token validation failed: ${data.error_description || 'Unknown error'}</div>`);
                }
            } catch (error) {
                showModal('❌ Validation Error', `<div class="alert error">Validation error: ${error.message}</div>`);
            }
        }

        async function refreshToken() {
            if (!refreshTokenValue) {
                showModal('❌ Token Refresh', '<div class="alert error">No refresh token available</div>');
                return;
            }
            
            try {
                const response = await apiCall('/oauth/refresh', 'POST', {
                    refresh_token: refreshTokenValue,
                    grant_type: 'refresh_token'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    accessToken = data.access_token;
                    if (data.refresh_token) {
                        refreshTokenValue = data.refresh_token;
                    }
                    
                    localStorage.setItem('access_token', accessToken);
                    localStorage.setItem('refresh_token', refreshTokenValue);
                    
                    const refreshContent = `
                        <div class="alert success">✅ Token refreshed successfully!</div>
                        <div class="alert info">
                            <strong>Token Type:</strong> ${data.token_type || 'Bearer'}<br>
                            <strong>Expires In:</strong> ${data.expires_in || 3600} seconds<br>
                            <strong>New Token:</strong> ${data.access_token.substring(0, 20)}...
                        </div>
                    `;
                    showModal('🔄 Token Refresh Successful', refreshContent);
                } else {
                    const error = await response.json();
                    showModal('❌ Token Refresh Failed', `<div class="alert error">Token refresh failed: ${error.error_description || 'Unknown error'}</div>`);
                }
            } catch (error) {
                showModal('❌ Refresh Error', `<div class="alert error">Refresh error: ${error.message}</div>`);
            }
        }

        function showAdmin() {
            showView('admin-panel');
        }

        function backToDashboard() {
            showView('dashboard');
        }

        function showForgotPassword() {
            showModal('🔑 Forgot Password', `
                <div class="alert info">
                    <strong>Password Reset</strong><br>
                    For password reset, please contact your system administrator at:<br>
                    <strong><EMAIL></strong>
                </div>
                <p>Include your username and a brief description of the issue.</p>
            `);
        }

        function showUserManagement() {
            const userTable = `
                <div class="alert info">👥 User Management Dashboard</div>
                <table class="user-table">
                    <thead>
                        <tr>
                            <th>Username</th>
                            <th>Email</th>
                            <th>Status</th>
                            <th>Last Login</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${mockUsers.map(user => `
                            <tr>
                                <td>${user.username}</td>
                                <td>${user.email}</td>
                                <td><span class="status-badge status-${user.status}">${user.status}</span></td>
                                <td>${user.lastLogin}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
                <div class="alert info">
                    <strong>Future Features:</strong><br>
                    • Add/remove users<br>
                    • Manage user roles and permissions<br>
                    • View detailed user activity logs<br>
                    • Password reset functionality
                </div>
            `;
            showModal('👥 User Management', userTable);
        }

        function showTokenAnalytics() {
            const analyticsContent = `
                <div class="alert info">📊 Token Analytics Dashboard</div>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value">15</div>
                        <div class="stat-label">Active Tokens</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">127</div>
                        <div class="stat-label">Total Issued</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">5</div>
                        <div class="stat-label">Rate Limited</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">2</div>
                        <div class="stat-label">Failed Logins</div>
                    </div>
                </div>
                <div class="alert info">
                    <strong>Analytics Features:</strong><br>
                    • Real-time token usage statistics<br>
                    • Rate limiting metrics and trends<br>
                    • Security event monitoring<br>
                    • Performance analytics<br>
                    • User behavior insights
                </div>
            `;
            showModal('📊 Token Analytics', analyticsContent);
        }

        function showSystemSettings() {
            const settingsContent = `
                <div class="alert info">⚙️ System Settings</div>
                <div class="alert info">
                    <strong>OAuth 2.0 Configuration:</strong><br>
                    • JWT Algorithm: RS256<br>
                    • Token Expiry: 3600 seconds<br>
                    • Refresh Token Expiry: 7 days<br>
                    • Rate Limiting: 5 requests/minute for token endpoint
                </div>
                <div class="alert info">
                    <strong>Security Settings:</strong><br>
                    • Password Hashing: Argon2id<br>
                    • SSL/TLS: Enabled with wildcard certificates<br>
                    • CORS: Configured for secure cross-origin requests<br>
                    • Rate Limiting: nginx-based protection
                </div>
                <div class="alert info">
                    <strong>Future Configuration Options:</strong><br>
                    • Adjustable rate limiting thresholds<br>
                    • Custom JWT expiration times<br>
                    • Multi-factor authentication settings<br>
                    • Advanced security policies
                </div>
            `;
            showModal('⚙️ System Settings', settingsContent);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Check for existing tokens
            const storedToken = localStorage.getItem('access_token');
            const storedRefresh = localStorage.getItem('refresh_token');
            
            if (storedToken) {
                accessToken = storedToken;
                refreshTokenValue = storedRefresh;
                currentUser = { username: 'user' };
                document.getElementById('success-message').innerHTML = 
                    '<div class="alert success">✅ Welcome back! Your session has been restored.</div>';
                showView('dashboard');
            } else {
                // Set demo credentials
                document.getElementById('username').value = 'testuser';
                document.getElementById('password').value = 'testpass123';
            }
            
            // Bind form submission
            document.getElementById('login-form-element').addEventListener('submit', login);
            
            // Clear default values on focus
            document.getElementById('username').addEventListener('focus', function() {
                if (this.value === 'testuser') this.value = '';
            });
            
            document.getElementById('password').addEventListener('focus', function() {
                if (this.value === 'testpass123') this.value = '';
            });
            
            // Close modal when clicking outside
            document.getElementById('modal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeModal();
                }
            });
        });
    </script>
</body>
</html>
