[{"D:\\Coding_Projects\\auth-service\\react-ui\\src\\index.tsx": "1", "D:\\Coding_Projects\\auth-service\\react-ui\\src\\App.tsx": "2", "D:\\Coding_Projects\\auth-service\\react-ui\\src\\pages\\LoginPage.tsx": "3", "D:\\Coding_Projects\\auth-service\\react-ui\\src\\pages\\DashboardPage.tsx": "4", "D:\\Coding_Projects\\auth-service\\react-ui\\src\\pages\\AdminPage.tsx": "5", "D:\\Coding_Projects\\auth-service\\react-ui\\src\\components\\ProtectedRoute.tsx": "6", "D:\\Coding_Projects\\auth-service\\react-ui\\src\\contexts\\AuthContext.tsx": "7", "D:\\Coding_Projects\\auth-service\\react-ui\\src\\services\\authApi.ts": "8"}, {"size": 1590, "mtime": 1752672935480, "results": "9", "hashOfConfig": "10"}, {"size": 1305, "mtime": 1752672935480, "results": "11", "hashOfConfig": "10"}, {"size": 4458, "mtime": 1752672935487, "results": "12", "hashOfConfig": "10"}, {"size": 8053, "mtime": 1752672935486, "results": "13", "hashOfConfig": "10"}, {"size": 7267, "mtime": 1752672935486, "results": "14", "hashOfConfig": "10"}, {"size": 1001, "mtime": 1752672935486, "results": "15", "hashOfConfig": "10"}, {"size": 4280, "mtime": 1752672935485, "results": "16", "hashOfConfig": "10"}, {"size": 3755, "mtime": 1752672935488, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "b9a57g", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Coding_Projects\\auth-service\\react-ui\\src\\index.tsx", [], [], "D:\\Coding_Projects\\auth-service\\react-ui\\src\\App.tsx", [], [], "D:\\Coding_Projects\\auth-service\\react-ui\\src\\pages\\LoginPage.tsx", [], [], "D:\\Coding_Projects\\auth-service\\react-ui\\src\\pages\\DashboardPage.tsx", ["42", "43"], [], "D:\\Coding_Projects\\auth-service\\react-ui\\src\\pages\\AdminPage.tsx", ["44"], [], "D:\\Coding_Projects\\auth-service\\react-ui\\src\\components\\ProtectedRoute.tsx", [], [], "D:\\Coding_Projects\\auth-service\\react-ui\\src\\contexts\\AuthContext.tsx", [], [], "D:\\Coding_Projects\\auth-service\\react-ui\\src\\services\\authApi.ts", [], [], {"ruleId": "45", "severity": 1, "message": "46", "line": 30, "column": 10, "nodeType": "47", "messageId": "48", "endLine": 30, "endColumn": 22}, {"ruleId": "45", "severity": 1, "message": "49", "line": 30, "column": 24, "nodeType": "47", "messageId": "48", "endLine": 30, "endColumn": 39}, {"ruleId": "45", "severity": 1, "message": "50", "line": 48, "column": 11, "nodeType": "47", "messageId": "48", "endLine": 48, "endColumn": 15}, "@typescript-eslint/no-unused-vars", "'HealthStatus' is defined but never used.", "Identifier", "unusedVar", "'TokenValidation' is defined but never used.", "'user' is assigned a value but never used."]