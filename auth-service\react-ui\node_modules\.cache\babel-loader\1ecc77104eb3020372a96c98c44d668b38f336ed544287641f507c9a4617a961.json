{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\n\n/**\n * @ignore - internal component.\n */\nconst GridContext = /*#__PURE__*/React.createContext();\nif (process.env.NODE_ENV !== 'production') {\n  GridContext.displayName = 'GridContext';\n}\nexport default GridContext;", "map": {"version": 3, "names": ["React", "GridContext", "createContext", "process", "env", "NODE_ENV", "displayName"], "sources": ["D:/Coding_Projects/auth-service/react-ui/node_modules/@mui/material/Grid/GridContext.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\n\n/**\n * @ignore - internal component.\n */\nconst GridContext = /*#__PURE__*/React.createContext();\nif (process.env.NODE_ENV !== 'production') {\n  GridContext.displayName = 'GridContext';\n}\nexport default GridContext;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;;AAE9B;AACA;AACA;AACA,MAAMC,WAAW,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,CAAC;AACtD,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,WAAW,CAACK,WAAW,GAAG,aAAa;AACzC;AACA,eAAeL,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}