{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getStepperUtilityClass(slot) {\n  return generateUtilityClass('MuiStepper', slot);\n}\nconst stepperClasses = generateUtilityClasses('MuiStepper', ['root', 'horizontal', 'vertical', 'nonLinear', 'alternativeLabel']);\nexport default stepperClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getStepperUtilityClass", "slot", "stepperClasses"], "sources": ["D:/Coding_Projects/auth-service/react-ui/node_modules/@mui/material/Stepper/stepperClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getStepperUtilityClass(slot) {\n  return generateUtilityClass('MuiStepper', slot);\n}\nconst stepperClasses = generateUtilityClasses('MuiStepper', ['root', 'horizontal', 'vertical', 'nonLinear', 'alternativeLabel']);\nexport default stepperClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,sBAAsBA,CAACC,IAAI,EAAE;EAC3C,OAAOF,oBAAoB,CAAC,YAAY,EAAEE,IAAI,CAAC;AACjD;AACA,MAAMC,cAAc,GAAGJ,sBAAsB,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,EAAE,kBAAkB,CAAC,CAAC;AAChI,eAAeI,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}