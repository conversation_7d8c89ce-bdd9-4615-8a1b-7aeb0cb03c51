{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"component\", \"padding\", \"size\", \"stickyHeader\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport TableContext from './TableContext';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled from '../styles/styled';\nimport { getTableUtilityClass } from './tableClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    stickyHeader\n  } = ownerState;\n  const slots = {\n    root: ['root', stickyHeader && 'stickyHeader']\n  };\n  return composeClasses(slots, getTableUtilityClass, classes);\n};\nconst TableRoot = styled('table', {\n  name: 'MuiTable',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.stickyHeader && styles.stickyHeader];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  display: 'table',\n  width: '100%',\n  borderCollapse: 'collapse',\n  borderSpacing: 0,\n  '& caption': _extends({}, theme.typography.body2, {\n    padding: theme.spacing(2),\n    color: (theme.vars || theme).palette.text.secondary,\n    textAlign: 'left',\n    captionSide: 'bottom'\n  })\n}, ownerState.stickyHeader && {\n  borderCollapse: 'separate'\n}));\nconst defaultComponent = 'table';\nconst Table = /*#__PURE__*/React.forwardRef(function Table(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiTable'\n  });\n  const {\n      className,\n      component = defaultComponent,\n      padding = 'normal',\n      size = 'medium',\n      stickyHeader = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    component,\n    padding,\n    size,\n    stickyHeader\n  });\n  const classes = useUtilityClasses(ownerState);\n  const table = React.useMemo(() => ({\n    padding,\n    size,\n    stickyHeader\n  }), [padding, size, stickyHeader]);\n  return /*#__PURE__*/_jsx(TableContext.Provider, {\n    value: table,\n    children: /*#__PURE__*/_jsx(TableRoot, _extends({\n      as: component,\n      role: component === defaultComponent ? null : 'table',\n      ref: ref,\n      className: clsx(classes.root, className),\n      ownerState: ownerState\n    }, other))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Table.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the table, normally `TableHead` and `TableBody`.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Allows TableCells to inherit padding of the Table.\n   * @default 'normal'\n   */\n  padding: PropTypes.oneOf(['checkbox', 'none', 'normal']),\n  /**\n   * Allows TableCells to inherit size of the Table.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.string]),\n  /**\n   * Set the header sticky.\n   *\n   * ⚠️ It doesn't work with IE11.\n   * @default false\n   */\n  stickyHeader: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Table;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "TableContext", "useDefaultProps", "styled", "getTableUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "<PERSON><PERSON><PERSON><PERSON>", "slots", "root", "TableRoot", "name", "slot", "overridesResolver", "props", "styles", "theme", "display", "width", "borderCollapse", "borderSpacing", "typography", "body2", "padding", "spacing", "color", "vars", "palette", "text", "secondary", "textAlign", "captionSide", "defaultComponent", "Table", "forwardRef", "inProps", "ref", "className", "component", "size", "other", "table", "useMemo", "Provider", "value", "children", "as", "role", "process", "env", "NODE_ENV", "propTypes", "node", "object", "string", "elementType", "oneOf", "oneOfType", "bool", "sx", "arrayOf", "func"], "sources": ["D:/Coding_Projects/auth-service/react-ui/node_modules/@mui/material/Table/Table.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"component\", \"padding\", \"size\", \"stickyHeader\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport TableContext from './TableContext';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled from '../styles/styled';\nimport { getTableUtilityClass } from './tableClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    stickyHeader\n  } = ownerState;\n  const slots = {\n    root: ['root', stickyHeader && 'stickyHeader']\n  };\n  return composeClasses(slots, getTableUtilityClass, classes);\n};\nconst TableRoot = styled('table', {\n  name: 'MuiTable',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.stickyHeader && styles.stickyHeader];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  display: 'table',\n  width: '100%',\n  borderCollapse: 'collapse',\n  borderSpacing: 0,\n  '& caption': _extends({}, theme.typography.body2, {\n    padding: theme.spacing(2),\n    color: (theme.vars || theme).palette.text.secondary,\n    textAlign: 'left',\n    captionSide: 'bottom'\n  })\n}, ownerState.stickyHeader && {\n  borderCollapse: 'separate'\n}));\nconst defaultComponent = 'table';\nconst Table = /*#__PURE__*/React.forwardRef(function Table(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiTable'\n  });\n  const {\n      className,\n      component = defaultComponent,\n      padding = 'normal',\n      size = 'medium',\n      stickyHeader = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    component,\n    padding,\n    size,\n    stickyHeader\n  });\n  const classes = useUtilityClasses(ownerState);\n  const table = React.useMemo(() => ({\n    padding,\n    size,\n    stickyHeader\n  }), [padding, size, stickyHeader]);\n  return /*#__PURE__*/_jsx(TableContext.Provider, {\n    value: table,\n    children: /*#__PURE__*/_jsx(TableRoot, _extends({\n      as: component,\n      role: component === defaultComponent ? null : 'table',\n      ref: ref,\n      className: clsx(classes.root, className),\n      ownerState: ownerState\n    }, other))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Table.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the table, normally `TableHead` and `TableBody`.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Allows TableCells to inherit padding of the Table.\n   * @default 'normal'\n   */\n  padding: PropTypes.oneOf(['checkbox', 'none', 'normal']),\n  /**\n   * Allows TableCells to inherit size of the Table.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.string]),\n  /**\n   * Set the header sticky.\n   *\n   * ⚠️ It doesn't work with IE11.\n   * @default false\n   */\n  stickyHeader: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Table;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,cAAc,CAAC;AAC/E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,oBAAoB,QAAQ,gBAAgB;AACrD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEF,YAAY,IAAI,cAAc;EAC/C,CAAC;EACD,OAAOV,cAAc,CAACW,KAAK,EAAEP,oBAAoB,EAAEK,OAAO,CAAC;AAC7D,CAAC;AACD,MAAMI,SAAS,GAAGV,MAAM,CAAC,OAAO,EAAE;EAChCW,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJV;IACF,CAAC,GAAGS,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAEJ,UAAU,CAACE,YAAY,IAAIQ,MAAM,CAACR,YAAY,CAAC;EACtE;AACF,CAAC,CAAC,CAAC,CAAC;EACFS,KAAK;EACLX;AACF,CAAC,KAAKb,QAAQ,CAAC;EACbyB,OAAO,EAAE,OAAO;EAChBC,KAAK,EAAE,MAAM;EACbC,cAAc,EAAE,UAAU;EAC1BC,aAAa,EAAE,CAAC;EAChB,WAAW,EAAE5B,QAAQ,CAAC,CAAC,CAAC,EAAEwB,KAAK,CAACK,UAAU,CAACC,KAAK,EAAE;IAChDC,OAAO,EAAEP,KAAK,CAACQ,OAAO,CAAC,CAAC,CAAC;IACzBC,KAAK,EAAE,CAACT,KAAK,CAACU,IAAI,IAAIV,KAAK,EAAEW,OAAO,CAACC,IAAI,CAACC,SAAS;IACnDC,SAAS,EAAE,MAAM;IACjBC,WAAW,EAAE;EACf,CAAC;AACH,CAAC,EAAE1B,UAAU,CAACE,YAAY,IAAI;EAC5BY,cAAc,EAAE;AAClB,CAAC,CAAC,CAAC;AACH,MAAMa,gBAAgB,GAAG,OAAO;AAChC,MAAMC,KAAK,GAAG,aAAavC,KAAK,CAACwC,UAAU,CAAC,SAASD,KAAKA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACvE,MAAMtB,KAAK,GAAGf,eAAe,CAAC;IAC5Be,KAAK,EAAEqB,OAAO;IACdxB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACF0B,SAAS;MACTC,SAAS,GAAGN,gBAAgB;MAC5BT,OAAO,GAAG,QAAQ;MAClBgB,IAAI,GAAG,QAAQ;MACfhC,YAAY,GAAG;IACjB,CAAC,GAAGO,KAAK;IACT0B,KAAK,GAAGjD,6BAA6B,CAACuB,KAAK,EAAErB,SAAS,CAAC;EACzD,MAAMY,UAAU,GAAGb,QAAQ,CAAC,CAAC,CAAC,EAAEsB,KAAK,EAAE;IACrCwB,SAAS;IACTf,OAAO;IACPgB,IAAI;IACJhC;EACF,CAAC,CAAC;EACF,MAAMD,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMoC,KAAK,GAAG/C,KAAK,CAACgD,OAAO,CAAC,OAAO;IACjCnB,OAAO;IACPgB,IAAI;IACJhC;EACF,CAAC,CAAC,EAAE,CAACgB,OAAO,EAAEgB,IAAI,EAAEhC,YAAY,CAAC,CAAC;EAClC,OAAO,aAAaJ,IAAI,CAACL,YAAY,CAAC6C,QAAQ,EAAE;IAC9CC,KAAK,EAAEH,KAAK;IACZI,QAAQ,EAAE,aAAa1C,IAAI,CAACO,SAAS,EAAElB,QAAQ,CAAC;MAC9CsD,EAAE,EAAER,SAAS;MACbS,IAAI,EAAET,SAAS,KAAKN,gBAAgB,GAAG,IAAI,GAAG,OAAO;MACrDI,GAAG,EAAEA,GAAG;MACRC,SAAS,EAAEzC,IAAI,CAACU,OAAO,CAACG,IAAI,EAAE4B,SAAS,CAAC;MACxChC,UAAU,EAAEA;IACd,CAAC,EAAEmC,KAAK,CAAC;EACX,CAAC,CAAC;AACJ,CAAC,CAAC;AACFQ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGjB,KAAK,CAACkB,SAAS,CAAC,yBAAyB;EAC/E;EACA;EACA;EACA;EACA;AACF;AACA;EACEN,QAAQ,EAAElD,SAAS,CAACyD,IAAI;EACxB;AACF;AACA;EACE9C,OAAO,EAAEX,SAAS,CAAC0D,MAAM;EACzB;AACF;AACA;EACEhB,SAAS,EAAE1C,SAAS,CAAC2D,MAAM;EAC3B;AACF;AACA;AACA;EACEhB,SAAS,EAAE3C,SAAS,CAAC4D,WAAW;EAChC;AACF;AACA;AACA;EACEhC,OAAO,EAAE5B,SAAS,CAAC6D,KAAK,CAAC,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;EACxD;AACF;AACA;AACA;EACEjB,IAAI,EAAE5C,SAAS,CAAC,sCAAsC8D,SAAS,CAAC,CAAC9D,SAAS,CAAC6D,KAAK,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,EAAE7D,SAAS,CAAC2D,MAAM,CAAC,CAAC;EACzH;AACF;AACA;AACA;AACA;AACA;EACE/C,YAAY,EAAEZ,SAAS,CAAC+D,IAAI;EAC5B;AACF;AACA;EACEC,EAAE,EAAEhE,SAAS,CAAC8D,SAAS,CAAC,CAAC9D,SAAS,CAACiE,OAAO,CAACjE,SAAS,CAAC8D,SAAS,CAAC,CAAC9D,SAAS,CAACkE,IAAI,EAAElE,SAAS,CAAC0D,MAAM,EAAE1D,SAAS,CAAC+D,IAAI,CAAC,CAAC,CAAC,EAAE/D,SAAS,CAACkE,IAAI,EAAElE,SAAS,CAAC0D,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAepB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}