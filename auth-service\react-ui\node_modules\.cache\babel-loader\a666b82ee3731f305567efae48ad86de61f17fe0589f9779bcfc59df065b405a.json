{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTextFieldUtilityClass(slot) {\n  return generateUtilityClass('MuiTextField', slot);\n}\nconst textFieldClasses = generateUtilityClasses('MuiTextField', ['root']);\nexport default textFieldClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getTextFieldUtilityClass", "slot", "textFieldClasses"], "sources": ["D:/Coding_Projects/auth-service/react-ui/node_modules/@mui/material/TextField/textFieldClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTextFieldUtilityClass(slot) {\n  return generateUtilityClass('MuiTextField', slot);\n}\nconst textFieldClasses = generateUtilityClasses('MuiTextField', ['root']);\nexport default textFieldClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,wBAAwBA,CAACC,IAAI,EAAE;EAC7C,OAAOF,oBAAoB,CAAC,cAAc,EAAEE,IAAI,CAAC;AACnD;AACA,MAAMC,gBAAgB,GAAGJ,sBAAsB,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC;AACzE,eAAeI,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}