{"ast": null, "code": "export default function chainPropTypes(propType1, propType2) {\n  if (process.env.NODE_ENV === 'production') {\n    return () => null;\n  }\n  return function validate(...args) {\n    return propType1(...args) || propType2(...args);\n  };\n}", "map": {"version": 3, "names": ["chainPropTypes", "propType1", "propType2", "process", "env", "NODE_ENV", "validate", "args"], "sources": ["D:/Coding_Projects/auth-service/react-ui/node_modules/@mui/utils/esm/chainPropTypes/chainPropTypes.js"], "sourcesContent": ["export default function chainPropTypes(propType1, propType2) {\n  if (process.env.NODE_ENV === 'production') {\n    return () => null;\n  }\n  return function validate(...args) {\n    return propType1(...args) || propType2(...args);\n  };\n}"], "mappings": "AAAA,eAAe,SAASA,cAAcA,CAACC,SAAS,EAAEC,SAAS,EAAE;EAC3D,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,OAAO,MAAM,IAAI;EACnB;EACA,OAAO,SAASC,QAAQA,CAAC,GAAGC,IAAI,EAAE;IAChC,OAAON,SAAS,CAAC,GAAGM,IAAI,CAAC,IAAIL,SAAS,CAAC,GAAGK,IAAI,CAAC;EACjD,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}