{"ast": null, "code": "/**\n * Throttle decorator\n * @param {Function} fn\n * @param {Number} freq\n * @return {Function}\n */\nfunction throttle(fn, freq) {\n  let timestamp = 0;\n  let threshold = 1000 / freq;\n  let lastArgs;\n  let timer;\n  const invoke = (args, now = Date.now()) => {\n    timestamp = now;\n    lastArgs = null;\n    if (timer) {\n      clearTimeout(timer);\n      timer = null;\n    }\n    fn.apply(null, args);\n  };\n  const throttled = (...args) => {\n    const now = Date.now();\n    const passed = now - timestamp;\n    if (passed >= threshold) {\n      invoke(args, now);\n    } else {\n      lastArgs = args;\n      if (!timer) {\n        timer = setTimeout(() => {\n          timer = null;\n          invoke(lastArgs);\n        }, threshold - passed);\n      }\n    }\n  };\n  const flush = () => lastArgs && invoke(lastArgs);\n  return [throttled, flush];\n}\nexport default throttle;", "map": {"version": 3, "names": ["throttle", "fn", "freq", "timestamp", "threshold", "lastArgs", "timer", "invoke", "args", "now", "Date", "clearTimeout", "apply", "throttled", "passed", "setTimeout", "flush"], "sources": ["D:/Coding_Projects/auth-service/react-ui/node_modules/axios/lib/helpers/throttle.js"], "sourcesContent": ["/**\n * Throttle decorator\n * @param {Function} fn\n * @param {Number} freq\n * @return {Function}\n */\nfunction throttle(fn, freq) {\n  let timestamp = 0;\n  let threshold = 1000 / freq;\n  let lastArgs;\n  let timer;\n\n  const invoke = (args, now = Date.now()) => {\n    timestamp = now;\n    lastArgs = null;\n    if (timer) {\n      clearTimeout(timer);\n      timer = null;\n    }\n    fn.apply(null, args);\n  }\n\n  const throttled = (...args) => {\n    const now = Date.now();\n    const passed = now - timestamp;\n    if ( passed >= threshold) {\n      invoke(args, now);\n    } else {\n      lastArgs = args;\n      if (!timer) {\n        timer = setTimeout(() => {\n          timer = null;\n          invoke(lastArgs)\n        }, threshold - passed);\n      }\n    }\n  }\n\n  const flush = () => lastArgs && invoke(lastArgs);\n\n  return [throttled, flush];\n}\n\nexport default throttle;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAQA,CAACC,EAAE,EAAEC,IAAI,EAAE;EAC1B,IAAIC,SAAS,GAAG,CAAC;EACjB,IAAIC,SAAS,GAAG,IAAI,GAAGF,IAAI;EAC3B,IAAIG,QAAQ;EACZ,IAAIC,KAAK;EAET,MAAMC,MAAM,GAAGA,CAACC,IAAI,EAAEC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC,KAAK;IACzCN,SAAS,GAAGM,GAAG;IACfJ,QAAQ,GAAG,IAAI;IACf,IAAIC,KAAK,EAAE;MACTK,YAAY,CAACL,KAAK,CAAC;MACnBA,KAAK,GAAG,IAAI;IACd;IACAL,EAAE,CAACW,KAAK,CAAC,IAAI,EAAEJ,IAAI,CAAC;EACtB,CAAC;EAED,MAAMK,SAAS,GAAGA,CAAC,GAAGL,IAAI,KAAK;IAC7B,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IACtB,MAAMK,MAAM,GAAGL,GAAG,GAAGN,SAAS;IAC9B,IAAKW,MAAM,IAAIV,SAAS,EAAE;MACxBG,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC;IACnB,CAAC,MAAM;MACLJ,QAAQ,GAAGG,IAAI;MACf,IAAI,CAACF,KAAK,EAAE;QACVA,KAAK,GAAGS,UAAU,CAAC,MAAM;UACvBT,KAAK,GAAG,IAAI;UACZC,MAAM,CAACF,QAAQ,CAAC;QAClB,CAAC,EAAED,SAAS,GAAGU,MAAM,CAAC;MACxB;IACF;EACF,CAAC;EAED,MAAME,KAAK,GAAGA,CAAA,KAAMX,QAAQ,IAAIE,MAAM,CAACF,QAAQ,CAAC;EAEhD,OAAO,CAACQ,SAAS,EAAEG,KAAK,CAAC;AAC3B;AAEA,eAAehB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}