{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"only\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport capitalize from '../utils/capitalize';\nimport styled from '../styles/styled';\nimport useTheme from '../styles/useTheme';\nimport { getHiddenCssUtilityClass } from './hiddenCssClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    breakpoints\n  } = ownerState;\n  const slots = {\n    root: ['root', ...breakpoints.map(({\n      breakpoint,\n      dir\n    }) => {\n      return dir === 'only' ? `${dir}${capitalize(breakpoint)}` : `${breakpoint}${capitalize(dir)}`;\n    })]\n  };\n  return composeClasses(slots, getHiddenCssUtilityClass, classes);\n};\nconst HiddenCssRoot = styled('div', {\n  name: 'PrivateHiddenCss',\n  slot: 'Root'\n})(({\n  theme,\n  ownerState\n}) => {\n  const hidden = {\n    display: 'none'\n  };\n  return _extends({}, ownerState.breakpoints.map(({\n    breakpoint,\n    dir\n  }) => {\n    if (dir === 'only') {\n      return {\n        [theme.breakpoints.only(breakpoint)]: hidden\n      };\n    }\n    return dir === 'up' ? {\n      [theme.breakpoints.up(breakpoint)]: hidden\n    } : {\n      [theme.breakpoints.down(breakpoint)]: hidden\n    };\n  }).reduce((r, o) => {\n    Object.keys(o).forEach(k => {\n      r[k] = o[k];\n    });\n    return r;\n  }, {}));\n});\n\n/**\n * @ignore - internal component.\n */\nfunction HiddenCss(props) {\n  const {\n      children,\n      className,\n      only\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const theme = useTheme();\n  if (process.env.NODE_ENV !== 'production') {\n    const unknownProps = Object.keys(other).filter(propName => {\n      const isUndeclaredBreakpoint = !theme.breakpoints.keys.some(breakpoint => {\n        return `${breakpoint}Up` === propName || `${breakpoint}Down` === propName;\n      });\n      return !['classes', 'theme', 'isRtl', 'sx'].includes(propName) && isUndeclaredBreakpoint;\n    });\n    if (unknownProps.length > 0) {\n      console.error(`MUI: Unsupported props received by \\`<Hidden implementation=\"css\" />\\`: ${unknownProps.join(', ')}. Did you forget to wrap this component in a ThemeProvider declaring these breakpoints?`);\n    }\n  }\n  const breakpoints = [];\n  for (let i = 0; i < theme.breakpoints.keys.length; i += 1) {\n    const breakpoint = theme.breakpoints.keys[i];\n    const breakpointUp = other[`${breakpoint}Up`];\n    const breakpointDown = other[`${breakpoint}Down`];\n    if (breakpointUp) {\n      breakpoints.push({\n        breakpoint,\n        dir: 'up'\n      });\n    }\n    if (breakpointDown) {\n      breakpoints.push({\n        breakpoint,\n        dir: 'down'\n      });\n    }\n  }\n  if (only) {\n    const onlyBreakpoints = Array.isArray(only) ? only : [only];\n    onlyBreakpoints.forEach(breakpoint => {\n      breakpoints.push({\n        breakpoint,\n        dir: 'only'\n      });\n    });\n  }\n  const ownerState = _extends({}, props, {\n    breakpoints\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(HiddenCssRoot, {\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    children: children\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? HiddenCss.propTypes = {\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Specify which implementation to use.  'js' is the default, 'css' works better for\n   * server-side rendering.\n   */\n  implementation: PropTypes.oneOf(['js', 'css']),\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  lgDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  lgUp: PropTypes.bool,\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  mdDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  mdUp: PropTypes.bool,\n  /**\n   * Hide the given breakpoint(s).\n   */\n  only: PropTypes.oneOfType([PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']), PropTypes.arrayOf(PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']))]),\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  smDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  smUp: PropTypes.bool,\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  xlDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  xlUp: PropTypes.bool,\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  xsDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  xsUp: PropTypes.bool\n} : void 0;\nexport default HiddenCss;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "clsx", "PropTypes", "composeClasses", "capitalize", "styled", "useTheme", "getHiddenCssUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "breakpoints", "slots", "root", "map", "breakpoint", "dir", "HiddenCssRoot", "name", "slot", "theme", "hidden", "display", "only", "up", "down", "reduce", "r", "o", "Object", "keys", "for<PERSON>ach", "k", "HiddenCss", "props", "children", "className", "other", "process", "env", "NODE_ENV", "unknownProps", "filter", "propName", "isUndeclaredBreakpoint", "some", "includes", "length", "console", "error", "join", "i", "breakpointUp", "breakpointDown", "push", "onlyBreakpoints", "Array", "isArray", "propTypes", "node", "string", "implementation", "oneOf", "lgDown", "bool", "lgUp", "mdDown", "mdUp", "oneOfType", "arrayOf", "smDown", "smUp", "xlDown", "xlUp", "xsDown", "xsUp"], "sources": ["D:/Coding_Projects/auth-service/react-ui/node_modules/@mui/material/Hidden/HiddenCss.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"only\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport capitalize from '../utils/capitalize';\nimport styled from '../styles/styled';\nimport useTheme from '../styles/useTheme';\nimport { getHiddenCssUtilityClass } from './hiddenCssClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    breakpoints\n  } = ownerState;\n  const slots = {\n    root: ['root', ...breakpoints.map(({\n      breakpoint,\n      dir\n    }) => {\n      return dir === 'only' ? `${dir}${capitalize(breakpoint)}` : `${breakpoint}${capitalize(dir)}`;\n    })]\n  };\n  return composeClasses(slots, getHiddenCssUtilityClass, classes);\n};\nconst HiddenCssRoot = styled('div', {\n  name: 'PrivateHiddenCss',\n  slot: 'Root'\n})(({\n  theme,\n  ownerState\n}) => {\n  const hidden = {\n    display: 'none'\n  };\n  return _extends({}, ownerState.breakpoints.map(({\n    breakpoint,\n    dir\n  }) => {\n    if (dir === 'only') {\n      return {\n        [theme.breakpoints.only(breakpoint)]: hidden\n      };\n    }\n    return dir === 'up' ? {\n      [theme.breakpoints.up(breakpoint)]: hidden\n    } : {\n      [theme.breakpoints.down(breakpoint)]: hidden\n    };\n  }).reduce((r, o) => {\n    Object.keys(o).forEach(k => {\n      r[k] = o[k];\n    });\n    return r;\n  }, {}));\n});\n\n/**\n * @ignore - internal component.\n */\nfunction HiddenCss(props) {\n  const {\n      children,\n      className,\n      only\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const theme = useTheme();\n  if (process.env.NODE_ENV !== 'production') {\n    const unknownProps = Object.keys(other).filter(propName => {\n      const isUndeclaredBreakpoint = !theme.breakpoints.keys.some(breakpoint => {\n        return `${breakpoint}Up` === propName || `${breakpoint}Down` === propName;\n      });\n      return !['classes', 'theme', 'isRtl', 'sx'].includes(propName) && isUndeclaredBreakpoint;\n    });\n    if (unknownProps.length > 0) {\n      console.error(`MUI: Unsupported props received by \\`<Hidden implementation=\"css\" />\\`: ${unknownProps.join(', ')}. Did you forget to wrap this component in a ThemeProvider declaring these breakpoints?`);\n    }\n  }\n  const breakpoints = [];\n  for (let i = 0; i < theme.breakpoints.keys.length; i += 1) {\n    const breakpoint = theme.breakpoints.keys[i];\n    const breakpointUp = other[`${breakpoint}Up`];\n    const breakpointDown = other[`${breakpoint}Down`];\n    if (breakpointUp) {\n      breakpoints.push({\n        breakpoint,\n        dir: 'up'\n      });\n    }\n    if (breakpointDown) {\n      breakpoints.push({\n        breakpoint,\n        dir: 'down'\n      });\n    }\n  }\n  if (only) {\n    const onlyBreakpoints = Array.isArray(only) ? only : [only];\n    onlyBreakpoints.forEach(breakpoint => {\n      breakpoints.push({\n        breakpoint,\n        dir: 'only'\n      });\n    });\n  }\n  const ownerState = _extends({}, props, {\n    breakpoints\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(HiddenCssRoot, {\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    children: children\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? HiddenCss.propTypes = {\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Specify which implementation to use.  'js' is the default, 'css' works better for\n   * server-side rendering.\n   */\n  implementation: PropTypes.oneOf(['js', 'css']),\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  lgDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  lgUp: PropTypes.bool,\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  mdDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  mdUp: PropTypes.bool,\n  /**\n   * Hide the given breakpoint(s).\n   */\n  only: PropTypes.oneOfType([PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']), PropTypes.arrayOf(PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']))]),\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  smDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  smUp: PropTypes.bool,\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  xlDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  xlUp: PropTypes.bool,\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  xsDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  xsUp: PropTypes.bool\n} : void 0;\nexport default HiddenCss;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,MAAM,CAAC;AACnD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,SAASC,wBAAwB,QAAQ,oBAAoB;AAC7D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAE,GAAGF,WAAW,CAACG,GAAG,CAAC,CAAC;MACjCC,UAAU;MACVC;IACF,CAAC,KAAK;MACJ,OAAOA,GAAG,KAAK,MAAM,GAAG,GAAGA,GAAG,GAAGd,UAAU,CAACa,UAAU,CAAC,EAAE,GAAG,GAAGA,UAAU,GAAGb,UAAU,CAACc,GAAG,CAAC,EAAE;IAC/F,CAAC,CAAC;EACJ,CAAC;EACD,OAAOf,cAAc,CAACW,KAAK,EAAEP,wBAAwB,EAAEK,OAAO,CAAC;AACjE,CAAC;AACD,MAAMO,aAAa,GAAGd,MAAM,CAAC,KAAK,EAAE;EAClCe,IAAI,EAAE,kBAAkB;EACxBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC,CAAC;EACFC,KAAK;EACLX;AACF,CAAC,KAAK;EACJ,MAAMY,MAAM,GAAG;IACbC,OAAO,EAAE;EACX,CAAC;EACD,OAAO1B,QAAQ,CAAC,CAAC,CAAC,EAAEa,UAAU,CAACE,WAAW,CAACG,GAAG,CAAC,CAAC;IAC9CC,UAAU;IACVC;EACF,CAAC,KAAK;IACJ,IAAIA,GAAG,KAAK,MAAM,EAAE;MAClB,OAAO;QACL,CAACI,KAAK,CAACT,WAAW,CAACY,IAAI,CAACR,UAAU,CAAC,GAAGM;MACxC,CAAC;IACH;IACA,OAAOL,GAAG,KAAK,IAAI,GAAG;MACpB,CAACI,KAAK,CAACT,WAAW,CAACa,EAAE,CAACT,UAAU,CAAC,GAAGM;IACtC,CAAC,GAAG;MACF,CAACD,KAAK,CAACT,WAAW,CAACc,IAAI,CAACV,UAAU,CAAC,GAAGM;IACxC,CAAC;EACH,CAAC,CAAC,CAACK,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IAClBC,MAAM,CAACC,IAAI,CAACF,CAAC,CAAC,CAACG,OAAO,CAACC,CAAC,IAAI;MAC1BL,CAAC,CAACK,CAAC,CAAC,GAAGJ,CAAC,CAACI,CAAC,CAAC;IACb,CAAC,CAAC;IACF,OAAOL,CAAC;EACV,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACT,CAAC,CAAC;;AAEF;AACA;AACA;AACA,SAASM,SAASA,CAACC,KAAK,EAAE;EACxB,MAAM;MACFC,QAAQ;MACRC,SAAS;MACTb;IACF,CAAC,GAAGW,KAAK;IACTG,KAAK,GAAG1C,6BAA6B,CAACuC,KAAK,EAAErC,SAAS,CAAC;EACzD,MAAMuB,KAAK,GAAGhB,QAAQ,CAAC,CAAC;EACxB,IAAIkC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,YAAY,GAAGZ,MAAM,CAACC,IAAI,CAACO,KAAK,CAAC,CAACK,MAAM,CAACC,QAAQ,IAAI;MACzD,MAAMC,sBAAsB,GAAG,CAACxB,KAAK,CAACT,WAAW,CAACmB,IAAI,CAACe,IAAI,CAAC9B,UAAU,IAAI;QACxE,OAAO,GAAGA,UAAU,IAAI,KAAK4B,QAAQ,IAAI,GAAG5B,UAAU,MAAM,KAAK4B,QAAQ;MAC3E,CAAC,CAAC;MACF,OAAO,CAAC,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,CAACG,QAAQ,CAACH,QAAQ,CAAC,IAAIC,sBAAsB;IAC1F,CAAC,CAAC;IACF,IAAIH,YAAY,CAACM,MAAM,GAAG,CAAC,EAAE;MAC3BC,OAAO,CAACC,KAAK,CAAC,2EAA2ER,YAAY,CAACS,IAAI,CAAC,IAAI,CAAC,yFAAyF,CAAC;IAC5M;EACF;EACA,MAAMvC,WAAW,GAAG,EAAE;EACtB,KAAK,IAAIwC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/B,KAAK,CAACT,WAAW,CAACmB,IAAI,CAACiB,MAAM,EAAEI,CAAC,IAAI,CAAC,EAAE;IACzD,MAAMpC,UAAU,GAAGK,KAAK,CAACT,WAAW,CAACmB,IAAI,CAACqB,CAAC,CAAC;IAC5C,MAAMC,YAAY,GAAGf,KAAK,CAAC,GAAGtB,UAAU,IAAI,CAAC;IAC7C,MAAMsC,cAAc,GAAGhB,KAAK,CAAC,GAAGtB,UAAU,MAAM,CAAC;IACjD,IAAIqC,YAAY,EAAE;MAChBzC,WAAW,CAAC2C,IAAI,CAAC;QACfvC,UAAU;QACVC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ;IACA,IAAIqC,cAAc,EAAE;MAClB1C,WAAW,CAAC2C,IAAI,CAAC;QACfvC,UAAU;QACVC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ;EACF;EACA,IAAIO,IAAI,EAAE;IACR,MAAMgC,eAAe,GAAGC,KAAK,CAACC,OAAO,CAAClC,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;IAC3DgC,eAAe,CAACxB,OAAO,CAAChB,UAAU,IAAI;MACpCJ,WAAW,CAAC2C,IAAI,CAAC;QACfvC,UAAU;QACVC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EACA,MAAMP,UAAU,GAAGb,QAAQ,CAAC,CAAC,CAAC,EAAEsC,KAAK,EAAE;IACrCvB;EACF,CAAC,CAAC;EACF,MAAMD,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACU,aAAa,EAAE;IACtCmB,SAAS,EAAErC,IAAI,CAACW,OAAO,CAACG,IAAI,EAAEuB,SAAS,CAAC;IACxC3B,UAAU,EAAEA,UAAU;IACtB0B,QAAQ,EAAEA;EACZ,CAAC,CAAC;AACJ;AACAG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGP,SAAS,CAACyB,SAAS,GAAG;EAC5D;AACF;AACA;EACEvB,QAAQ,EAAEnC,SAAS,CAAC2D,IAAI;EACxB;AACF;AACA;EACEvB,SAAS,EAAEpC,SAAS,CAAC4D,MAAM;EAC3B;AACF;AACA;AACA;EACEC,cAAc,EAAE7D,SAAS,CAAC8D,KAAK,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;EAC9C;AACF;AACA;EACEC,MAAM,EAAE/D,SAAS,CAACgE,IAAI;EACtB;AACF;AACA;EACEC,IAAI,EAAEjE,SAAS,CAACgE,IAAI;EACpB;AACF;AACA;EACEE,MAAM,EAAElE,SAAS,CAACgE,IAAI;EACtB;AACF;AACA;EACEG,IAAI,EAAEnE,SAAS,CAACgE,IAAI;EACpB;AACF;AACA;EACEzC,IAAI,EAAEvB,SAAS,CAACoE,SAAS,CAAC,CAACpE,SAAS,CAAC8D,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE9D,SAAS,CAACqE,OAAO,CAACrE,SAAS,CAAC8D,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EAChJ;AACF;AACA;EACEQ,MAAM,EAAEtE,SAAS,CAACgE,IAAI;EACtB;AACF;AACA;EACEO,IAAI,EAAEvE,SAAS,CAACgE,IAAI;EACpB;AACF;AACA;EACEQ,MAAM,EAAExE,SAAS,CAACgE,IAAI;EACtB;AACF;AACA;EACES,IAAI,EAAEzE,SAAS,CAACgE,IAAI;EACpB;AACF;AACA;EACEU,MAAM,EAAE1E,SAAS,CAACgE,IAAI;EACtB;AACF;AACA;EACEW,IAAI,EAAE3E,SAAS,CAACgE;AAClB,CAAC,GAAG,KAAK,CAAC;AACV,eAAe/B,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}