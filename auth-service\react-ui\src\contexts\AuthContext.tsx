import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { AuthContextType, User, AuthTokens, LoginCredentials, TokenValidation } from '../types/auth';
import { authApi } from '../services/authApi';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [tokens, setTokens] = useState<AuthTokens | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check for existing tokens on app load
    const storedTokens = localStorage.getItem('auth_tokens');
    if (storedTokens) {
      try {
        const parsedTokens = JSON.parse(storedTokens);
        setTokens(parsedTokens);
        // Validate the token and get user info
        validateStoredToken(parsedTokens);
      } catch (error) {
        console.error('Error parsing stored tokens:', error);
        localStorage.removeItem('auth_tokens');
      }
    }
    setIsLoading(false);
  }, []);

  const validateStoredToken = async (storedTokens: AuthTokens) => {
    try {
      const validation = await authApi.validateToken(storedTokens.accessToken);
      if (validation.valid) {
        // Token is valid, create user object from validation
        const userData: User = {
          id: validation.userId,
          username: 'user', // We'll need to get this from a user info endpoint
          email: '<EMAIL>', // We'll need to get this from a user info endpoint
          isActive: true,
          emailVerified: true,
          createdAt: new Date().toISOString(),
        };
        setUser(userData);
      } else {
        // Token is invalid, clear storage
        localStorage.removeItem('auth_tokens');
        setTokens(null);
      }
    } catch (error) {
      console.error('Token validation failed:', error);
      localStorage.removeItem('auth_tokens');
      setTokens(null);
    }
  };

  const login = async (credentials: LoginCredentials) => {
    setIsLoading(true);
    try {
      const authTokens = await authApi.login(credentials);
      setTokens(authTokens);
      localStorage.setItem('auth_tokens', JSON.stringify(authTokens));
      
      // Create user object (in a real app, you'd fetch user details)
      const userData: User = {
        id: 'user-id', // This would come from token validation
        username: credentials.username,
        email: `${credentials.username}@example.com`,
        isActive: true,
        emailVerified: true,
        createdAt: new Date().toISOString(),
        lastLogin: new Date().toISOString(),
      };
      setUser(userData);
    } catch (error) {
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    if (tokens?.accessToken) {
      try {
        await authApi.revokeToken(tokens.accessToken);
      } catch (error) {
        console.error('Error revoking token:', error);
      }
    }
    
    setUser(null);
    setTokens(null);
    localStorage.removeItem('auth_tokens');
  };

  const refreshToken = async () => {
    if (!tokens?.refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const newTokens = await authApi.refreshToken(tokens.refreshToken);
      setTokens(newTokens);
      localStorage.setItem('auth_tokens', JSON.stringify(newTokens));
    } catch (error) {
      // Refresh failed, logout user
      logout();
      throw error;
    }
  };

  const validateToken = async (): Promise<TokenValidation> => {
    if (!tokens?.accessToken) {
      throw new Error('No access token available');
    }

    return await authApi.validateToken(tokens.accessToken);
  };

  const value: AuthContextType = {
    user,
    tokens,
    isAuthenticated: !!user && !!tokens,
    isLoading,
    login,
    logout,
    refreshToken,
    validateToken,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
