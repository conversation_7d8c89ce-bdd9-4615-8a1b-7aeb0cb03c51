import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Grid,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Alert,
  LinearProgress,
} from '@mui/material';
import {
  ArrowBack,
  People,
  Security,
  Analytics,
  Settings,
  Warning,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';

// Mock data for demonstration
const mockUsers = [
  { id: '1', username: 'testuser', email: '<EMAIL>', status: 'active', lastLogin: '2024-01-15 10:30:00' },
  { id: '2', username: 'admin', email: '<EMAIL>', status: 'active', lastLogin: '2024-01-15 09:15:00' },
  { id: '3', username: 'demo', email: '<EMAIL>', status: 'inactive', lastLogin: '2024-01-10 14:20:00' },
];

const mockStats = {
  totalUsers: 3,
  activeUsers: 2,
  totalTokens: 15,
  failedLogins: 2,
  rateLimitHits: 5,
};

const AdminPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => setLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  const handleBack = () => {
    navigate('/dashboard');
  };

  if (loading) {
    return (
      <Card sx={{ width: '100%', maxWidth: 800, minHeight: 400 }}>
        <CardContent>
          <Typography variant="h5" gutterBottom>
            Loading Admin Panel...
          </Typography>
          <LinearProgress />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card sx={{ width: '100%', maxWidth: 1000, maxHeight: '90vh', overflow: 'auto' }}>
      <CardContent sx={{ p: 3 }}>
        {/* Header */}
        <Box display="flex" alignItems="center" mb={3}>
          <Button
            startIcon={<ArrowBack />}
            onClick={handleBack}
            sx={{ mr: 2 }}
          >
            Back
          </Button>
          <Box>
            <Typography variant="h4" component="h1">
              Admin Panel
            </Typography>
            <Typography variant="body2" color="text.secondary">
              OAuth 2.0 Service Administration
            </Typography>
          </Box>
        </Box>

        {/* Warning */}
        <Alert severity="info" sx={{ mb: 3 }}>
          <strong>Demo Mode:</strong> This is a demonstration admin interface. 
          Real user management and analytics would be implemented here.
        </Alert>

        {/* Stats Grid */}
        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <People sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
              <Typography variant="h4">{mockStats.totalUsers}</Typography>
              <Typography variant="body2" color="text.secondary">
                Total Users
              </Typography>
            </Paper>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Security sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
              <Typography variant="h4">{mockStats.totalTokens}</Typography>
              <Typography variant="body2" color="text.secondary">
                Active Tokens
              </Typography>
            </Paper>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Warning sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
              <Typography variant="h4">{mockStats.failedLogins}</Typography>
              <Typography variant="body2" color="text.secondary">
                Failed Logins
              </Typography>
            </Paper>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Analytics sx={{ fontSize: 40, color: 'error.main', mb: 1 }} />
              <Typography variant="h4">{mockStats.rateLimitHits}</Typography>
              <Typography variant="body2" color="text.secondary">
                Rate Limit Hits
              </Typography>
            </Paper>
          </Grid>
        </Grid>

        {/* User Management */}
        <Typography variant="h6" gutterBottom>
          User Management
        </Typography>
        <TableContainer component={Paper} sx={{ mb: 3 }}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Username</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Last Login</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {mockUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>{user.username}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>
                    <Chip
                      label={user.status}
                      color={user.status === 'active' ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>{user.lastLogin}</TableCell>
                  <TableCell>
                    <Button size="small" variant="outlined">
                      Manage
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Quick Actions */}
        <Typography variant="h6" gutterBottom>
          Quick Actions
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<People />}
              onClick={() => alert('User registration feature coming soon!')}
            >
              Add User
            </Button>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Security />}
              onClick={() => alert('Token management feature coming soon!')}
            >
              Manage Tokens
            </Button>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Analytics />}
              onClick={() => alert('Analytics dashboard coming soon!')}
            >
              View Analytics
            </Button>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Settings />}
              onClick={() => alert('System settings coming soon!')}
            >
              Settings
            </Button>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

export default AdminPage;
