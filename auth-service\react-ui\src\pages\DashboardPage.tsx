import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  CardContent,
  Button,
  Typography,
  Box,
  Alert,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Paper,
} from '@mui/material';
import {
  Logout,
  Security,
  Refresh,
  AdminPanelSettings,
  CheckCircle,
  Info,
  Close,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { authApi } from '../services/authApi';
import { HealthStatus, TokenValidation } from '../types/auth';

const DashboardPage: React.FC = () => {
  const [successMessage, setSuccessMessage] = useState('Login successful!');
  const [modalOpen, setModalOpen] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [modalContent, setModalContent] = useState<React.ReactNode>(null);
  const { user, tokens, logout, refreshToken, validateToken } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    // Clear success message after 5 seconds
    const timer = setTimeout(() => setSuccessMessage(''), 5000);
    return () => clearTimeout(timer);
  }, []);

  const handleLogout = async () => {
    await logout();
    navigate('/login');
  };

  const handleHealthCheck = async () => {
    try {
      const health = await authApi.getHealth();
      setModalTitle('Service Health Check');
      setModalContent(
        <Box>
          <Alert severity="success" sx={{ mb: 2 }}>
            <strong>Service is healthy!</strong>
          </Alert>
          <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
            <Typography variant="body2" component="pre" sx={{ fontFamily: 'monospace' }}>
              {JSON.stringify(health, null, 2)}
            </Typography>
          </Paper>
        </Box>
      );
      setModalOpen(true);
    } catch (error: any) {
      setModalTitle('Health Check Failed');
      setModalContent(
        <Alert severity="error">
          {error.error_description || 'Health check failed'}
        </Alert>
      );
      setModalOpen(true);
    }
  };

  const handleValidateToken = async () => {
    try {
      const validation = await validateToken();
      setModalTitle('Token Validation Results');
      setModalContent(
        <Box>
          <Alert severity="success" sx={{ mb: 2 }}>
            <strong>Token is valid!</strong>
          </Alert>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Typography variant="subtitle2" color="primary">
                User Information
              </Typography>
              <Typography variant="body2">User ID: {validation.userId}</Typography>
              <Typography variant="body2">
                Scopes: {validation.scopes.join(', ')}
              </Typography>
              <Typography variant="body2">
                Expires: {new Date(validation.expiresAt * 1000).toLocaleString()}
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle2" color="primary" sx={{ mt: 2 }}>
                Raw Response
              </Typography>
              <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                <Typography variant="body2" component="pre" sx={{ fontFamily: 'monospace' }}>
                  {JSON.stringify(validation, null, 2)}
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        </Box>
      );
      setModalOpen(true);
    } catch (error: any) {
      setModalTitle('Token Validation Failed');
      setModalContent(
        <Alert severity="error">
          {error.error_description || 'Token validation failed'}
        </Alert>
      );
      setModalOpen(true);
    }
  };

  const handleRefreshToken = async () => {
    try {
      await refreshToken();
      setModalTitle('Token Refresh Successful');
      setModalContent(
        <Box>
          <Alert severity="success" sx={{ mb: 2 }}>
            <strong>Token refreshed successfully!</strong>
          </Alert>
          <Typography variant="body2">
            New access token has been generated and stored.
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Token Type: {tokens?.tokenType || 'Bearer'}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Expires In: {tokens?.expiresIn || 3600} seconds
          </Typography>
        </Box>
      );
      setModalOpen(true);
    } catch (error: any) {
      setModalTitle('Token Refresh Failed');
      setModalContent(
        <Alert severity="error">
          {error.error_description || 'Token refresh failed'}
        </Alert>
      );
      setModalOpen(true);
    }
  };

  const handleAdminPanel = () => {
    navigate('/admin');
  };

  return (
    <Card
      sx={{
        width: '100%',
        maxWidth: 420,
        height: 'auto',
        maxHeight: '90vh',
        overflow: 'hidden',
      }}
    >
      <CardContent sx={{ p: 3 }}>
        {/* Success Message */}
        {successMessage && (
          <Alert
            severity="success"
            sx={{ mb: 2, height: 'auto' }}
            icon={<CheckCircle />}
          >
            {successMessage}
          </Alert>
        )}

        {/* User Info */}
        <Box textAlign="center" mb={3}>
          <Security sx={{ fontSize: 48, color: 'primary.main', mb: 1 }} />
          <Typography variant="h5" component="h1" gutterBottom>
            Welcome, {user?.username}!
          </Typography>
          <Chip
            label="Authenticated"
            color="success"
            size="small"
            icon={<CheckCircle />}
          />
        </Box>

        {/* Action Buttons */}
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Info />}
              onClick={handleHealthCheck}
              sx={{ mb: 1 }}
            >
              Health Check
            </Button>
          </Grid>
          
          <Grid item xs={12}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Security />}
              onClick={handleValidateToken}
              sx={{ mb: 1 }}
            >
              Validate Token
            </Button>
          </Grid>
          
          <Grid item xs={12}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Refresh />}
              onClick={handleRefreshToken}
              sx={{ mb: 1 }}
            >
              Refresh Token
            </Button>
          </Grid>
          
          <Grid item xs={12}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<AdminPanelSettings />}
              onClick={handleAdminPanel}
              sx={{ mb: 2 }}
            >
              Admin Panel
            </Button>
          </Grid>
          
          <Grid item xs={12}>
            <Button
              fullWidth
              variant="contained"
              color="secondary"
              startIcon={<Logout />}
              onClick={handleLogout}
            >
              Logout
            </Button>
          </Grid>
        </Grid>
      </CardContent>

      {/* Modal Dialog */}
      <Dialog
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          {modalTitle}
          <IconButton onClick={() => setModalOpen(false)}>
            <Close />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          {modalContent}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setModalOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Card>
  );
};

export default DashboardPage;
