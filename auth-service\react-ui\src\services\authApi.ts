import axios, { AxiosResponse } from 'axios';
import { AuthTokens, LoginCredentials, TokenValidation, HealthStatus, ApiError } from '../types/auth';

const API_BASE_URL = process.env.NODE_ENV === 'production' ? '' : 'https://auth-dev.chcit.org';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use((config) => {
  const tokens = localStorage.getItem('auth_tokens');
  if (tokens) {
    try {
      const parsedTokens = JSON.parse(tokens);
      config.headers.Authorization = `Bearer ${parsedTokens.accessToken}`;
    } catch (error) {
      console.error('Error parsing auth tokens:', error);
    }
  }
  return config;
});

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('auth_tokens');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export const authApi = {
  async login(credentials: LoginCredentials): Promise<AuthTokens> {
    try {
      const response: AxiosResponse<AuthTokens> = await api.post('/oauth/token', {
        username: credentials.username,
        password: credentials.password,
        grant_type: 'password',
      });
      return response.data;
    } catch (error: any) {
      const apiError: ApiError = {
        error: error.response?.data?.error || 'login_failed',
        error_description: error.response?.data?.error_description || 'Login failed',
        status: error.response?.status,
      };
      throw apiError;
    }
  },

  async refreshToken(refreshToken: string): Promise<AuthTokens> {
    try {
      const response: AxiosResponse<AuthTokens> = await api.post('/oauth/refresh', {
        refresh_token: refreshToken,
        grant_type: 'refresh_token',
      });
      return response.data;
    } catch (error: any) {
      const apiError: ApiError = {
        error: error.response?.data?.error || 'refresh_failed',
        error_description: error.response?.data?.error_description || 'Token refresh failed',
        status: error.response?.status,
      };
      throw apiError;
    }
  },

  async validateToken(accessToken: string): Promise<TokenValidation> {
    try {
      const response: AxiosResponse<TokenValidation> = await api.post('/oauth/validate', {
        token: accessToken,
      });
      return response.data;
    } catch (error: any) {
      const apiError: ApiError = {
        error: error.response?.data?.error || 'validation_failed',
        error_description: error.response?.data?.error_description || 'Token validation failed',
        status: error.response?.status,
      };
      throw apiError;
    }
  },

  async revokeToken(accessToken: string): Promise<void> {
    try {
      await api.post('/oauth/revoke', {
        token: accessToken,
      });
    } catch (error: any) {
      const apiError: ApiError = {
        error: error.response?.data?.error || 'revoke_failed',
        error_description: error.response?.data?.error_description || 'Token revocation failed',
        status: error.response?.status,
      };
      throw apiError;
    }
  },

  async getHealth(): Promise<HealthStatus> {
    try {
      const response: AxiosResponse<HealthStatus> = await api.get('/health');
      return response.data;
    } catch (error: any) {
      const apiError: ApiError = {
        error: error.response?.data?.error || 'health_check_failed',
        error_description: error.response?.data?.error_description || 'Health check failed',
        status: error.response?.status,
      };
      throw apiError;
    }
  },
};
