export interface User {
  id: string;
  username: string;
  email: string;
  isActive: boolean;
  emailVerified: boolean;
  createdAt: string;
  lastLogin?: string;
  isAdmin?: boolean;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: string;
  scope: string;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface TokenValidation {
  valid: boolean;
  userId: string;
  scopes: string[];
  expiresAt: number;
}

export interface AuthContextType {
  user: User | null;
  tokens: AuthTokens | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  validateToken: () => Promise<TokenValidation>;
}

export interface ApiError {
  error: string;
  error_description: string;
  status?: number;
}

export interface HealthStatus {
  status: string;
  service: string;
  version: string;
  oauth2_endpoints: string[];
  timestamp: number;
}

export interface RateLimitInfo {
  remaining: number;
  limit: number;
  resetTime: number;
}
