<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth-Service Test Login</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-header h1 {
            color: #333;
            margin-bottom: 8px;
            font-size: 28px;
        }

        .login-header p {
            color: #666;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .login-button {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 14px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .login-button:hover {
            transform: translateY(-2px);
        }

        .login-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .message {
            margin-top: 20px;
            padding: 12px;
            border-radius: 8px;
            font-size: 14px;
        }

        .error {
            background-color: #fee;
            color: #c33;
            border: 1px solid #fcc;
        }

        .success {
            background-color: #efe;
            color: #363;
            border: 1px solid #cfc;
        }

        .info {
            background-color: #eef;
            color: #336;
            border: 1px solid #ccf;
        }

        .test-section {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .test-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .test-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .test-button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #f8f9fa;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s ease;
        }

        .test-button:hover {
            background: #e9ecef;
        }

        .response-area {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .server-config {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #ddd;
        }

        .server-config label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }

        .server-config input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>🔐 Auth-Service Test</h1>
            <p>OAuth 2.0 Authentication Testing Interface</p>
        </div>

        <div class="server-config">
            <label for="serverUrl">Auth-Service URL:</label>
            <input type="text" id="serverUrl" value="http://auth-dev.chcit.org:8082" placeholder="http://localhost:8082">
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" value="testuser" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" value="testpass123" required>
            </div>
            
            <button type="submit" class="login-button" id="loginButton">
                🚀 Test Login (OAuth 2.0)
            </button>
        </form>

        <div class="test-section">
            <h3>🧪 API Testing</h3>
            <div class="test-buttons">
                <button class="test-button" onclick="testHealth()">Health Check</button>
                <button class="test-button" onclick="testToken()">Get Token</button>
                <button class="test-button" onclick="testValidate()">Validate Token</button>
                <button class="test-button" onclick="testRefresh()">Refresh Token</button>
            </div>
            <div class="response-area" id="responseArea">Click a test button to see API responses...</div>
        </div>

        <div id="message"></div>
    </div>

    <script>
        let currentTokens = {
            access_token: null,
            refresh_token: null
        };

        function getServerUrl() {
            return document.getElementById('serverUrl').value.trim();
        }

        function showMessage(text, type = 'info') {
            const messageDiv = document.getElementById('message');
            messageDiv.textContent = text;
            messageDiv.className = `message ${type}`;
        }

        function updateResponse(text) {
            document.getElementById('responseArea').textContent = text;
        }

        async function makeRequest(endpoint, options = {}) {
            const url = `${getServerUrl()}${endpoint}`;
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                }
            };

            try {
                updateResponse(`Making request to: ${url}\nMethod: ${options.method || 'GET'}\n\nWaiting for response...`);
                
                const response = await fetch(url, { ...defaultOptions, ...options });
                const responseText = await response.text();
                
                let responseData;
                try {
                    responseData = JSON.parse(responseText);
                } catch {
                    responseData = responseText;
                }

                const result = {
                    status: response.status,
                    statusText: response.statusText,
                    data: responseData,
                    headers: Object.fromEntries(response.headers.entries())
                };

                updateResponse(`Response Status: ${response.status} ${response.statusText}\n\n${JSON.stringify(result.data, null, 2)}`);
                return result;
            } catch (error) {
                const errorText = `Error: ${error.message}\n\nThis might be due to:\n- Auth-service not running\n- CORS policy\n- Network connectivity\n- Wrong server URL`;
                updateResponse(errorText);
                throw error;
            }
        }

        async function testHealth() {
            try {
                const result = await makeRequest('/health');
                if (result.status === 200) {
                    showMessage('✅ Health check successful! Auth-service is running.', 'success');
                } else {
                    showMessage(`⚠️ Health check returned status ${result.status}`, 'error');
                }
            } catch (error) {
                showMessage('❌ Health check failed. Is the auth-service running?', 'error');
            }
        }

        async function testToken() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                showMessage('Please enter username and password', 'error');
                return;
            }

            try {
                const result = await makeRequest('/oauth/token', {
                    method: 'POST',
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        grant_type: 'password'
                    })
                });

                if (result.status === 200 && result.data.access_token) {
                    currentTokens.access_token = result.data.access_token;
                    currentTokens.refresh_token = result.data.refresh_token;
                    showMessage('✅ Token generated successfully!', 'success');
                } else {
                    showMessage(`❌ Token generation failed: ${result.data.error || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                showMessage('❌ Token request failed', 'error');
            }
        }

        async function testValidate() {
            if (!currentTokens.access_token) {
                showMessage('No access token available. Generate a token first.', 'error');
                return;
            }

            try {
                const result = await makeRequest('/oauth/validate', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${currentTokens.access_token}`
                    }
                });

                if (result.status === 200) {
                    showMessage('✅ Token validation successful!', 'success');
                } else {
                    showMessage(`❌ Token validation failed: ${result.data.error || 'Invalid token'}`, 'error');
                }
            } catch (error) {
                showMessage('❌ Token validation request failed', 'error');
            }
        }

        async function testRefresh() {
            if (!currentTokens.refresh_token) {
                showMessage('No refresh token available. Generate a token first.', 'error');
                return;
            }

            try {
                const result = await makeRequest('/oauth/refresh', {
                    method: 'POST',
                    body: JSON.stringify({
                        refresh_token: currentTokens.refresh_token,
                        grant_type: 'refresh_token'
                    })
                });

                if (result.status === 200 && result.data.access_token) {
                    currentTokens.access_token = result.data.access_token;
                    if (result.data.refresh_token) {
                        currentTokens.refresh_token = result.data.refresh_token;
                    }
                    showMessage('✅ Token refresh successful!', 'success');
                } else {
                    showMessage(`❌ Token refresh failed: ${result.data.error || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                showMessage('❌ Token refresh request failed', 'error');
            }
        }

        // Handle form submission
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            await testToken();
        });

        // Test health on page load
        window.addEventListener('load', () => {
            setTimeout(testHealth, 1000);
        });
    </script>
</body>
</html>
