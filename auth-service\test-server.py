#!/usr/bin/env python3
"""
Simple HTTP server for testing auth-service OAuth 2.0 functionality
This server simulates the auth-service responses for testing purposes
"""

import json
import time
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import hashlib
import base64
import secrets

class AuthServiceHandler(BaseHTTPRequestHandler):
    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.send_cors_headers()
        self.end_headers()

    def do_GET(self):
        """Handle GET requests"""
        path = urlparse(self.path).path
        
        if path == '/health':
            self.handle_health()
        else:
            self.send_error(404, "Not Found")

    def do_POST(self):
        """Handle POST requests"""
        path = urlparse(self.path).path
        
        if path == '/oauth/token':
            self.handle_token()
        elif path == '/oauth/refresh':
            self.handle_refresh()
        elif path == '/oauth/validate':
            self.handle_validate()
        elif path == '/oauth/revoke':
            self.handle_revoke()
        else:
            self.send_error(404, "Not Found")

    def send_cors_headers(self):
        """Send CORS headers"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')

    def send_json_response(self, data, status_code=200):
        """Send JSON response with CORS headers"""
        response = json.dumps(data, indent=2)
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.send_cors_headers()
        self.end_headers()
        self.wfile.write(response.encode('utf-8'))

    def get_request_body(self):
        """Get request body as JSON"""
        content_length = int(self.headers.get('Content-Length', 0))
        if content_length > 0:
            body = self.rfile.read(content_length)
            try:
                return json.loads(body.decode('utf-8'))
            except json.JSONDecodeError:
                return {}
        return {}

    def handle_health(self):
        """Handle health check endpoint"""
        health_data = {
            "service": "auth-service",
            "status": "healthy",
            "version": "1.0.0",
            "timestamp": int(time.time()),
            "oauth2_endpoints": [
                "/oauth/token",
                "/oauth/refresh", 
                "/oauth/validate",
                "/oauth/revoke"
            ]
        }
        self.send_json_response(health_data)

    def handle_token(self):
        """Handle OAuth 2.0 token generation"""
        data = self.get_request_body()
        
        username = data.get('username', '')
        password = data.get('password', '')
        grant_type = data.get('grant_type', '')

        # Simulate authentication
        if grant_type == 'password' and username == 'testuser' and password == 'testpass123':
            # Generate mock tokens
            access_token = self.generate_mock_jwt('access')
            refresh_token = self.generate_mock_jwt('refresh')
            
            token_response = {
                "access_token": access_token,
                "token_type": "Bearer",
                "expires_in": 3600,
                "refresh_token": refresh_token,
                "scope": "openid profile email"
            }
            self.send_json_response(token_response)
        else:
            error_response = {
                "error": "invalid_grant",
                "error_description": "Invalid username or password"
            }
            self.send_json_response(error_response, 401)

    def handle_refresh(self):
        """Handle token refresh"""
        data = self.get_request_body()
        
        refresh_token = data.get('refresh_token', '')
        grant_type = data.get('grant_type', '')

        if grant_type == 'refresh_token' and refresh_token:
            # Generate new access token
            access_token = self.generate_mock_jwt('access')
            
            token_response = {
                "access_token": access_token,
                "token_type": "Bearer",
                "expires_in": 3600,
                "scope": "openid profile email"
            }
            self.send_json_response(token_response)
        else:
            error_response = {
                "error": "invalid_grant",
                "error_description": "Invalid refresh token"
            }
            self.send_json_response(error_response, 401)

    def handle_validate(self):
        """Handle token validation"""
        auth_header = self.headers.get('Authorization', '')
        
        if auth_header.startswith('Bearer ') and len(auth_header) > 7:
            token = auth_header[7:]
            
            # Simple token validation (check if it looks like our mock JWT)
            if token.startswith('eyJ') and token.count('.') == 2:
                validation_response = {
                    "valid": True,
                    "user_id": "test-user-123",
                    "username": "testuser",
                    "scope": "openid profile email",
                    "expires_at": int(time.time()) + 3600
                }
                self.send_json_response(validation_response)
            else:
                error_response = {
                    "error": "invalid_token",
                    "error_description": "Invalid token signature"
                }
                self.send_json_response(error_response, 401)
        else:
            error_response = {
                "error": "invalid_token",
                "error_description": "Missing or invalid authorization header"
            }
            self.send_json_response(error_response, 401)

    def handle_revoke(self):
        """Handle token revocation"""
        data = self.get_request_body()
        
        token = data.get('token', '')
        
        if token:
            revoke_response = {
                "revoked": True,
                "message": "Token successfully revoked"
            }
            self.send_json_response(revoke_response)
        else:
            error_response = {
                "error": "invalid_request",
                "error_description": "Missing token parameter"
            }
            self.send_json_response(error_response, 400)

    def generate_mock_jwt(self, token_type):
        """Generate a mock JWT token"""
        # JWT Header
        header = {
            "alg": "HS256",
            "typ": "JWT"
        }
        
        # JWT Payload
        payload = {
            "iss": "auth.chcit.org",
            "sub": "test-user-123",
            "aud": "auth-service",
            "exp": int(time.time()) + (3600 if token_type == 'access' else 604800),
            "iat": int(time.time()),
            "token_type": token_type,
            "username": "testuser"
        }
        
        # Base64 encode header and payload
        header_b64 = base64.urlsafe_b64encode(json.dumps(header).encode()).decode().rstrip('=')
        payload_b64 = base64.urlsafe_b64encode(json.dumps(payload).encode()).decode().rstrip('=')
        
        # Create signature (mock)
        signature_data = f"{header_b64}.{payload_b64}"
        signature = hashlib.sha256(signature_data.encode()).hexdigest()[:32]
        signature_b64 = base64.urlsafe_b64encode(signature.encode()).decode().rstrip('=')
        
        return f"{header_b64}.{payload_b64}.{signature_b64}"

    def log_message(self, format, *args):
        """Override to provide better logging"""
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

def run_server(port=8082):
    """Run the test server"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, AuthServiceHandler)
    
    print(f"🚀 Auth-Service Test Server starting on port {port}")
    print(f"📍 Health endpoint: http://localhost:{port}/health")
    print(f"🔐 OAuth endpoints: http://localhost:{port}/oauth/*")
    print(f"🧪 Test credentials: testuser / testpass123")
    print(f"⏹️  Press Ctrl+C to stop")
    print()
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped")
        httpd.server_close()

if __name__ == '__main__':
    run_server()
