#!/bin/bash
echo "Getting access token..."
TOKEN=$(curl -s -X POST http://localhost/oauth/token -H 'Content-Type: application/json' -d @/tmp/test_login.json | jq -r '.access_token')
echo "Token obtained: ${TOKEN:0:50}..."

echo "Testing token validation..."
curl -s -X POST http://localhost/oauth/validate \
  -H 'Content-Type: application/json' \
  -H "Authorization: Bearer $TOKEN" \
  -d "{\"token\":\"$TOKEN\"}" | jq . 2>/dev/null || \
curl -s -X POST http://localhost/oauth/validate \
  -H 'Content-Type: application/json' \
  -H "Authorization: Bearer $TOKEN" \
  -d "{\"token\":\"$TOKEN\"}"
