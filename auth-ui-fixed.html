<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth Service - Complete Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 900px;
            min-height: 600px;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .content-area {
            padding: 30px;
            min-height: 500px;
            display: flex;
            flex-direction: column;
        }

        .hidden {
            display: none !important;
        }

        /* Login Form Styles */
        .form-group {
            margin-bottom: 20px;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4facfe;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            margin: 5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        /* Dashboard Styles */
        .dashboard-nav {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .nav-btn {
            padding: 10px 20px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .nav-btn:hover, .nav-btn.active {
            background: #4facfe;
            color: white;
        }

        .dashboard-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .user-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .info-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4facfe;
        }

        .token-display {
            background: #f1f3f4;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            word-break: break-all;
            margin: 10px 0;
            max-height: 100px;
            overflow-y: auto;
        }

        .admin-panel {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .admin-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online { background: #28a745; }
        .status-offline { background: #dc3545; }

        .alert {
            padding: 12px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .forgot-password {
            text-align: center;
            margin-top: 15px;
        }

        .forgot-password a {
            color: #4facfe;
            text-decoration: none;
            font-size: 14px;
        }

        .forgot-password a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Auth Service Dashboard</h1>
            <p>Complete OAuth 2.0 Authentication System</p>
            <div style="margin-top: 10px;">
                <span class="status-indicator status-online"></span>
                <span id="connection-status">Connected</span>
            </div>
        </div>

        <div class="content-area">
            <!-- Login Form -->
            <div id="login-form">
                <h2 style="text-align: center; margin-bottom: 30px;">Sign In</h2>
                
                <div class="form-group">
                    <input type="text" id="username" placeholder="Username" required>
                </div>
                
                <div class="form-group">
                    <input type="password" id="password" placeholder="Password" required>
                </div>
                
                <div style="text-align: center;">
                    <button class="btn btn-primary" onclick="login()">Sign In</button>
                </div>

                <div class="forgot-password">
                    <a href="#" onclick="alert('Password reset functionality coming soon!')">Forgot Password?</a>
                </div>

                <div id="login-message"></div>
            </div>

            <!-- Dashboard -->
            <div id="dashboard" class="hidden">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h2>Welcome, <span id="user-display">User</span>!</h2>
                    <button class="btn btn-secondary" onclick="logout()">Logout</button>
                </div>

                <div class="dashboard-nav">
                    <div class="nav-btn active" onclick="showDashboardSection('overview')">Overview</div>
                    <div class="nav-btn" onclick="showDashboardSection('tokens')">Token Management</div>
                    <div class="nav-btn" onclick="showDashboardSection('profile')">Profile</div>
                    <div class="nav-btn" onclick="showDashboardSection('admin')">Admin Panel</div>
                    <div class="nav-btn" onclick="showDashboardSection('health')">System Health</div>
                </div>

                <!-- Overview Section -->
                <div id="section-overview" class="dashboard-section">
                    <h3>Account Overview</h3>
                    <div class="user-info">
                        <div class="info-card">
                            <h4>User ID</h4>
                            <p id="user-id">12345</p>
                        </div>
                        <div class="info-card">
                            <h4>Email</h4>
                            <p id="user-email"><EMAIL></p>
                        </div>
                        <div class="info-card">
                            <h4>Account Status</h4>
                            <p id="user-status">Active</p>
                        </div>
                        <div class="info-card">
                            <h4>Last Login</h4>
                            <p id="last-login">Just now</p>
                        </div>
                    </div>
                </div>

                <!-- Token Management Section -->
                <div id="section-tokens" class="dashboard-section hidden">
                    <h3>Token Management</h3>
                    <div style="margin-bottom: 20px;">
                        <button class="btn btn-primary" onclick="refreshToken()">Refresh Token</button>
                        <button class="btn btn-secondary" onclick="validateCurrentToken()">Validate Token</button>
                        <button class="btn btn-danger" onclick="revokeToken()">Revoke Token</button>
                    </div>
                    
                    <div>
                        <h4>Current Access Token:</h4>
                        <div class="token-display" id="current-token">No token available</div>
                    </div>
                    
                    <div id="token-info"></div>
                </div>

                <!-- Profile Section -->
                <div id="section-profile" class="dashboard-section hidden">
                    <h3>User Profile</h3>
                    <div class="form-group">
                        <label>Username:</label>
                        <input type="text" id="profile-username" readonly>
                    </div>
                    <div class="form-group">
                        <label>Email:</label>
                        <input type="email" id="profile-email">
                    </div>
                    <div class="form-group">
                        <label>New Password:</label>
                        <input type="password" id="new-password" placeholder="Leave blank to keep current">
                    </div>
                    <button class="btn btn-success" onclick="updateProfile()">Update Profile</button>
                </div>

                <!-- Admin Panel Section -->
                <div id="section-admin" class="dashboard-section admin-panel hidden">
                    <h3>🛠️ Admin Panel</h3>
                    <p><strong>Administrative Functions</strong></p>
                    
                    <div class="admin-controls">
                        <div>
                            <h4>User Management</h4>
                            <button class="btn btn-primary" onclick="listUsers()">List All Users</button>
                            <button class="btn btn-success" onclick="createUser()">Create User</button>
                        </div>
                        
                        <div>
                            <h4>System Control</h4>
                            <button class="btn btn-secondary" onclick="viewSystemLogs()">View Logs</button>
                            <button class="btn btn-danger" onclick="clearSessions()">Clear All Sessions</button>
                        </div>
                        
                        <div>
                            <h4>Configuration</h4>
                            <button class="btn btn-primary" onclick="viewConfig()">View Config</button>
                            <button class="btn btn-secondary" onclick="reloadConfig()">Reload Config</button>
                        </div>
                    </div>
                    
                    <div id="admin-results" style="margin-top: 20px;"></div>
                </div>

                <!-- Health Section -->
                <div id="section-health" class="dashboard-section hidden">
                    <h3>System Health</h3>
                    <button class="btn btn-primary" onclick="checkHealth()">Check System Health</button>
                    <div id="health-results" style="margin-top: 20px;"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentToken = null;
        let currentUser = null;

        // Show/Hide Views
        function showView(viewName) {
            console.log('Switching to view:', viewName);
            
            const loginForm = document.getElementById('login-form');
            const dashboard = document.getElementById('dashboard');
            
            if (viewName === 'dashboard') {
                loginForm.style.display = 'none';
                dashboard.style.display = 'block';
                dashboard.classList.remove('hidden');
                loadUserInfo();
                console.log('Dashboard should now be visible');
            } else {
                loginForm.style.display = 'block';
                dashboard.style.display = 'none';
                dashboard.classList.add('hidden');
                console.log('Login form should now be visible');
            }
        }

        // Dashboard Navigation
        function showDashboardSection(section) {
            console.log('Switching to section:', section);
            
            // Hide all sections
            document.querySelectorAll('[id^="section-"]').forEach(el => {
                el.classList.add('hidden');
                el.style.display = 'none';
            });
            
            // Remove active class from all nav buttons
            document.querySelectorAll('.nav-btn').forEach(btn => btn.classList.remove('active'));
            
            // Show selected section
            const targetSection = document.getElementById('section-' + section);
            if (targetSection) {
                targetSection.classList.remove('hidden');
                targetSection.style.display = 'block';
                console.log('Section', section, 'is now visible');
            }
            
            // Add active class to clicked button
            event.target.classList.add('active');
        }

        // Authentication Functions
        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            console.log('Attempting login with:', username);
            
            if (!username || !password) {
                showMessage('Please enter both username and password', 'error');
                return;
            }

            try {
                const response = await fetch('/oauth/token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        grant_type: 'password'
                    })
                });

                const data = await response.json();
                console.log('Login response:', data);

                if (response.ok && data.access_token) {
                    currentToken = data.access_token;
                    currentUser = username;
                    document.getElementById('current-token').textContent = currentToken.substring(0, 50) + '...';
                    document.getElementById('user-display').textContent = username;
                    showMessage('Login successful! Redirecting to dashboard...', 'success');
                    
                    // Force dashboard to show after a short delay
                    setTimeout(() => {
                        console.log('Forcing dashboard view...');
                        showView('dashboard');
                    }, 1500);
                } else {
                    showMessage(data.error || 'Login failed', 'error');
                }
            } catch (error) {
                console.error('Login error:', error);
                showMessage('Connection error: ' + error.message, 'error');
            }
        }

        function logout() {
            currentToken = null;
            currentUser = null;
            document.getElementById('username').value = '';
            document.getElementById('password').value = '';
            document.getElementById('current-token').textContent = 'No token available';
            showView('login');
            showMessage('Logged out successfully', 'success');
        }

        // Token Management Functions
        async function refreshToken() {
            showTokenMessage('Token refresh functionality coming soon!', 'success');
        }

        async function validateCurrentToken() {
            if (!currentToken) {
                showTokenMessage('No token to validate', 'error');
                return;
            }

            try {
                const response = await fetch('/oauth/validate', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer ' + currentToken,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ token: currentToken })
                });

                const data = await response.json();
                if (response.ok) {
                    showTokenMessage('Token is valid ✅', 'success');
                    document.getElementById('token-info').innerHTML = `
                        <div class="alert alert-success">
                            <strong>Token Details:</strong><br>
                            Valid: ${data.valid ? 'Yes' : 'No'}<br>
                            User: ${data.username || currentUser}<br>
                            Expires: ${data.expires_at || 'Unknown'}
                        </div>
                    `;
                } else {
                    showTokenMessage('Token validation failed ❌', 'error');
                }
            } catch (error) {
                showTokenMessage('Error validating token: ' + error.message, 'error');
            }
        }

        function revokeToken() {
            if (confirm('Are you sure you want to revoke the current token? You will be logged out.')) {
                currentToken = null;
                document.getElementById('current-token').textContent = 'Token revoked';
                showTokenMessage('Token revoked. Please log in again.', 'error');
                setTimeout(logout, 2000);
            }
        }

        // Health Check
        async function checkHealth() {
            try {
                const response = await fetch('/health');
                const data = await response.json();
                
                document.getElementById('health-results').innerHTML = `
                    <div class="alert alert-success">
                        <strong>System Status:</strong> ${data.status || 'OK'}<br>
                        <strong>Version:</strong> ${data.version || 'Unknown'}<br>
                        <strong>Uptime:</strong> ${data.uptime || 'Unknown'}<br>
                        <strong>Database:</strong> ${data.database || 'Connected'}<br>
                        <strong>Timestamp:</strong> ${new Date().toLocaleString()}
                    </div>
                `;
            } catch (error) {
                document.getElementById('health-results').innerHTML = `
                    <div class="alert alert-error">
                        <strong>Health Check Failed:</strong> ${error.message}
                    </div>
                `;
            }
        }

        // Admin Functions
        function listUsers() {
            document.getElementById('admin-results').innerHTML = `
                <div class="alert alert-success">
                    <strong>User List:</strong><br>
                    • testuser (Active)<br>
                    • admin (Active)<br>
                    • demo (Inactive)<br>
                    <em>Note: Full user management requires backend implementation</em>
                </div>
            `;
        }

        function createUser() {
            const username = prompt('Enter username:');
            const email = prompt('Enter email:');
            if (username && email) {
                document.getElementById('admin-results').innerHTML = `
                    <div class="alert alert-success">
                        <strong>User Created:</strong><br>
                        Username: ${username}<br>
                        Email: ${email}<br>
                        <em>Note: This is a demo. Backend implementation required.</em>
                    </div>
                `;
            }
        }

        function viewSystemLogs() {
            document.getElementById('admin-results').innerHTML = `
                <div class="alert alert-success">
                    <strong>Recent System Logs:</strong><br>
                    <div style="font-family: monospace; background: #f1f3f4; padding: 10px; margin-top: 10px;">
                    [${new Date().toISOString()}] INFO: User login successful<br>
                    [${new Date().toISOString()}] INFO: Token validation request<br>
                    [${new Date().toISOString()}] INFO: Health check performed<br>
                    [${new Date().toISOString()}] INFO: System running normally
                    </div>
                </div>
            `;
        }

        function clearSessions() {
            if (confirm('Clear all active sessions? This will log out all users.')) {
                document.getElementById('admin-results').innerHTML = `
                    <div class="alert alert-success">
                        <strong>Sessions Cleared:</strong> All active sessions have been terminated.
                    </div>
                `;
            }
        }

        function viewConfig() {
            document.getElementById('admin-results').innerHTML = `
                <div class="alert alert-success">
                    <strong>System Configuration:</strong><br>
                    <div style="font-family: monospace; background: #f1f3f4; padding: 10px; margin-top: 10px;">
                    Database: PostgreSQL<br>
                    JWT Algorithm: RS256<br>
                    Token Expiry: 3600s<br>
                    Rate Limiting: Enabled<br>
                    SSL: Enabled
                    </div>
                </div>
            `;
        }

        function reloadConfig() {
            document.getElementById('admin-results').innerHTML = `
                <div class="alert alert-success">
                    <strong>Configuration Reloaded:</strong> System configuration has been refreshed.
                </div>
            `;
        }

        // Profile Management
        function updateProfile() {
            const email = document.getElementById('profile-email').value;
            const newPassword = document.getElementById('new-password').value;
            
            if (email) {
                showMessage('Profile updated successfully!', 'success');
                document.getElementById('user-email').textContent = email;
            }
        }

        function loadUserInfo() {
            if (currentUser) {
                document.getElementById('user-id').textContent = '12345';
                document.getElementById('user-email').textContent = currentUser + '@example.com';
                document.getElementById('profile-username').value = currentUser;
                document.getElementById('profile-email').value = currentUser + '@example.com';
            }
        }

        // Utility Functions
        function showMessage(message, type) {
            const messageDiv = document.getElementById('login-message');
            messageDiv.innerHTML = `<div class="alert alert-${type === 'error' ? 'error' : 'success'}">${message}</div>`;
            setTimeout(() => messageDiv.innerHTML = '', 5000);
        }

        function showTokenMessage(message, type) {
            const tokenInfo = document.getElementById('token-info');
            tokenInfo.innerHTML = `<div class="alert alert-${type === 'error' ? 'error' : 'success'}">${message}</div>`;
            setTimeout(() => tokenInfo.innerHTML = '', 5000);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded, initializing...');
            
            // Allow Enter key to submit login
            document.getElementById('password').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    login();
                }
            });
            
            // Check connection status
            checkHealth().then(() => {
                document.getElementById('connection-status').textContent = 'Connected';
                document.querySelector('.status-indicator').className = 'status-indicator status-online';
            }).catch(() => {
                document.getElementById('connection-status').textContent = 'Disconnected';
                document.querySelector('.status-indicator').className = 'status-indicator status-offline';
            });
        });
    </script>
</body>
</html>
