# Auth-Service Deployment Guide

**Date**: January 11, 2025  
**Status**: ✅ COMPLETE - Production Ready  
**Target**: Three-Server Architecture

## Overview

This guide provides step-by-step instructions for deploying the auth-service across the three-server architecture with automated certificate management, Valkey caching, and comprehensive monitoring.

## Prerequisites

### **Local Development Machine**
- **Windows 10/11** with PowerShell 5.1+
- **SSH Client** with key-based authentication
- **Git** for version control
- **Network Access** to target servers

### **Target Servers**
- **auth-dev.chcit.org** (***********) - Development
- **authbe.chcit.org** (***********) - Backend Production
- **authfe.chcit.org** (***********) - Frontend Production
- **Ubuntu 24.04 LTS** on all servers
- **SSH Access** with sudo privileges

### **SSH Key Configuration**
```bash
# SSH key location
C:\Users\<USER>\.ssh\id_rsa

# SSH config for easy access
Host auth-dev
    HostName auth-dev.chcit.org
    User btaylor-admin
    IdentityFile C:\Users\<USER>\.ssh\id_rsa

Host authbe
    HostName authbe.chcit.org
    User btaylor-admin
    IdentityFile C:\Users\<USER>\.ssh\id_rsa

Host authfe
    HostName authfe.chcit.org
    User btaylor-admin
    IdentityFile C:\Users\<USER>\.ssh\id_rsa
```

## Deployment Architecture

### **Server Roles**
```
auth-dev.chcit.org (Development)
├── Auth-Service (C++23)
├── Auth-UI (React)
├── PostgreSQL 17.5
├── Valkey 7.2.8
└── Nginx (SSL termination)

authbe.chcit.org (Backend Production)
├── Auth-Service (C++23)
├── PostgreSQL 17.5
├── Valkey 7.2.8
└── Nginx (SSL termination)

authfe.chcit.org (Frontend Production)
├── Auth-UI (React)
├── Nginx (SSL termination)
└── Redis-tools (connects to backend cache)
```

### **Network Configuration**
- **Development**: All services on single server
- **Production**: Separated frontend/backend with secure communication
- **SSL**: Wildcard *.chcit.org certificates on all servers
- **Cache**: Co-located with backend, frontend connects remotely

## Step-by-Step Deployment

### **Step 1: Launch Deployment System**
```powershell
# Navigate to deployment scripts
cd "D:\Coding_Projects\auth-service\auth-service-deployment\deployment_scripts"

# Launch main deployment menu
.\Deploy-AuthService.ps1
```

### **Step 2: Development Environment Setup**
```
Main Menu > [1] Development Server Management

Development Server Management Options:
[1] Test Development Server Readiness
[2] Manage Development Dependencies
[3] Build & Deploy Auth Service
[4] Build & Deploy Auth UI
[5] Initialize Development Database
[6] Start/Stop Services
[7] View Service Status
[8] View Service Logs
[9] Development Environment Configuration
```

#### **2.1 Test Server Readiness**
```
Select: [1] Test Development Server Readiness

Expected Results:
✅ SSH connection successful
✅ GCC 14+: gcc (Ubuntu 14.2.0-4ubuntu2~24.04) 14.2.0
✅ CMake: cmake version 3.28.3
✅ PostgreSQL 17 server is running
✅ Redis/Valkey client tools: redis-cli 7.2.8
✅ Certificate files found (4 files) for domain: chcit.org
✅ Certificate sync cron job is scheduled
```

#### **2.2 Install Dependencies**
```
Select: [2] Manage Development Dependencies
Select: [1] Install All Development Dependencies

This will install:
- GCC 14.2.0 (C++23 compiler)
- CMake 3.28+ (build system)
- PostgreSQL 17+ (database)
- Valkey 7.2+ (cache server)
- All C++ libraries (Boost, OpenSSL, JWT, etc.)
- Node.js 20+ (for React UI)
```

#### **2.3 Setup Certificate Sync**
```
Main Menu > [26] SSL-Sync User Management
Select: [1] Install Certificate Sync Cron Job

This will:
- Create ssl-sync user with proper permissions
- Install SSH keys for certificate sync
- Setup automated cron job (every 6 hours)
- Test certificate synchronization
```

### **Step 3: Backend Production Setup**
```
Main Menu > [2] Backend Production Management

Backend Production Management Options:
[1] Test Backend Server Readiness
[2] Manage Backend Dependencies
[3] Build & Deploy Auth Service
[4] Initialize Production Database
[5] Start/Stop Services
[6] View Service Status
[7] View Service Logs
[8] Backend Environment Configuration
```

#### **3.1 Test Server Readiness**
```
Select: [1] Test Backend Server Readiness

Expected Results:
✅ All dependencies installed
✅ PostgreSQL 17 configured
✅ Valkey server running
✅ SSL certificates deployed
✅ Minimal production packages only
```

#### **3.2 Install Production Dependencies**
```
Select: [2] Manage Backend Dependencies
Select: [1] Install All Backend Dependencies

Production-optimized installation:
- Minimal package set (no development tools)
- PostgreSQL 17 server
- Valkey server (co-located)
- Production C++ libraries only
- No Git, GCC, or build tools
```

### **Step 4: Frontend Production Setup**
```
Main Menu > [3] Frontend Production Management

Frontend Production Management Options:
[1] Test Frontend Server Readiness
[2] Manage Frontend Dependencies
[3] Build & Deploy Auth UI
[4] Start/Stop Services
[5] View Service Status
[6] View Service Logs
[7] Frontend Environment Configuration
```

#### **4.1 Install Frontend Dependencies**
```
Select: [2] Manage Frontend Dependencies
Select: [1] Install All Frontend Dependencies

Frontend-optimized installation:
- Node.js 20+ (for React UI)
- Nginx (reverse proxy)
- Redis-tools (cache client only)
- SSL certificates
- No database server (connects to backend)
```

## Certificate Management

### **Automated Certificate Sync**
The system automatically syncs wildcard *.chcit.org certificates:

```bash
# Cron job runs every 6 hours
0 */6 * * * /opt/auth-service/scripts/sync-auth-certificates.sh development

# Manual sync test
sudo -u ssl-sync /opt/auth-service/scripts/sync-auth-certificates.sh development
```

### **Certificate Verification**
```bash
# Check certificate expiration
openssl x509 -in /etc/letsencrypt/live/chcit.org/cert.pem -noout -dates

# Verify certificate chain
openssl verify -CAfile /etc/letsencrypt/live/chcit.org/chain.pem /etc/letsencrypt/live/chcit.org/cert.pem

# Test HTTPS endpoint
curl -I https://auth-dev.chcit.org:8082
```

## Service Management

### **Development Environment**
```bash
# Start all services
sudo systemctl start postgresql valkey-server nginx auth-service

# Check service status
sudo systemctl status auth-service
sudo systemctl status valkey-server
sudo systemctl status postgresql

# View logs
sudo journalctl -u auth-service -f
sudo journalctl -u valkey-server -f
```

### **Production Environment**
```bash
# Backend services
sudo systemctl start postgresql valkey-server nginx auth-service

# Frontend services  
sudo systemctl start nginx auth-ui

# Monitor all services
sudo systemctl status auth-service valkey-server postgresql nginx
```

## Database Initialization

### **Development Database**
```
Development Menu > [5] Initialize Development Database

This will:
- Create auth_service database
- Setup database schema
- Create initial admin user
- Configure database permissions
- Test database connectivity
```

### **Production Database**
```
Backend Menu > [4] Initialize Production Database

This will:
- Create production database with security hardening
- Setup replication (if configured)
- Create production admin user
- Configure backup procedures
- Test production connectivity
```

## Monitoring & Health Checks

### **Server Readiness Validation**
Each environment provides comprehensive health checking:

```
✅ SSH connectivity
✅ System resources (CPU, memory, disk)
✅ Compiler and build tools
✅ Database server status
✅ Cache server status
✅ SSL certificate validity
✅ Service dependencies
✅ Network connectivity
✅ File permissions
✅ Service configuration
✅ Log file access
```

### **Service Monitoring**
```bash
# Check all auth-service components
sudo systemctl status auth-service valkey-server postgresql nginx

# Monitor resource usage
htop
df -h
free -h

# Check network connectivity
ss -tlnp | grep :8082
ss -tlnp | grep :6379
ss -tlnp | grep :5432
```

### **Log Monitoring**
```bash
# Auth-service logs
tail -f /opt/auth-service/logs/auth-service.log

# System logs
sudo journalctl -u auth-service -f
sudo journalctl -u valkey-server -f
sudo journalctl -u postgresql -f

# Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

## Troubleshooting

### **Common Issues**

#### **Certificate Problems**
```bash
# Test certificate sync
sudo -u ssl-sync /opt/auth-service/scripts/sync-auth-certificates.sh development

# Check SSL-sync permissions
sudo -u ssl-sync sudo -l

# Verify certificate files
ls -la /etc/letsencrypt/live/chcit.org/
```

#### **Database Connection Issues**
```bash
# Test PostgreSQL connection
sudo -u postgres psql -c "SELECT version();"

# Check database service
sudo systemctl status postgresql

# Test auth-service database
psql -h localhost -U auth_user -d auth_service -c "SELECT 1;"
```

#### **Cache Connection Issues**
```bash
# Test Valkey connection
redis-cli ping

# Check Valkey service
sudo systemctl status valkey-server

# Monitor Valkey
redis-cli monitor
```

#### **Service Startup Issues**
```bash
# Check service logs
sudo journalctl -u auth-service -n 50

# Verify configuration
sudo nginx -t
sudo -u auth-service /opt/auth-service/bin/auth-service --test-config

# Check file permissions
ls -la /opt/auth-service/
ls -la /opt/auth-service/bin/
```

### **Recovery Procedures**

#### **Service Recovery**
```bash
# Restart all services
sudo systemctl restart auth-service valkey-server postgresql nginx

# Reset service configuration
sudo systemctl daemon-reload
sudo systemctl reset-failed auth-service
```

#### **Certificate Recovery**
```bash
# Manual certificate deployment
sudo -u ssl-sync /opt/auth-service/scripts/sync-auth-certificates.sh development

# Emergency certificate restore
sudo cp /home/<USER>/letsencrypt_backup/live/chcit.org/* /etc/letsencrypt/live/chcit.org/
sudo systemctl reload nginx
```

#### **Database Recovery**
```bash
# Restart PostgreSQL
sudo systemctl restart postgresql

# Restore from backup
sudo -u postgres pg_restore -d auth_service /path/to/backup.sql

# Reset database permissions
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE auth_service TO auth_user;"
```

## Security Considerations

### **Network Security**
- **Firewall**: UFW configured for minimal exposure
- **SSL Only**: All external communication encrypted
- **Internal Communication**: Services communicate via localhost
- **Port Isolation**: Services bound to localhost only

### **User Security**
- **Service Users**: Dedicated users for each service
- **SSH Keys**: Key-based authentication only
- **Sudo Restrictions**: Minimal sudo permissions
- **File Permissions**: Proper ownership and modes

### **Data Security**
- **Database Encryption**: SSL connections required
- **Cache Security**: Localhost-only access
- **Certificate Security**: Proper file permissions
- **Log Security**: Restricted log file access

## Performance Optimization

### **Resource Allocation**
- **Development**: 2 cores, 4GB RAM minimum
- **Production Backend**: 4 cores, 8GB RAM recommended
- **Production Frontend**: 2 cores, 4GB RAM recommended
- **Database**: SSD storage for optimal performance

### **Cache Optimization**
- **Development**: 128MB Valkey memory
- **Production**: 256MB Valkey memory
- **Eviction Policy**: allkeys-lru
- **Connection Pooling**: Optimized for application load

### **Database Optimization**
- **Connection Pooling**: Configured for concurrent users
- **Query Optimization**: Indexed for common queries
- **Backup Strategy**: Regular automated backups
- **Monitoring**: Performance metrics collection

## Conclusion

The auth-service deployment system provides:
- ✅ **Automated Deployment** across three environments
- ✅ **Comprehensive Testing** and validation
- ✅ **Security Hardening** with SSL and proper permissions
- ✅ **High Availability** with monitoring and recovery
- ✅ **Production Ready** infrastructure

Follow this guide for successful deployment of the auth-service infrastructure. The system is designed for enterprise-level reliability and security.
