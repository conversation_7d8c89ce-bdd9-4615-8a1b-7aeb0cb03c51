# Auth-Service Valkey Cache Integration

**Date**: January 11, 2025  
**Status**: ✅ COMPLETE - Production Ready  
**Cache System**: Valkey 7.2.8 (Redis-compatible)

## Overview

The auth-service implements Valkey as the primary caching solution, providing high-performance Redis-compatible caching with co-located architecture for optimal performance. Valkey is used for session management, JWT token validation, and database query caching.

## Architecture

### **Co-Located Cache Strategy**
- **Development**: Valkey server on auth-dev.chcit.org
- **Backend Production**: Valkey server on authbe.chcit.org
- **Frontend Production**: Connects to backend's Valkey server
- **Benefits**: Reduced latency, simplified networking, resource efficiency

### **Valkey vs Redis**
- **Valkey**: Open-source Redis fork maintained by Linux Foundation
- **Compatibility**: 100% Redis protocol compatible
- **Performance**: Enhanced performance optimizations
- **Licensing**: BSD 3-Clause (more permissive than Redis)
- **Community**: Strong community support and active development

## Technical Specifications

### **Version Information**
```bash
Valkey: 7.2.8 (Ubuntu 24.04 package)
Protocol: Redis 7.2 compatible
Client Tools: redis-cli (Redis-compatible tools)
```

### **Configuration**
```ini
# /etc/valkey/valkey.conf
bind 127.0.0.1
port 6379
maxmemory 256mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### **Memory Configuration**
- **Development**: 128MB (sufficient for development workload)
- **Production Backend**: 256MB (handles production session load)
- **Production Frontend**: Uses backend cache (no local cache)
- **Eviction Policy**: allkeys-lru (least recently used)

## Deployment Integration

### **Requirements Files**
Each environment defines Valkey requirements in JSON format:

#### **Development Environment**
```json
{
  "cache_requirements": {
    "valkey": {
      "version_minimum": "7.0",
      "server_package": "valkey-server",
      "client_package": "redis-tools",
      "install_command": "sudo apt-get install -y valkey-server redis-tools",
      "service_name": "valkey-server",
      "configuration": {
        "bind": "127.0.0.1",
        "port": "6379",
        "maxmemory": "128mb",
        "maxmemory_policy": "allkeys-lru"
      }
    }
  }
}
```

#### **Backend Production Environment**
```json
{
  "cache_requirements": {
    "valkey": {
      "version_minimum": "7.0",
      "server_package": "valkey-server",
      "client_package": "redis-tools",
      "install_command": "sudo apt-get install -y valkey-server redis-tools",
      "service_name": "valkey-server",
      "configuration": {
        "bind": "127.0.0.1",
        "port": "6379",
        "maxmemory": "256mb",
        "maxmemory_policy": "allkeys-lru"
      }
    }
  }
}
```

#### **Frontend Production Environment**
```json
{
  "cache_requirements": {
    "valkey": {
      "version_minimum": "7.0",
      "client_package": "redis-tools",
      "install_command": "sudo apt-get install -y redis-tools",
      "connection_host": "authbe.chcit.org",
      "connection_port": "6379"
    }
  }
}
```

## Installation & Configuration

### **Automated Installation**
The deployment system automatically installs Valkey based on environment requirements:

```bash
# Install Valkey server and client tools
sudo apt-get update
sudo apt-get install -y valkey-server redis-tools

# Enable and start service
sudo systemctl enable valkey-server
sudo systemctl start valkey-server

# Verify installation
redis-cli ping
# Expected: PONG
```

### **Service Management**
```bash
# Service status
sudo systemctl status valkey-server

# Start/stop/restart
sudo systemctl start valkey-server
sudo systemctl stop valkey-server
sudo systemctl restart valkey-server

# View logs
sudo journalctl -u valkey-server -f
```

### **Configuration Management**
- **Config File**: `/etc/valkey/valkey.conf`
- **Data Directory**: `/var/lib/valkey/`
- **Log File**: `/var/log/valkey/valkey-server.log`
- **PID File**: `/var/run/valkey/valkey-server.pid`

## Server Readiness Testing

### **Cache Testing Integration**
The server readiness test includes comprehensive Valkey validation:

```powershell
# Test 7: Checking cache requirements (Valkey)
✅ Redis/Valkey client tools: redis-cli 7.2.8
✅ Valkey server connection: PONG
```

### **Connection Testing**
```bash
# Basic connection test
redis-cli ping

# Server info
redis-cli info server

# Memory usage
redis-cli info memory

# Connected clients
redis-cli info clients
```

### **Performance Testing**
```bash
# Benchmark performance
redis-cli --latency -h 127.0.0.1 -p 6379

# Memory usage monitoring
redis-cli info memory | grep used_memory_human

# Connection count
redis-cli info clients | grep connected_clients
```

## Auth-Service Integration

### **Use Cases**
1. **Session Management**: Store user session data
2. **JWT Token Validation**: Cache valid/invalid tokens
3. **Database Query Caching**: Cache frequent database queries
4. **Rate Limiting**: Track API request rates per user
5. **Temporary Data**: Store temporary authentication codes

### **C++ Integration**
```cpp
// Example Valkey integration in auth-service
#include <hiredis/hiredis.h>

class ValkeyCache {
private:
    redisContext* context;
    
public:
    bool connect() {
        context = redisConnect("127.0.0.1", 6379);
        return context && !context->err;
    }
    
    bool setSession(const std::string& sessionId, const std::string& data, int ttl = 3600) {
        redisReply* reply = (redisReply*)redisCommand(context, 
            "SETEX %s %d %s", sessionId.c_str(), ttl, data.c_str());
        bool success = reply && reply->type == REDIS_REPLY_STATUS;
        freeReplyObject(reply);
        return success;
    }
    
    std::string getSession(const std::string& sessionId) {
        redisReply* reply = (redisReply*)redisCommand(context, 
            "GET %s", sessionId.c_str());
        std::string result;
        if (reply && reply->type == REDIS_REPLY_STRING) {
            result = reply->str;
        }
        freeReplyObject(reply);
        return result;
    }
};
```

### **Session Storage Schema**
```
Key Pattern: auth:session:{session_id}
Value: JSON session data
TTL: 3600 seconds (1 hour)

Key Pattern: auth:jwt:{token_hash}
Value: "valid" or "invalid"
TTL: Token expiration time

Key Pattern: auth:rate_limit:{user_id}
Value: Request count
TTL: 60 seconds (1 minute window)
```

## Security Considerations

### **Network Security**
- **Bind Address**: 127.0.0.1 (localhost only)
- **No External Access**: Firewall blocks external connections
- **No Authentication**: Not needed for localhost-only access
- **SSL/TLS**: Not required for localhost communication

### **Data Security**
- **Sensitive Data**: Encrypt before storing in cache
- **Session Tokens**: Hash session IDs before using as keys
- **TTL Management**: Automatic expiration for security
- **Memory Protection**: Valkey memory is not swapped to disk

### **Access Control**
- **Service User**: Valkey runs as dedicated valkey user
- **File Permissions**: Restricted access to config and data files
- **Process Isolation**: Valkey process isolated from other services
- **Resource Limits**: Memory and CPU limits configured

## Monitoring & Maintenance

### **Performance Monitoring**
```bash
# Real-time monitoring
redis-cli --stat

# Memory usage
redis-cli info memory

# Hit/miss ratio
redis-cli info stats | grep keyspace

# Slow queries
redis-cli slowlog get 10
```

### **Health Checks**
```bash
# Service health
sudo systemctl is-active valkey-server

# Connection test
redis-cli ping

# Memory usage check
redis-cli info memory | grep used_memory_peak_human

# Client connections
redis-cli info clients | grep connected_clients
```

### **Maintenance Tasks**
- **Log Rotation**: Automatic log rotation configured
- **Memory Monitoring**: Alert on high memory usage
- **Performance Tuning**: Optimize based on usage patterns
- **Backup Strategy**: Periodic RDB snapshots

## Troubleshooting

### **Common Issues**
1. **Connection Refused**: Check if valkey-server is running
2. **Memory Errors**: Check maxmemory configuration
3. **Slow Performance**: Monitor memory usage and eviction
4. **High CPU Usage**: Check for inefficient operations

### **Diagnostic Commands**
```bash
# Check service status
sudo systemctl status valkey-server

# View logs
sudo journalctl -u valkey-server -n 50

# Test connection
redis-cli ping

# Check configuration
redis-cli config get "*"

# Monitor real-time
redis-cli monitor
```

### **Recovery Procedures**
1. **Service Restart**: `sudo systemctl restart valkey-server`
2. **Configuration Reset**: Restore default configuration
3. **Data Recovery**: Restore from RDB backup
4. **Performance Reset**: Clear cache and restart
5. **Emergency Fallback**: Disable cache temporarily

## Performance Optimization

### **Memory Optimization**
- **Eviction Policy**: allkeys-lru for automatic cleanup
- **Memory Monitoring**: Alert at 80% memory usage
- **Key Expiration**: Set appropriate TTL values
- **Data Compression**: Use efficient data serialization

### **Connection Optimization**
- **Connection Pooling**: Reuse connections in application
- **Pipeline Operations**: Batch multiple commands
- **Async Operations**: Use non-blocking operations
- **Connection Limits**: Configure appropriate limits

### **Performance Tuning**
```ini
# /etc/valkey/valkey.conf optimizations
tcp-keepalive 300
timeout 0
tcp-backlog 511
maxclients 10000
```

## Integration with Deployment System

### **Dependency Management**
- **Install-Dependencies Module**: Includes Valkey client tools
- **Install-CacheDependencies Function**: Dedicated Valkey server installation
- **Environment Detection**: Automatic server vs client installation
- **Service Verification**: Post-installation testing

### **Menu Integration**
```
Development Dependencies Management:
[1] Install All Development Dependencies (includes Valkey)
[2] Install Database Dependencies
[3] Check Dependency Status (includes Valkey status)
```

### **Automated Testing**
- **Server Readiness**: Valkey connection testing
- **Performance Validation**: Basic performance checks
- **Configuration Verification**: Config file validation
- **Service Health**: Systemd service status

## Future Enhancements

### **Planned Improvements**
- **Cluster Mode**: Multi-node Valkey cluster for high availability
- **Monitoring Integration**: Prometheus metrics collection
- **Advanced Security**: TLS encryption for inter-service communication
- **Backup Automation**: Automated RDB backup and restoration

### **Scalability Considerations**
- **Horizontal Scaling**: Multiple Valkey instances
- **Load Balancing**: Distribute cache load across instances
- **Data Partitioning**: Shard data across multiple servers
- **Geographic Distribution**: Regional cache deployment

## Conclusion

The Valkey cache integration provides:
- ✅ **High Performance** Redis-compatible caching
- ✅ **Co-Located Architecture** for optimal latency
- ✅ **Automated Deployment** with environment-specific configuration
- ✅ **Comprehensive Testing** and monitoring capabilities
- ✅ **Production Ready** with security and maintenance procedures

The system is ready for production deployment and provides enterprise-grade caching capabilities for the auth-service infrastructure.
