# Certificate Scripts Refactoring Summary

## Overview

The certificate scripts have been successfully refactored to support the three auth-service deployment targets:
- **auth-dev.chcit.org** (Development)
- **authbe.chcit.org** (Backend Production)
- **authfe.chcit.org** (Frontend Production)

## Changes Made

### 1. Enhanced setup-certificate-access.sh

#### Key Improvements
- **Updated for Auth-Service**: Changed from database-service to auth-service context
- **Enhanced Logging**: Added comprehensive logging with timestamps
- **User Management**: Creates auth-service user if it doesn't exist
- **Backup Directory Support**: Configures both standard and backup certificate locations
- **Comprehensive Permissions**: Sets up proper permissions for all certificate directories

#### Major Changes
```bash
# BEFORE
DB_SERVICE_USER="database-service"

# AFTER
AUTH_SERVICE_USER="auth-service"
BACKUP_DIR="/home/<USER>/letsencrypt_backup"
```

#### New Features
- Creates auth-service user automatically
- Sets up backup certificate directory structure
- Adds btaylor-admin to certificate group for management
- Creates auth-service directories (/opt/auth-service/logs, /opt/auth-service/scripts)
- Comprehensive permission setup for both standard and backup locations

### 2. Enhanced sync-auth-certificates.sh

#### Multi-Environment Support
```bash
# BEFORE
declare -A AUTH_SERVERS=(
    ["development"]="auth-dev.chcit.org"
    ["production"]="auth.chcit.org"
)

# AFTER
declare -A AUTH_SERVERS=(
    ["development"]="auth-dev.chcit.org"
    ["backend-production"]="authbe.chcit.org"
    ["frontend-production"]="authfe.chcit.org"
)
```

#### Service-Specific Configuration
```bash
# Service ports for each environment
declare -A AUTH_PORTS=(
    ["development"]="8082"
    ["backend-production"]="8082"
    ["frontend-production"]="3000"
)

# Service names for each environment
declare -A SERVICE_NAMES=(
    ["development"]="auth-service"
    ["backend-production"]="auth-service"
    ["frontend-production"]="auth-ui"
)
```

#### Enhanced HTTPS Testing
- **Development**: Tests https://auth-dev.chcit.org:8082
- **Backend Production**: Tests https://authbe.chcit.org:8082
- **Frontend Production**: Tests https://authfe.chcit.org:3000

#### Updated Usage Options
```bash
# New environment options
development          - auth-dev.chcit.org (auth-service:8082)
backend-production   - authbe.chcit.org (auth-service:8082)
frontend-production  - authfe.chcit.org (auth-ui:3000)
all                  - All environments
```

### 3. Removed Unnecessary Script

- **Deleted**: `sync-certificates-updated.sh` (original git server script)
- **Reason**: No longer needed as functionality is integrated into the enhanced sync script

## Architecture Overview

### Certificate Flow
1. **Source**: project-tracker.chcit.org (main certificate server)
2. **Sync Script**: Runs from project-tracker.chcit.org
3. **Targets**: 
   - auth-dev.chcit.org (development)
   - authbe.chcit.org (backend production)
   - authfe.chcit.org (frontend production)

### Certificate Locations
- **Primary Location**: `/home/<USER>/letsencrypt_backup/live/chcit.org/`
- **Standard Location**: `/etc/letsencrypt/live/chcit.org/`
- **Archive Location**: `/etc/letsencrypt/archive/chcit.org/`

### Service Integration
- **Development**: Reloads `auth-service` systemd service
- **Backend Production**: Reloads `auth-service` systemd service
- **Frontend Production**: Reloads `auth-ui` systemd service

## Usage Examples

### Setup Certificate Access (One-time)
```bash
# Run on each target server
sudo /opt/auth-service/scripts/setup-certificate-access.sh
```

### Sync Certificates
```bash
# From project-tracker.chcit.org

# Sync to all environments
/opt/auth-service/scripts/sync-auth-certificates.sh

# Sync to specific environment
/opt/auth-service/scripts/sync-auth-certificates.sh development
/opt/auth-service/scripts/sync-auth-certificates.sh backend-production
/opt/auth-service/scripts/sync-auth-certificates.sh frontend-production
```

## Deployment Integration

### PowerShell Menu Integration
The scripts are ready for integration into Menu Option 5 of the deployment system:

1. **Setup Certificate Access** → Runs `setup-certificate-access.sh`
2. **Sync Certificates** → Runs `sync-auth-certificates.sh` with environment selection
3. **Test Installation** → Verifies certificate installation
4. **Schedule Sync** → Sets up cron jobs for automatic updates

### Cron Job Scheduling
Recommended cron schedule for automatic certificate updates:
```bash
# Every 6 hours (recommended)
0 */6 * * * /opt/auth-service/scripts/sync-auth-certificates.sh >> /opt/auth-service/logs/cert-sync.log 2>&1
```

## Security Considerations

### User Permissions
- **auth-service user**: Member of ssl-cert group for certificate access
- **btaylor-admin user**: Member of ssl-cert group for management
- **Certificate files**: Proper permissions (644 for certs, 600 for private keys)

### SSH Key Management
- Uses existing ed25519 SSH key: `/home/<USER>/.ssh/id_ed25519`
- SSH connections from project-tracker.chcit.org to target servers
- Batch mode SSH for automated execution

## Testing and Validation

### Pre-Deployment Testing
1. **SSH Connectivity**: Verify SSH access to all target servers
2. **Certificate Source**: Confirm certificates exist on project-tracker.chcit.org
3. **User Permissions**: Ensure auth-service user exists on target servers
4. **Directory Structure**: Verify certificate directories are created

### Post-Deployment Validation
1. **Certificate Files**: Confirm certificates are copied to target servers
2. **Service Reload**: Verify services reload successfully
3. **HTTPS Endpoints**: Test HTTPS connectivity to all environments
4. **Log Files**: Check sync logs for any errors

## Benefits of Refactored Scripts

### Operational Benefits
1. **Multi-Environment Support**: Single script handles all three environments
2. **Service-Aware**: Knows which service to reload for each environment
3. **Port-Aware**: Tests correct ports for each service type
4. **Comprehensive Logging**: Detailed logs for troubleshooting

### Maintenance Benefits
1. **Reduced Complexity**: Two scripts instead of three
2. **Consistent Configuration**: Centralized environment definitions
3. **Error Handling**: Robust error checking and reporting
4. **Automated Testing**: Built-in HTTPS endpoint verification

### Security Benefits
1. **Proper Permissions**: Comprehensive permission management
2. **User Isolation**: Service-specific user accounts
3. **Group-Based Access**: ssl-cert group for certificate access
4. **Secure Transfers**: SSH-based certificate synchronization

## Next Steps

1. **Deploy Scripts**: Copy refactored scripts to target servers
2. **Test Setup**: Run setup-certificate-access.sh on each server
3. **Test Sync**: Run sync-auth-certificates.sh from project-tracker.chcit.org
4. **Integrate Menu**: Enhance PowerShell Menu Option 5
5. **Schedule Automation**: Set up cron jobs for automatic updates

The refactored scripts provide a robust, scalable solution for managing SSL certificates across all auth-service environments while maintaining security and operational efficiency.
