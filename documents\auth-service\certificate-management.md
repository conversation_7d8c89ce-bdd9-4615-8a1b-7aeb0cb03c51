# Certificate Management for Auth Service

This document outlines the SSL/TLS certificate management for the auth-service deployment.

## Overview

The auth-service uses wildcard SSL certificates (`*.chcit.org`) for secure HTTPS communication. Certificates are managed through Let's Encrypt and synchronized across servers.

## Certificate Locations

### Primary Certificate Storage
- **Live Certificates**: `/etc/letsencrypt/live/chcit.org/`
  - `fullchain.pem` - Full certificate chain
  - `privkey.pem` - Private key
  - `cert.pem` - Certificate only
  - `chain.pem` - Certificate chain only

### Backup Storage
- **Backup Location**: `/home/<USER>/letsencrypt_backup/`
- **Purpose**: Secondary copy for certificate synchronization

## Certificate Synchronization

### SSL-Sync User
- **Username**: `ssl-sync`
- **Purpose**: Dedicated user for certificate synchronization
- **SSH Key**: Used for secure certificate transfer between servers

### Sync Process
1. Certificates are generated on `project-tracker.chcit.org`
2. Target servers pull certificates using `ssl-sync` user
3. Certificates are copied to both backup and live locations
4. <PERSON>in<PERSON> is reloaded to use new certificates

### Sync Script Location
- **Menu Option**: Main menu option 26, sub-option 11
- **Installation**: Via deployment menu system
- **Frequency**: Automated via cron job

## Certificate Validation

### Validation Paths
The system checks certificates in multiple locations:
- `/home/<USER>/letsencrypt_backup/live/chcit.org/`
- `/etc/letsencrypt/live/chcit.org/`

### Validation Commands
```bash
# Check certificate expiry
openssl x509 -in /etc/letsencrypt/live/chcit.org/cert.pem -noout -dates

# Verify certificate chain
openssl verify -CAfile /etc/letsencrypt/live/chcit.org/chain.pem /etc/letsencrypt/live/chcit.org/cert.pem

# Test SSL connection
openssl s_client -connect auth-dev.chcit.org:443 -servername auth-dev.chcit.org
```

## Nginx Configuration

### SSL Settings
```nginx
ssl_certificate /etc/letsencrypt/live/chcit.org/fullchain.pem;
ssl_certificate_key /etc/letsencrypt/live/chcit.org/privkey.pem;
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
```

### Security Headers
```nginx
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
add_header X-Frame-Options DENY always;
add_header X-Content-Type-Options nosniff always;
add_header X-XSS-Protection "1; mode=block" always;
```

## Troubleshooting

### Common Issues

1. **Certificate Not Found**
   - Check if certificates exist in `/etc/letsencrypt/live/chcit.org/`
   - Verify ssl-sync user has proper permissions
   - Run certificate sync manually

2. **Permission Denied**
   - Ensure nginx user can read certificate files
   - Check file ownership: `chown -R root:ssl-cert /etc/letsencrypt/`
   - Verify permissions: `chmod 640 /etc/letsencrypt/live/chcit.org/privkey.pem`

3. **Certificate Expired**
   - Check expiry date: `openssl x509 -in cert.pem -noout -dates`
   - Renew certificates on primary server
   - Sync to target servers

### Manual Certificate Sync
```bash
# On target server, as ssl-sync user
ssh <EMAIL> "sudo tar -czf - -C /etc letsencrypt" | sudo tar -xzf - -C /home/<USER>/letsencrypt_backup/
sudo cp -r /home/<USER>/letsencrypt_backup/letsencrypt/* /etc/letsencrypt/
sudo systemctl reload nginx
```

## Security Considerations

### Access Control
- Certificate private keys are readable only by root and ssl-cert group
- SSL-sync user has limited sudo permissions for certificate operations
- SSH keys are used for secure transfer between servers

### Monitoring
- Certificate expiry should be monitored (Let's Encrypt certificates expire every 90 days)
- Automated renewal should be configured on the primary server
- Sync failures should trigger alerts

## Integration with Auth Service

### HTTPS Enforcement
- All HTTP requests are redirected to HTTPS
- OAuth 2.0 endpoints require HTTPS for security
- Admin interface is HTTPS-only

### Certificate Validation in Application
The auth-service application relies on nginx for SSL termination and does not handle certificates directly. All certificate management is handled at the nginx level.

## Maintenance Schedule

### Regular Tasks
- **Weekly**: Verify certificate sync is working
- **Monthly**: Check certificate expiry dates
- **Quarterly**: Review certificate security settings
- **Annually**: Review and update SSL/TLS configuration

### Emergency Procedures
1. If certificates expire unexpectedly:
   - Generate temporary self-signed certificates
   - Renew Let's Encrypt certificates immediately
   - Sync to all servers
   - Verify all services are working

2. If private key is compromised:
   - Revoke existing certificates immediately
   - Generate new certificates with new private keys
   - Update all servers
   - Monitor for unauthorized usage
