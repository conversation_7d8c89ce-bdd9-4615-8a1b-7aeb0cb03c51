<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth Service React UI Preview</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .preview-header {
            background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }

        .preview-section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 500;
            margin-bottom: 20px;
            color: #1976d2;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .material-icons {
            font-size: 24px;
        }

        /* Login Form Styles */
        .login-form {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            padding: 32px;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.12);
        }

        .login-header {
            text-align: center;
            margin-bottom: 32px;
        }

        .login-icon {
            font-size: 48px;
            color: #1976d2;
            margin-bottom: 16px;
        }

        .form-field {
            margin-bottom: 20px;
        }

        .form-field label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }

        .form-field input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.2s;
        }

        .form-field input:focus {
            outline: none;
            border-color: #1976d2;
        }

        .login-button {
            width: 100%;
            padding: 12px;
            background: #1976d2;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .login-button:hover {
            background: #1565c0;
        }

        .test-credentials {
            background: #f5f5f5;
            padding: 16px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .test-credentials h4 {
            margin-bottom: 8px;
            color: #666;
        }

        /* Dashboard Styles */
        .app-bar {
            background: #1976d2;
            color: white;
            padding: 16px 24px;
            border-radius: 8px;
            margin-bottom: 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .app-bar-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 1.25rem;
            font-weight: 500;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
        }

        .dashboard-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 20px;
        }

        .card-header h3 {
            font-size: 1.25rem;
            font-weight: 500;
        }

        .status-chip {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .status-success {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .status-info {
            background: #e3f2fd;
            color: #1976d2;
        }

        .info-list {
            list-style: none;
            margin: 16px 0;
        }

        .info-list li {
            padding: 4px 0;
            color: #666;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            margin-top: 20px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: #1976d2;
            color: white;
        }

        .btn-primary:hover {
            background: #1565c0;
        }

        .btn-outline {
            background: transparent;
            color: #1976d2;
            border: 2px solid #1976d2;
        }

        .btn-outline:hover {
            background: #1976d2;
            color: white;
        }

        .btn-error {
            background: transparent;
            color: #d32f2f;
            border: 2px solid #d32f2f;
        }

        .btn-error:hover {
            background: #d32f2f;
            color: white;
        }

        .token-preview {
            background: #f5f5f5;
            padding: 12px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 0.875rem;
            word-break: break-all;
            margin: 8px 0;
        }

        .feature-highlight {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
        }

        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            background: rgba(255,255,255,0.7);
            border-radius: 8px;
        }

        .feature-item .material-icons {
            color: #4caf50;
            font-size: 20px;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .btn {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="preview-header">
            <h1>🚀 Auth Service React/TypeScript UI Preview</h1>
            <p>Modern Material-UI based authentication interface</p>
        </div>

        <!-- Login Form Preview -->
        <div class="preview-section">
            <h2 class="section-title">
                <span class="material-icons">login</span>
                Login Form Component
            </h2>
            <div class="login-form">
                <div class="login-header">
                    <div class="material-icons login-icon">security</div>
                    <h2>Auth Service</h2>
                    <p style="color: #666; margin-top: 8px;">OAuth 2.0 Authentication</p>
                </div>
                
                <form>
                    <div class="form-field">
                        <label for="username">Username</label>
                        <input type="text" id="username" placeholder="Enter your username" value="testuser">
                    </div>
                    
                    <div class="form-field">
                        <label for="password">Password</label>
                        <input type="password" id="password" placeholder="Enter your password" value="password">
                    </div>
                    
                    <button type="button" class="login-button">
                        <span class="material-icons">login</span>
                        Sign In
                    </button>
                </form>
                
                <div class="test-credentials">
                    <h4>Test Credentials:</h4>
                    <p><strong>Username:</strong> testuser</p>
                    <p><strong>Password:</strong> password</p>
                    <p><strong>Admin:</strong> btaylor-admin / admin123</p>
                </div>
            </div>
        </div>

        <!-- Dashboard Preview -->
        <div class="preview-section">
            <h2 class="section-title">
                <span class="material-icons">dashboard</span>
                Dashboard Component
            </h2>
            
            <div class="app-bar">
                <div class="app-bar-title">
                    <span class="material-icons">security</span>
                    Auth Service Dashboard
                </div>
                <div class="user-menu">
                    <span class="material-icons">account_circle</span>
                    <span>testuser</span>
                </div>
            </div>
            
            <div class="dashboard-grid">
                <!-- Service Health Card -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <span class="material-icons" style="color: #4caf50;">health_and_safety</span>
                        <h3>Service Health</h3>
                    </div>
                    <div class="status-chip status-success">healthy</div>
                    <ul class="info-list">
                        <li><strong>Service:</strong> auth-service</li>
                        <li><strong>Version:</strong> 1.0.0</li>
                        <li><strong>Last Check:</strong> 7/14/2025, 2:30:45 PM</li>
                    </ul>
                    <h4 style="margin-top: 16px; margin-bottom: 8px;">Available Endpoints:</h4>
                    <ul class="info-list">
                        <li>/oauth/token</li>
                        <li>/oauth/refresh</li>
                        <li>/oauth/validate</li>
                        <li>/oauth/revoke</li>
                    </ul>
                </div>
                
                <!-- Token Information Card -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <span class="material-icons" style="color: #1976d2;">token</span>
                        <h3>Token Information</h3>
                    </div>
                    <div class="status-chip status-info">Valid</div>
                    <ul class="info-list">
                        <li><strong>User:</strong> testuser</li>
                        <li><strong>User ID:</strong> 550e8400-e29b-41d4-a716-446655440000</li>
                        <li><strong>Expires:</strong> 7/14/2025, 3:30:45 PM</li>
                        <li><strong>Scopes:</strong> read, write</li>
                    </ul>
                    <h4 style="margin-top: 16px; margin-bottom: 8px;">Access Token:</h4>
                    <div class="token-preview">eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...kQtuOydnY809MIaO0dXbnaePjiPU</div>
                    <h4 style="margin-bottom: 8px;">Refresh Token:</h4>
                    <div class="token-preview">eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...o/ykKkR7CLfqWvfQAQ1RIo51T8aK</div>
                </div>
            </div>
            
            <!-- Token Actions -->
            <div class="dashboard-card" style="margin-top: 24px;">
                <h3 style="margin-bottom: 20px;">Token Actions</h3>
                <div class="action-buttons">
                    <button class="btn btn-primary">
                        <span class="material-icons">refresh</span>
                        Refresh Token
                    </button>
                    <button class="btn btn-outline">
                        <span class="material-icons">health_and_safety</span>
                        Check Health
                    </button>
                    <button class="btn btn-outline">
                        <span class="material-icons">token</span>
                        Validate Token
                    </button>
                    <button class="btn btn-error">
                        <span class="material-icons">logout</span>
                        Logout
                    </button>
                </div>
            </div>
        </div>

        <!-- Features Overview -->
        <div class="preview-section">
            <h2 class="section-title">
                <span class="material-icons">star</span>
                React UI Features
            </h2>
            <div class="feature-highlight">
                <h3 style="margin-bottom: 16px;">Modern React/TypeScript Implementation</h3>
                <div class="feature-list">
                    <div class="feature-item">
                        <span class="material-icons">check_circle</span>
                        <span>Material-UI Components</span>
                    </div>
                    <div class="feature-item">
                        <span class="material-icons">check_circle</span>
                        <span>TypeScript Type Safety</span>
                    </div>
                    <div class="feature-item">
                        <span class="material-icons">check_circle</span>
                        <span>React Router Navigation</span>
                    </div>
                    <div class="feature-item">
                        <span class="material-icons">check_circle</span>
                        <span>Form Validation (Yup)</span>
                    </div>
                    <div class="feature-item">
                        <span class="material-icons">check_circle</span>
                        <span>Axios API Integration</span>
                    </div>
                    <div class="feature-item">
                        <span class="material-icons">check_circle</span>
                        <span>JWT Token Management</span>
                    </div>
                    <div class="feature-item">
                        <span class="material-icons">check_circle</span>
                        <span>Responsive Design</span>
                    </div>
                    <div class="feature-item">
                        <span class="material-icons">check_circle</span>
                        <span>Loading States</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
