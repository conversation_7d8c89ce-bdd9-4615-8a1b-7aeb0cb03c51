<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth Service - Professional Authentication</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .auth-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            padding: 40px;
            position: relative;
            overflow: hidden;
        }
        .auth-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo h1 {
            color: #2d3748;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        .logo p {
            color: #718096;
            font-size: 14px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #4a5568;
            font-weight: 500;
            font-size: 14px;
        }
        .form-group input {
            width: 100%;
            padding: 14px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.2s ease;
            background: #f7fafc;
        }
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .btn {
            width: 100%;
            padding: 14px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 12px;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        .btn-secondary {
            background: #f7fafc;
            color: #4a5568;
            border: 2px solid #e2e8f0;
        }
        .btn-secondary:hover {
            background: #edf2f7;
            border-color: #cbd5e0;
        }
        .btn-admin {
            background: #f59e0b;
            color: white;
        }
        .btn-admin:hover {
            background: #d97706;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(245, 158, 11, 0.3);
        }
        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin: 16px 0;
            font-size: 14px;
            font-weight: 500;
        }
        .alert-success {
            background: #f0fff4;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }
        .alert-error {
            background: #fed7d7;
            color: #742a2a;
            border: 1px solid #feb2b2;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            color: #718096;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="logo">
            <h1>🔐 Auth Service</h1>
            <p>Professional OAuth 2.0 Authentication System</p>
        </div>
        
        <form onsubmit="handleLogin(event)">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" value="testuser" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" value="testpass123" required>
            </div>
            
            <button type="submit" class="btn btn-primary">🚀 Sign In</button>
            <button type="button" class="btn btn-secondary" onclick="showHealthCheck()">📊 Health Check</button>
            <button type="button" class="btn btn-admin" onclick="openAdminDashboard()">⚙️ Admin Dashboard</button>
        </form>
        
        <div id="login-message"></div>
        
        <div class="footer">
            <p>Powered by C++23 OAuth 2.0 Implementation</p>
        </div>
    </div>

    <script>
        async function handleLogin(event) {
            event.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            showMessage('Attempting authentication...', 'success');
            
            try {
                const response = await fetch('/oauth/token', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        username: username, 
                        password: password, 
                        grant_type: 'password' 
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    showMessage('Authentication successful! Token generated.', 'success');
                    console.log('OAuth Token:', data);
                } else {
                    const error = await response.json();
                    showMessage('Authentication failed: ' + (error.error_description || 'Invalid credentials'), 'error');
                }
            } catch (error) {
                showMessage('Connection error: Unable to reach authentication server', 'error');
                console.error('Auth error:', error);
            }
        }

        async function showHealthCheck() {
            try {
                const response = await fetch('/health');
                const data = await response.json();
                showMessage('Service Status: ' + data.status + ' (v' + data.version + ')', 'success');
                console.log('Health Check:', data);
            } catch (error) {
                showMessage('Health check failed: Service unavailable', 'error');
            }
        }

        function openAdminDashboard() {
            window.open('/admin.html', '_blank');
        }

        function showMessage(message, type) {
            const messageDiv = document.getElementById('login-message');
            messageDiv.innerHTML = '<div class="alert alert-' + type + '">' + message + '</div>';
            setTimeout(() => messageDiv.innerHTML = '', 5000);
        }
    </script>
</body>
</html>
