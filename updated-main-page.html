<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth Service - Professional Authentication</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0f1419 0%, #1a202c 50%, #2d3748 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .auth-container {
            background: rgba(26, 54, 93, 0.4);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
            width: 100%;
            max-width: 420px;
            padding: 50px;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .auth-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3182ce, #2c5282, #2a4365);
        }
        .logo {
            text-align: center;
            margin-bottom: 35px;
        }
        .logo h1 {
            color: #e2e8f0;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .logo p {
            color: #bee3f8;
            font-size: 15px;
            opacity: 0.9;
        }
        .form-group {
            margin-bottom: 24px;
        }
        .form-group label {
            display: block;
            margin-bottom: 10px;
            color: #e2e8f0;
            font-weight: 500;
            font-size: 14px;
        }
        .form-group input {
            width: 100%;
            padding: 16px 18px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            color: #e2e8f0;
            backdrop-filter: blur(10px);
        }
        .form-group input::placeholder {
            color: #a0aec0;
        }
        .form-group input:focus {
            outline: none;
            border-color: #63b3ed;
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 3px rgba(99, 179, 237, 0.2);
        }
        .btn {
            width: 100%;
            padding: 16px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 14px;
            position: relative;
            overflow: hidden;
        }
        .btn-primary {
            background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%);
            color: white;
            box-shadow: 0 6px 20px rgba(49, 130, 206, 0.4);
        }
        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(49, 130, 206, 0.6);
            background: linear-gradient(135deg, #2c5282 0%, #2a4365 100%);
        }
        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: #cbd5e0;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            color: #e2e8f0;
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(255, 255, 255, 0.1);
        }
        .btn-admin {
            background: rgba(255, 255, 255, 0.15);
            color: #bee3f8;
            border: 1px solid rgba(190, 227, 248, 0.3);
            backdrop-filter: blur(10px);
        }
        .btn-admin:hover {
            background: rgba(255, 255, 255, 0.25);
            color: #e2e8f0;
            border-color: rgba(226, 232, 240, 0.4);
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(190, 227, 248, 0.2);
        }
        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin: 16px 0;
            font-size: 14px;
            font-weight: 500;
        }
        .alert-success {
            background: rgba(72, 187, 120, 0.2);
            color: #9ae6b4;
            border: 1px solid rgba(72, 187, 120, 0.3);
            backdrop-filter: blur(10px);
        }
        .alert-error {
            background: rgba(229, 62, 62, 0.2);
            color: #feb2b2;
            border: 1px solid rgba(229, 62, 62, 0.3);
            backdrop-filter: blur(10px);
        }
        .footer {
            text-align: center;
            margin-top: 35px;
            padding-top: 25px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: #a0aec0;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="logo">
            <h1>🔐 Auth Service</h1>
            <p>Professional OAuth 2.0 Authentication System</p>
        </div>
        
        <form onsubmit="handleLogin(event)">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" value="testuser" required>
            </div>

            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" value="testpass123" required>
            </div>

            <button type="submit" class="btn btn-primary">🚀 Sign In</button>
        </form>
        
        <div id="login-message"></div>
        
        <div class="footer">
            <p>Powered by C++23 OAuth 2.0 Implementation</p>
        </div>
    </div>

    <script>
        async function handleLogin(event) {
            event.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            showMessage('Attempting authentication...', 'success');
            
            try {
                const response = await fetch('/oauth/token', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        username: username, 
                        password: password, 
                        grant_type: 'password' 
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log('OAuth Token:', data);

                    // Store token and user info
                    localStorage.setItem('auth_token', data.access_token);
                    localStorage.setItem('refresh_token', data.refresh_token || '');
                    localStorage.setItem('user_info', JSON.stringify({
                        username: username,
                        login_time: new Date().toISOString(),
                        token_expires: data.expires_in,
                        is_admin: data.is_admin || false,
                        permissions: data.permissions || []
                    }));

                    // Check if user is admin and redirect accordingly
                    if (data.is_admin) {
                        window.location.href = '/admin.html';
                    } else {
                        window.location.href = '/dashboard.html';
                    }
                } else {
                    const error = await response.json();
                    showMessage('Authentication failed: ' + (error.error_description || 'Invalid credentials'), 'error');
                }
            } catch (error) {
                showMessage('Connection error: Unable to reach authentication server', 'error');
                console.error('Auth error:', error);
            }
        }





        function showMessage(message, type) {
            const messageDiv = document.getElementById('login-message');
            messageDiv.innerHTML = '<div class="alert alert-' + type + '">' + message + '</div>';
            setTimeout(() => messageDiv.innerHTML = '', 5000);
        }
    </script>
</body>
</html>
